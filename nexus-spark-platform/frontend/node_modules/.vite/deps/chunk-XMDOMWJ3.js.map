{"version": 3, "sources": ["../../lodash/uniq.js", "../../lodash/_baseIsDate.js", "../../lodash/isDate.js", "../../@nivo/scales/src/timeHelpers.ts", "../../@nivo/scales/src/linearScale.ts", "../../@nivo/scales/src/pointScale.ts", "../../@nivo/scales/src/bandScale.ts", "../../@nivo/scales/src/timeScale.ts", "../../@nivo/scales/src/logScale.ts", "../../@nivo/scales/src/symlogScale.ts", "../../@nivo/scales/src/compute.ts", "../../@nivo/scales/src/ticks.ts", "../../@nivo/scales/src/types.ts", "../../@nivo/axes/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs", "../../@nivo/axes/src/compute.ts", "../../@nivo/axes/src/components/AxisTick.tsx", "../../@nivo/axes/src/defaults.ts", "../../@nivo/axes/src/components/Axis.tsx", "../../@nivo/axes/src/props.ts", "../../@nivo/axes/src/components/Axes.tsx", "../../@nivo/axes/src/components/GridLine.tsx", "../../@nivo/axes/src/components/GridLines.tsx", "../../@nivo/axes/src/components/Grid.tsx", "../../@nivo/axes/src/canvas.ts"], "sourcesContent": ["var baseUniq = require('./_baseUniq');\n\n/**\n * Creates a duplicate-free version of an array, using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons, in which only the first occurrence of each element\n * is kept. The order of result values is determined by the order they occur\n * in the array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.uniq([2, 1, 2]);\n * // => [2, 1]\n */\nfunction uniq(array) {\n  return (array && array.length) ? baseUniq(array) : [];\n}\n\nmodule.exports = uniq;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar dateTag = '[object Date]';\n\n/**\n * The base implementation of `_.isDate` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n */\nfunction baseIsDate(value) {\n  return isObjectLike(value) && baseGetTag(value) == dateTag;\n}\n\nmodule.exports = baseIsDate;\n", "var baseIsDate = require('./_baseIsDate'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsDate = nodeUtil && nodeUtil.isDate;\n\n/**\n * Checks if `value` is classified as a `Date` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a date object, else `false`.\n * @example\n *\n * _.isDate(new Date);\n * // => true\n *\n * _.isDate('Mon April 23 2012');\n * // => false\n */\nvar isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;\n\nmodule.exports = isDate;\n", "import { timeParse, utcParse } from 'd3-time-format'\n\nexport const timePrecisions = [\n    'millisecond',\n    'second',\n    'minute',\n    'hour',\n    'day',\n    'month',\n    'year',\n] as const\n\nexport type TIME_PRECISION = (typeof timePrecisions)[number]\n\nexport const precisionCutOffs: ((date: Date) => void)[] = [\n    date => date.setMilliseconds(0),\n    date => date.setSeconds(0),\n    date => date.setMinutes(0),\n    date => date.setHours(0),\n    date => date.setDate(1),\n    date => date.setMonth(0),\n]\n\nexport const precisionCutOffsByType: Record<TIME_PRECISION, ((date: Date) => void)[]> = {\n    millisecond: [],\n    second: precisionCutOffs.slice(0, 1),\n    minute: precisionCutOffs.slice(0, 2),\n    hour: precisionCutOffs.slice(0, 3),\n    day: precisionCutOffs.slice(0, 4),\n    month: precisionCutOffs.slice(0, 5),\n    year: precisionCutOffs.slice(0, 6),\n}\n\nexport const createPrecisionMethod = (precision: TIME_PRECISION) => (date: Date) => {\n    precisionCutOffsByType[precision].forEach(cutOff => {\n        cutOff(date)\n    })\n\n    return date\n}\n\nexport const createDateNormalizer = ({\n    format = 'native',\n    precision = 'millisecond',\n    useUTC = true,\n}: {\n    format?: 'native' | string\n    precision?: TIME_PRECISION\n    useUTC?: boolean\n}) => {\n    const precisionFn = createPrecisionMethod(precision)\n\n    return (value: Date | string | undefined) => {\n        if (value === undefined) {\n            return value\n        }\n\n        if (format === 'native' || value instanceof Date) {\n            return precisionFn(value as Date)\n        }\n\n        const parseTime = useUTC ? utcParse(format) : timeParse(format)\n        return precisionFn(parseTime(value as string) as Date)\n    }\n}\n", "import { NumberValue, scaleLinear, ScaleLinear as D3ScaleLinear } from 'd3-scale'\nimport { interpolateRound, interpolateNumber } from 'd3-interpolate'\nimport { ScaleLinearSpec, ScaleLinear, ComputedSerieAxis, ScaleAxis } from './types'\n\nexport const linearScaleDefaults: Required<ScaleLinearSpec> = {\n    type: 'linear',\n    min: 0,\n    max: 'auto',\n    stacked: false,\n    reverse: false,\n    clamp: false,\n    nice: true,\n    round: false,\n}\n\nexport const createLinearScale = <Output extends NumberValue>(\n    {\n        min = linearScaleDefaults.min,\n        max = linearScaleDefaults.max,\n        stacked = linearScaleDefaults.stacked,\n        reverse = linearScaleDefaults.reverse,\n        clamp = linearScaleDefaults.clamp,\n        nice = linearScaleDefaults.nice,\n        round = linearScaleDefaults.round,\n    }: ScaleLinearSpec,\n    data: ComputedSerieAxis<Output>,\n    size: number,\n    axis: ScaleAxis\n) => {\n    let minValue: NumberValue\n    if (min === 'auto') {\n        minValue = stacked === true ? (data.minStacked ?? 0) : data.min\n    } else {\n        minValue = min\n    }\n\n    let maxValue: NumberValue\n    if (max === 'auto') {\n        maxValue = stacked === true ? (data.maxStacked ?? 0) : data.max\n    } else {\n        maxValue = max\n    }\n\n    const scale = scaleLinear<number, Output>()\n        .range(axis === 'x' ? [0, size] : [size, 0])\n        .interpolate(round ? interpolateRound : interpolateNumber)\n        .domain(reverse ? [maxValue, minValue] : [minValue, maxValue])\n        .clamp(clamp)\n\n    if (nice === true) scale.nice()\n    else if (typeof nice === 'number') scale.nice(nice)\n\n    return castLinearScale(scale, stacked)\n}\n\nexport const castLinearScale = <Range, Output>(\n    scale: D3ScaleLinear<Range, Output>,\n    stacked = false\n) => {\n    const typedScale = scale as unknown as ScaleLinear<number>\n    typedScale.type = 'linear'\n    typedScale.stacked = stacked\n\n    return typedScale\n}\n", "import { scalePoint, ScalePoint as D3ScalePoint } from 'd3-scale'\nimport { ComputedSerieAxis, ScalePoint, ScalePointSpec, StringValue } from './types'\n\nexport const createPointScale = <Input extends StringValue>(\n    _spec: ScalePointSpec,\n    data: ComputedSerieAxis<Input>,\n    size: number\n) => {\n    const scale = scalePoint<Input>().range([0, size]).domain(data.all)\n\n    const typedScale = scale as ScalePoint<Input>\n    typedScale.type = 'point'\n\n    return typedScale\n}\n\nexport const castPointScale = <Input extends StringValue>(scale: D3ScalePoint<Input>) => {\n    const typedScale = scale as ScalePoint<Input>\n    typedScale.type = 'point'\n\n    return typedScale\n}\n", "import { scaleBand, ScaleBand as D3ScaleBand } from 'd3-scale'\nimport { ComputedSerieAxis, ScaleBand, ScaleBandSpec, StringValue, ScaleAxis } from './types'\n\nexport const bandScaleDefaults: Required<ScaleBandSpec> = {\n    type: 'band',\n    round: false,\n}\n\nexport const createBandScale = <Input extends StringValue>(\n    { round = bandScaleDefaults.round }: ScaleBandSpec,\n    data: ComputedSerieAxis<Input>,\n    size: number,\n    axis: ScaleAxis\n) => {\n    const scale = scaleBand<Input>()\n        .range(axis === 'x' ? [0, size] : [size, 0])\n        .domain(data.all)\n        .round(round)\n\n    return castBandScale<Input>(scale)\n}\n\nexport const castBandScale = <Input extends StringValue>(scale: D3ScaleBand<Input>) => {\n    const typedScale = scale as ScaleBand<Input>\n    typedScale.type = 'band'\n\n    return typedScale\n}\n", "import { NumberValue, scaleTime, scaleUtc } from 'd3-scale'\nimport { createDateNormalizer } from './timeHelpers'\nimport { ComputedSerieAxis, ScaleTime, ScaleTimeSpec } from './types'\n\nexport const timeScaleDefaults: Required<ScaleTimeSpec> = {\n    type: 'time',\n    format: 'native',\n    precision: 'millisecond',\n    min: 'auto',\n    max: 'auto',\n    useUTC: true,\n    nice: false,\n}\n\nexport const createTimeScale = <Input extends Date | NumberValue>(\n    {\n        format = timeScaleDefaults.format,\n        precision = timeScaleDefaults.precision,\n        min = timeScaleDefaults.min,\n        max = timeScaleDefaults.max,\n        useUTC = timeScaleDefaults.useUTC,\n        nice = timeScaleDefaults.nice,\n    }: ScaleTimeSpec,\n    data: ComputedSerieAxis<string | Date>,\n    size: number\n) => {\n    const normalize = createDateNormalizer({ format, precision, useUTC })\n\n    let minValue: Date | undefined\n    if (min === 'auto') {\n        minValue = normalize(data.min)\n    } else if (format !== 'native') {\n        minValue = normalize(min)\n    } else {\n        minValue = min as Date\n    }\n\n    let maxValue: Date | undefined\n    if (max === 'auto') {\n        maxValue = normalize(data.max)\n    } else if (format !== 'native') {\n        maxValue = normalize(max)\n    } else {\n        maxValue = max as Date\n    }\n\n    const scale = useUTC ? scaleUtc() : scaleTime()\n\n    scale.range([0, size])\n\n    if (minValue && maxValue) scale.domain([minValue, maxValue])\n\n    if (nice === true) scale.nice()\n    else if (typeof nice === 'object' || typeof nice === 'number') scale.nice(nice)\n\n    const typedScale = scale as unknown as ScaleTime<Input>\n\n    typedScale.type = 'time'\n    typedScale.useUTC = useUTC\n\n    return typedScale\n}\n", "import { scaleLog } from 'd3-scale'\nimport { ComputedSerieAxis, ScaleAxis, ScaleLog, ScaleLogSpec } from './types'\n\nexport const logScaleDefaults: Required<ScaleLogSpec> = {\n    type: 'log',\n    base: 10,\n    min: 'auto',\n    max: 'auto',\n    round: false,\n    reverse: false,\n    nice: true,\n}\n\nexport const createLogScale = (\n    {\n        base = logScaleDefaults.base,\n        min = logScaleDefaults.min,\n        max = logScaleDefaults.max,\n        round = logScaleDefaults.round,\n        reverse = logScaleDefaults.reverse,\n        nice = logScaleDefaults.nice,\n    }: ScaleLogSpec,\n    data: ComputedSerieAxis<number>,\n    size: number,\n    axis: ScaleAxis\n) => {\n    const hasZero = data.all.some(v => v === 0)\n    if (hasZero) {\n        throw new Error(`a log scale domain must not include or cross zero`)\n    }\n\n    let sign: number\n    let hasMixedSign = false\n    data.all\n        .filter(v => v != null)\n        .forEach(v => {\n            if (hasMixedSign) return\n            if (sign === undefined) {\n                sign = Math.sign(v)\n            } else if (Math.sign(v) !== sign) {\n                hasMixedSign = true\n            }\n        })\n\n    if (hasMixedSign) {\n        throw new Error(`a log scale domain must be strictly-positive or strictly-negative`)\n    }\n\n    let minValue: number\n    if (min === 'auto') {\n        minValue = data.min\n    } else {\n        minValue = min\n    }\n\n    let maxValue: number\n    if (max === 'auto') {\n        maxValue = data.max\n    } else {\n        maxValue = max\n    }\n\n    const scale = scaleLog<number, number>().base(base)\n\n    const range = axis === 'x' ? [0, size] : [size, 0]\n    if (round === true) scale.rangeRound(range)\n    else scale.range(range)\n\n    if (reverse === true) scale.domain([maxValue, minValue])\n    else scale.domain([minValue, maxValue])\n\n    if (nice === true) scale.nice()\n    // @ts-expect-error not sure why this is not working, it's available for symlog.\n    else if (typeof nice === 'number') scale.nice(nice)\n\n    const typedScale = scale as ScaleLog\n    typedScale.type = 'log'\n\n    return typedScale\n}\n", "import { scaleSymlog } from 'd3-scale'\nimport { ComputedSerieAxis, ScaleAxis, ScaleSymlog, ScaleSymlogSpec } from './types'\n\nexport const symlogScaleDefaults: Required<ScaleSymlogSpec> = {\n    type: 'symlog',\n    constant: 1,\n    min: 'auto',\n    max: 'auto',\n    round: false,\n    reverse: false,\n    nice: true,\n}\n\nexport const createSymlogScale = (\n    {\n        constant = symlogScaleDefaults.constant,\n        min = symlogScaleDefaults.min,\n        max = symlogScaleDefaults.max,\n        round = symlogScaleDefaults.round,\n        reverse = symlogScaleDefaults.reverse,\n        nice = symlogScaleDefaults.nice,\n    }: ScaleSymlogSpec,\n    data: ComputedSerieAxis<number>,\n    size: number,\n    axis: ScaleAxis\n) => {\n    let minValue: number\n    if (min === 'auto') {\n        minValue = data.min\n    } else {\n        minValue = min\n    }\n\n    let maxValue: number\n    if (max === 'auto') {\n        maxValue = data.max\n    } else {\n        maxValue = max\n    }\n\n    const scale = scaleSymlog<number, number>().constant(constant)\n\n    const range = axis === 'x' ? [0, size] : [size, 0]\n    if (round === true) scale.rangeRound(range)\n    else scale.range(range)\n\n    if (reverse === true) scale.domain([maxValue, minValue])\n    else scale.domain([minValue, maxValue])\n\n    if (nice === true) scale.nice()\n    else if (typeof nice === 'number') scale.nice(nice)\n\n    const typedScale = scale as ScaleSymlog\n    typedScale.type = 'symlog'\n\n    return typedScale\n}\n", "import uniq from 'lodash/uniq.js'\nimport uniqBy from 'lodash/uniqBy.js'\nimport sortBy from 'lodash/sortBy.js'\nimport last from 'lodash/last.js'\nimport isDate from 'lodash/isDate.js'\nimport { createDateNormalizer } from './timeHelpers'\nimport { ScaleAxis, ScaleSpec, ScaleValue, SerieAxis, ComputedSerieAxis } from './types'\nimport { createLinearScale } from './linearScale'\nimport { createPointScale } from './pointScale'\nimport { createBandScale } from './bandScale'\nimport { createTimeScale } from './timeScale'\nimport { createLogScale } from './logScale'\nimport { createSymlogScale } from './symlogScale'\n\ntype XY = ReturnType<typeof generateSeriesXY>\n\ntype StackedXY = {\n    [K in keyof XY]: XY[K] & {\n        maxStacked: number\n        minStacked: number\n    }\n}\n\ninterface SerieDatum {\n    x: number | string | Date | null\n    // only numbers can be stacked\n    xStacked?: number | null\n    y: number | string | Date | null\n    // only numbers can be stacked\n    yStacked?: number | null\n}\n\ntype Serie<S = never, D extends SerieDatum = SerieDatum> = S & {\n    data: readonly D[]\n}\n\ntype NestedSerie<S = never, D extends SerieDatum = SerieDatum> = S & {\n    data: {\n        data: D\n    }[]\n}\n\nexport type ComputedSerie<S = never, D extends SerieDatum = SerieDatum> = S & {\n    data: {\n        data: D\n        position: {\n            x: number | null\n            y: number | null\n        }\n    }[]\n}\n\ntype Compare = <T>(a: T, b: T) => boolean\n\nexport const getOtherAxis = (axis: ScaleAxis): ScaleAxis => (axis === 'x' ? 'y' : 'x')\n\nexport const compareValues = (a: string | number, b: string | number) => a === b\nexport const compareDateValues = (a: Date, b: Date) => a.getTime() === b.getTime()\n\nexport function computeScale<Input extends ScaleValue>(\n    spec: ScaleSpec,\n    data: ComputedSerieAxis<any>,\n    size: number,\n    axis: ScaleAxis\n) {\n    switch (spec.type) {\n        case 'linear':\n            return createLinearScale(spec, data, size, axis)\n        case 'point':\n            return createPointScale<Exclude<Input, null>>(spec, data, size)\n        case 'band':\n            return createBandScale<Exclude<Input, null>>(spec, data, size, axis)\n        case 'time':\n            return createTimeScale(spec, data, size)\n        case 'log':\n            return createLogScale(spec, data, size, axis)\n        case 'symlog':\n            return createSymlogScale(spec, data, size, axis)\n        default:\n            throw new Error('invalid scale spec')\n    }\n}\n\n/**\n * Convert serie data to have the original data stored in a nested prop.\n *\n * We do this in order to avoid conflicts between raw & computed properties.\n * <- { data: { x: 1, y: 3 }[] }\n * -> { data: { data: { x: 1, y: 3 } }[] }\n */\nconst nestSerieData = <S = never, D extends SerieDatum = SerieDatum>(\n    serie: Serie<S, D>\n): NestedSerie<S, D> => ({\n    ...serie,\n    data: serie.data.map(d => ({ data: { ...d } })),\n})\n\nconst getDatumAxisPosition = <D extends SerieDatum = SerieDatum>(\n    datum: { data: D },\n    axis: ScaleAxis,\n    scale: any\n): number | null => {\n    if ('stacked' in scale && scale.stacked) {\n        const stackedValue = datum.data[axis === 'x' ? 'xStacked' : 'yStacked']\n        if (stackedValue === null || stackedValue === undefined) {\n            return null\n        }\n\n        return scale(stackedValue)\n    }\n\n    return scale(datum.data[axis]) ?? null\n}\n\n/**\n * Compute x/y d3 scales from an array of data series, and scale specifications.\n *\n * We use generics as it's not uncommon to have extra properties such as an id\n * added to the series, or extra props on data, in such case, you should override\n * the default types.\n */\nexport const computeXYScalesForSeries = <S = never, D extends SerieDatum = SerieDatum>(\n    series: Serie<S, D>[],\n    xScaleSpec: ScaleSpec,\n    yScaleSpec: ScaleSpec,\n    width: number,\n    height: number\n) => {\n    // first nest series to avoid property conflicts\n    const nestedSeries = series.map(serie => nestSerieData<S, D>(serie))\n\n    // then compute data for each axis: all, min, max values\n    const xy = generateSeriesXY<S, D>(nestedSeries, xScaleSpec, yScaleSpec)\n\n    // stack x values depending on xScale\n    if ('stacked' in xScaleSpec && xScaleSpec.stacked === true) {\n        stackX<S, D>(xy as StackedXY, nestedSeries)\n    }\n\n    // stack y values depending on yScale\n    if ('stacked' in yScaleSpec && yScaleSpec.stacked === true) {\n        stackY<S, D>(xy as StackedXY, nestedSeries)\n    }\n\n    // computes scales\n    const xScale = computeScale<D['x']>(xScaleSpec, xy.x, width, 'x')\n    const yScale = computeScale<D['y']>(yScaleSpec, xy.y, height, 'y')\n\n    // assign position to each datum in every scale\n    const computedSeries: ComputedSerie<S, D>[] = nestedSeries.map(serie => ({\n        ...serie,\n        data: serie.data.map(datum => ({\n            ...datum,\n            position: {\n                x: getDatumAxisPosition(datum, 'x', xScale),\n                y: getDatumAxisPosition(datum, 'y', yScale),\n            },\n        })),\n    }))\n\n    return {\n        ...xy,\n        series: computedSeries,\n        xScale,\n        yScale,\n    }\n}\n\nexport const generateSeriesXY = <S = never, D extends SerieDatum = SerieDatum>(\n    series: NestedSerie<S, D>[],\n    xScaleSpec: ScaleSpec,\n    yScaleSpec: ScaleSpec\n) => ({\n    x: generateSeriesAxis<'x', D['x']>(series, 'x', xScaleSpec),\n    y: generateSeriesAxis<'y', D['y']>(series, 'y', yScaleSpec),\n})\n\n/**\n * Normalize data according to scale type, (time => Date, linear => Number)\n * compute sorted unique values and min/max.\n */\nexport const generateSeriesAxis = <Axis extends ScaleAxis, Value extends ScaleValue>(\n    series: SerieAxis<Axis, Value>,\n    axis: Axis,\n    scaleSpec: ScaleSpec,\n    {\n        getValue = d => d.data[axis],\n        setValue = (d, v) => {\n            d.data[axis] = v\n        },\n    }: {\n        getValue?: (d: { data: Record<Axis, Value | null> }) => Value | null\n        setValue?: (d: { data: Record<Axis, Value | null> }, v: Value) => void\n    } = {}\n) => {\n    if (scaleSpec.type === 'linear') {\n        series.forEach(serie => {\n            serie.data.forEach(d => {\n                const value = getValue(d)\n\n                if (value) {\n                    setValue(d, parseFloat(String(value)) as unknown as Value)\n                }\n            })\n        })\n    } else if (scaleSpec.type === 'time' && scaleSpec.format !== 'native') {\n        // `native` means we already have Date instances,\n        // otherwise we have to convert the values to Date.\n        const parseTime = createDateNormalizer(scaleSpec)\n\n        series.forEach(serie => {\n            serie.data.forEach(d => {\n                const value = getValue(d)\n\n                if (value) {\n                    setValue(d, parseTime(value as Date) as unknown as Value)\n                }\n            })\n        })\n    }\n\n    const values: unknown[] = []\n\n    series.forEach(serie => {\n        serie.data.forEach(d => {\n            values.push(getValue(d))\n        })\n    })\n\n    switch (scaleSpec.type) {\n        case 'linear': {\n            const all = sortBy(\n                // filer null values to deal with holes in linechart\n                uniq(values as number[]).filter(v => v !== null),\n                v => v\n            )\n\n            return { all, min: Math.min(...all), max: Math.max(...all) }\n        }\n        case 'time': {\n            const all = uniqBy(values as Date[], v => v.getTime())\n                .slice(0)\n                .sort((a, b) => b.getTime() - a.getTime())\n                .reverse()\n\n            return { all, min: all[0], max: last(all) }\n        }\n        default: {\n            const all = uniq(values)\n\n            return { all, min: all[0], max: last(all) }\n        }\n    }\n}\n\nexport const stackAxis = <S = never, D extends SerieDatum = SerieDatum>(\n    axis: ScaleAxis,\n    xy: StackedXY,\n    series: NestedSerie<S, D>[]\n) => {\n    const otherAxis = getOtherAxis(axis)\n    const all: number[] = []\n\n    xy[otherAxis].all.forEach(v => {\n        const compare = (isDate(v) ? compareDateValues : compareValues) as Compare\n        const stack: Array<number | null> = []\n\n        series.forEach(serie => {\n            const datum = serie.data.find(d => compare(d.data[otherAxis], v))\n            let value = null\n            let stackValue = null\n\n            if (datum !== undefined) {\n                // stacked values only support numbers\n                value = datum.data[axis] as number\n                if (value !== null) {\n                    const head = last(stack)\n                    if (head === undefined) {\n                        stackValue = value\n                    } else if (head !== null) {\n                        stackValue = head + value\n                    }\n                }\n\n                datum.data[axis === 'x' ? 'xStacked' : 'yStacked'] = stackValue\n            }\n\n            stack.push(stackValue)\n\n            if (stackValue !== null) {\n                all.push(stackValue)\n            }\n        })\n    })\n\n    xy[axis].minStacked = Math.min(...all)\n    xy[axis].maxStacked = Math.max(...all)\n}\n\nconst stackX = <S = never, D extends SerieDatum = SerieDatum>(\n    xy: StackedXY,\n    series: NestedSerie<S, D>[]\n) => stackAxis<S, D>('x', xy, series)\n\nconst stackY = <S = never, D extends SerieDatum = SerieDatum>(\n    xy: StackedXY,\n    series: NestedSerie<S, D>[]\n) => stackAxis<S, D>('y', xy, series)\n", "import {\n    CountableTimeInterval,\n    timeMillisecond,\n    utcMillisecond,\n    timeSecond,\n    utcSecond,\n    timeMinute,\n    utcMinute,\n    timeHour,\n    utcHour,\n    timeWeek,\n    utcWeek,\n    timeSunday,\n    utcSunday,\n    timeMonday,\n    utcMonday,\n    timeTuesday,\n    utcTuesday,\n    timeWednesday,\n    utcWednesday,\n    timeThursday,\n    utcThursday,\n    timeFriday,\n    utcFriday,\n    timeSaturday,\n    utcSaturday,\n    timeMonth,\n    utcMonth,\n    timeYear,\n    utcYear,\n    timeInterval,\n} from 'd3-time'\nimport { ScaleValue, TicksSpec, AnyScale, ScaleWithBandwidth } from './types'\n\n/**\n * Band and point scales are not centered, meaning the ticks would\n * be positioned at the beginning of each step; however, we want\n * them to be centered for each step.\n */\nexport const centerScale = <Value>(scale: ScaleWithBandwidth) => {\n    const bandwidth = scale.bandwidth()\n\n    if (bandwidth === 0) return scale\n\n    let offset = bandwidth / 2\n    if (scale.round()) {\n        offset = Math.round(offset)\n    }\n\n    return <T extends Value>(d: T) => (scale(d) ?? 0) + offset\n}\n\nconst timeDay = timeInterval(\n    date => date.setHours(0, 0, 0, 0),\n    (date, step) => date.setDate(date.getDate() + step),\n    (start, end) => (end.getTime() - start.getTime()) / 864e5,\n    date => Math.floor(date.getTime() / 864e5)\n)\n\nconst utcDay = timeInterval(\n    date => date.setUTCHours(0, 0, 0, 0),\n    (date, step) => date.setUTCDate(date.getUTCDate() + step),\n    (start, end) => (end.getTime() - start.getTime()) / 864e5,\n    date => Math.floor(date.getTime() / 864e5)\n)\n\nconst timeByType: Record<string, [CountableTimeInterval, CountableTimeInterval]> = {\n    millisecond: [timeMillisecond, utcMillisecond],\n    second: [timeSecond, utcSecond],\n    minute: [timeMinute, utcMinute],\n    hour: [timeHour, utcHour],\n    day: [timeDay, utcDay],\n    week: [timeWeek, utcWeek],\n    sunday: [timeSunday, utcSunday],\n    monday: [timeMonday, utcMonday],\n    tuesday: [timeTuesday, utcTuesday],\n    wednesday: [timeWednesday, utcWednesday],\n    thursday: [timeThursday, utcThursday],\n    friday: [timeFriday, utcFriday],\n    saturday: [timeSaturday, utcSaturday],\n    month: [timeMonth, utcMonth],\n    year: [timeYear, utcYear],\n}\n\nconst timeTypes = Object.keys(timeByType)\nconst timeIntervalRegexp = new RegExp(`^every\\\\s*(\\\\d+)?\\\\s*(${timeTypes.join('|')})s?$`, 'i')\n\nconst isInteger = (value: unknown): value is number =>\n    typeof value === 'number' && isFinite(value) && Math.floor(value) === value\n\nexport const getScaleTicks = <Value extends ScaleValue>(\n    scale: AnyScale,\n    spec?: TicksSpec<Value>\n) => {\n    // specific values\n    if (Array.isArray(spec)) {\n        return spec\n    }\n\n    if (typeof spec === 'string' && 'useUTC' in scale) {\n        // time interval\n        const matches = spec.match(timeIntervalRegexp)\n\n        if (matches) {\n            const [, amount, type] = matches\n            // UTC is used as it's more predictable,\n            // however, local time could be used too\n            // let's see how it fits users' requirements\n            const timeType = timeByType[type][scale.useUTC ? 1 : 0]\n\n            if (type === 'day') {\n                const [start, originalStop] = scale.domain()\n                const stop = new Date(originalStop)\n\n                // Set range to include last day in the domain since `interval.range` function is exclusive stop\n                stop.setDate(stop.getDate() + 1)\n\n                return timeType.every(Number(amount ?? 1))?.range(start, stop) ?? []\n            }\n\n            if (amount === undefined) {\n                return scale.ticks(timeType)\n            }\n\n            const interval = timeType.every(Number(amount))\n\n            if (interval) {\n                return scale.ticks(interval)\n            }\n        }\n\n        throw new Error(`Invalid tickValues: ${spec}`)\n    }\n\n    // continuous scales\n    if ('ticks' in scale) {\n        // default behaviour\n        if (spec === undefined) {\n            return scale.ticks()\n        }\n\n        // specific tick count\n        if (isInteger(spec)) {\n            return scale.ticks(spec)\n        }\n    }\n\n    // non linear scale default\n    return scale.domain()\n}\n", "import {\n    ScaleLinear as D3ScaleLinear,\n    ScalePoint as D3ScalePoint,\n    ScaleBand as D3ScaleBand,\n    ScaleLogarithmic as D3ScaleLogarithmic,\n    ScaleSymLog as D3ScaleSymLog,\n    ScaleTime as D3ScaleTime,\n} from 'd3-scale'\nimport { TIME_PRECISION } from './timeHelpers'\n\nexport type ScaleAxis = 'x' | 'y'\nexport type OtherScaleAxis<Axis extends ScaleAxis> = Axis extends 'x' ? 'y' : 'x'\n\nexport type NumericValue = { valueOf(): number }\nexport type StringValue = { toString(): string }\nexport type ScaleValue = NumericValue | StringValue | Date | null\n\nexport interface ScaleTypeToSpec {\n    linear: ScaleLinearSpec\n    log: ScaleLogSpec\n    symlog: ScaleSymlogSpec\n    point: ScalePointSpec\n    band: ScaleBandSpec\n    time: ScaleTimeSpec\n}\n\nexport type ScaleType = keyof ScaleTypeToSpec\nexport type ScaleSpec = ScaleTypeToSpec[keyof ScaleTypeToSpec]\n\nexport type ReversibleScaleSpec = ScaleLinearSpec | ScaleLogSpec | ScaleSymlogSpec\n\nexport const isReversibleScaleSpec = (scaleSpec: ScaleSpec): scaleSpec is ReversibleScaleSpec => {\n    return scaleSpec.type === 'linear' || scaleSpec.type === 'log' || scaleSpec.type === 'symlog'\n}\n\nexport interface ScaleTypeToScale<Input, Output> {\n    linear: Input extends NumericValue ? ScaleLinear<Output> : never\n    log: Input extends NumericValue ? ScaleLog : never\n    symlog: Input extends NumericValue ? ScaleSymlog : never\n    point: Input extends StringValue ? ScalePoint<Input> : never\n    band: Input extends StringValue ? ScaleBand<Input> : never\n    time: Input extends StringValue | Date ? ScaleTime<Input> : never\n}\n\nexport type Scale<Input, Output> = ScaleTypeToScale<Input, Output>[keyof ScaleTypeToScale<\n    Input,\n    Output\n>]\n\nexport type ScaleLinearSpec = {\n    type: 'linear'\n    min?: 'auto' | number\n    max?: 'auto' | number\n    stacked?: boolean\n    reverse?: boolean\n    clamp?: boolean\n    nice?: boolean | number\n    round?: boolean\n}\nexport interface ScaleLinear<Output> extends D3ScaleLinear<number, Output, never> {\n    type: 'linear'\n    stacked: boolean\n}\n\nexport interface ScaleLogSpec {\n    type: 'log'\n    base?: number\n    min?: 'auto' | number\n    max?: 'auto' | number\n    round?: boolean\n    reverse?: boolean\n    nice?: boolean | number\n}\nexport interface ScaleLog extends D3ScaleLogarithmic<number, number> {\n    type: 'log'\n}\n\nexport interface ScaleSymlogSpec {\n    type: 'symlog'\n    constant?: number\n    min?: 'auto' | number\n    max?: 'auto' | number\n    round?: boolean\n    reverse?: boolean\n    nice?: boolean | number\n}\nexport interface ScaleSymlog extends D3ScaleSymLog<number, number> {\n    type: 'symlog'\n}\n\nexport type ScalePointSpec = {\n    type: 'point'\n}\nexport interface ScalePoint<Input extends StringValue> extends D3ScalePoint<Input> {\n    type: 'point'\n}\n\nexport type ScaleBandSpec = {\n    type: 'band'\n    round?: boolean\n}\nexport interface ScaleBand<Input extends StringValue> extends D3ScaleBand<Input> {\n    type: 'band'\n}\n\nexport type ScaleTimeSpec = {\n    type: 'time'\n    format?: 'native' | string\n    precision?: TIME_PRECISION\n    min?: 'auto' | Date | string\n    max?: 'auto' | Date | string\n    useUTC?: boolean\n    nice?: boolean\n}\n\nexport interface ScaleTime<Input> extends D3ScaleTime<Input, number> {\n    type: 'time'\n    useUTC: boolean\n}\n\nexport type AnyScale = Scale<any, any>\n\nexport type ScaleWithBandwidth = ScaleBand<any> | ScalePoint<any>\n\nexport type Series<XValue extends ScaleValue, YValue extends ScaleValue> = {\n    data: {\n        data: {\n            x: XValue | null\n            y: YValue | null\n        }\n    }[]\n}[]\n\n// A serie containing data for a specific axis\nexport type SerieAxis<Axis extends ScaleAxis, Value extends ScaleValue> = {\n    data: {\n        data: Record<Axis, Value | null>\n    }[]\n}[]\n\nexport type ComputedSerieAxis<Value extends ScaleValue> = {\n    all: readonly Value[]\n    min: Value\n    minStacked?: Value\n    max: Value\n    maxStacked?: Value\n}\n\nexport type TicksSpec<Value extends ScaleValue> =\n    // exact number of ticks, please note that\n    // depending on the current range of values,\n    // you might not get this exact count\n    | number\n    // string is used for Date based scales,\n    // it can express a time interval,\n    // for example: every 2 weeks\n    | string\n    // override scale ticks with custom explicit values\n    | readonly Value[]\n", "// src/index.ts\nimport { Globals } from \"@react-spring/core\";\nimport { unstable_batchedUpdates } from \"react-dom\";\nimport { createStringInterpolator, colors } from \"@react-spring/shared\";\nimport { createHost } from \"@react-spring/animated\";\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\") return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\nimport { AnimatedObject } from \"@react-spring/animated\";\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver\n} from \"@react-spring/shared\";\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    eachProp(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (is.und(value)) return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push(toArray(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0]);\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    callFluidObservers(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\nexport * from \"@react-spring/core\";\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors\n});\nvar host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\nexport {\n  animated as a,\n  animated\n};\n", "import { timeFormat } from 'd3-time-format'\nimport { format as d3Format } from 'd3-format'\n// @ts-expect-error no types\nimport { textPropsByEngine } from '@nivo/core'\nimport { ScaleValue, AnyScale, TicksSpec, getScaleTicks, centerScale } from '@nivo/scales'\nimport { Point, ValueFormatter, Line } from './types'\n\nconst isArray = <T>(value: unknown): value is T[] => Array.isArray(value)\n\nexport const computeCartesianTicks = <Value extends ScaleValue>({\n    axis,\n    scale,\n    ticksPosition,\n    tickValues,\n    tickSize,\n    tickPadding,\n    tickRotation,\n    truncateTickAt,\n    engine = 'svg',\n}: {\n    axis: 'x' | 'y'\n    scale: AnyScale\n    ticksPosition?: 'after' | 'before'\n    tickValues?: TicksSpec<Value>\n    tickSize: number\n    tickPadding: number\n    tickRotation: number\n    truncateTickAt?: number\n    engine?: 'svg' | 'canvas'\n}) => {\n    const values = getScaleTicks<Value>(scale, tickValues)\n\n    const textProps = textPropsByEngine[engine]\n\n    const position = 'bandwidth' in scale ? centerScale(scale) : scale\n    const line = { lineX: 0, lineY: 0 }\n    const text = { textX: 0, textY: 0 }\n\n    const isRTL = typeof document === 'object' ? document.dir === 'rtl' : false\n    let translate: (value: Value) => Point\n    let textAlign: CanvasTextAlign = textProps.align.center\n    let textBaseline: CanvasTextBaseline = textProps.baseline.center\n\n    if (axis === 'x') {\n        translate = d => ({ x: position(d) ?? 0, y: 0 })\n\n        line.lineY = tickSize * (ticksPosition === 'after' ? 1 : -1)\n        text.textY = (tickSize + tickPadding) * (ticksPosition === 'after' ? 1 : -1)\n\n        if (ticksPosition === 'after') {\n            textBaseline = textProps.baseline.top\n        } else {\n            textBaseline = textProps.baseline.bottom\n        }\n\n        if (tickRotation === 0) {\n            textAlign = textProps.align.center\n        } else if (\n            (ticksPosition === 'after' && tickRotation < 0) ||\n            (ticksPosition === 'before' && tickRotation > 0)\n        ) {\n            textAlign = textProps.align[isRTL ? 'left' : 'right']\n            textBaseline = textProps.baseline.center\n        } else if (\n            (ticksPosition === 'after' && tickRotation > 0) ||\n            (ticksPosition === 'before' && tickRotation < 0)\n        ) {\n            textAlign = textProps.align[isRTL ? 'right' : 'left']\n            textBaseline = textProps.baseline.center\n        }\n    } else {\n        translate = d => ({ x: 0, y: position(d) ?? 0 })\n\n        line.lineX = tickSize * (ticksPosition === 'after' ? 1 : -1)\n        text.textX = (tickSize + tickPadding) * (ticksPosition === 'after' ? 1 : -1)\n\n        if (ticksPosition === 'after') {\n            textAlign = textProps.align.left\n        } else {\n            textAlign = textProps.align.right\n        }\n    }\n\n    const truncateTick = (value: string) => {\n        const valueLength = String(value).length\n\n        if (truncateTickAt && truncateTickAt > 0 && valueLength > truncateTickAt) {\n            return `${String(value).slice(0, truncateTickAt).concat('...')}`\n        }\n        return `${value}`\n    }\n\n    const ticks = values.map((value: Value) => {\n        const processedValue =\n            typeof value === 'string' ? (truncateTick(value) as unknown as Value) : value\n        return {\n            key: value instanceof Date ? `${value.valueOf()}` : `${value}`,\n            value: processedValue,\n            ...translate(value),\n            ...line,\n            ...text,\n        }\n    })\n\n    return {\n        ticks,\n        textAlign,\n        textBaseline,\n    }\n}\n\nexport const getFormatter = <Value extends ScaleValue>(\n    format: string | ValueFormatter<Value> | undefined,\n    scale: AnyScale\n): ValueFormatter<Value> | undefined => {\n    if (typeof format === 'undefined' || typeof format === 'function') return format\n\n    if (scale.type === 'time') {\n        const formatter = timeFormat(format)\n\n        return ((d: any) => formatter(d instanceof Date ? d : new Date(d))) as ValueFormatter<Value>\n    }\n\n    return d3Format(format) as unknown as ValueFormatter<Value>\n}\n\nexport const computeGridLines = <Value extends ScaleValue>({\n    width,\n    height,\n    scale,\n    axis,\n    values: _values,\n}: {\n    width: number\n    height: number\n    scale: AnyScale\n    axis: 'x' | 'y'\n    values?: TicksSpec<Value>\n}) => {\n    const lineValues = isArray<number>(_values) ? _values : undefined\n    const values = lineValues || getScaleTicks<Value>(scale, _values)\n    const position = 'bandwidth' in scale ? centerScale(scale) : scale\n\n    const lines: Line[] =\n        axis === 'x'\n            ? values.map((value: Value) => ({\n                  key: value instanceof Date ? `${value.valueOf()}` : `${value}`,\n                  x1: position(value) ?? 0,\n                  x2: position(value) ?? 0,\n                  y1: 0,\n                  y2: height,\n              }))\n            : values.map((value: Value) => ({\n                  key: value instanceof Date ? `${value.valueOf()}` : `${value}`,\n                  x1: 0,\n                  x2: width,\n                  y1: position(value) ?? 0,\n                  y2: position(value) ?? 0,\n              }))\n\n    return lines\n}\n", "import { useMemo, memo } from 'react'\nimport * as React from 'react'\nimport { animated } from '@react-spring/web'\nimport { Text } from '@nivo/text'\nimport { ScaleValue } from '@nivo/scales'\nimport { AxisTickProps } from '../types'\n\nconst AxisTick = <Value extends ScaleValue>({\n    value: _value,\n    format,\n    lineX,\n    lineY,\n    onClick,\n    textBaseline,\n    textAnchor,\n    theme,\n    animatedProps,\n}: AxisTickProps<Value>) => {\n    const value = format?.(_value) ?? _value\n\n    const props = useMemo(() => {\n        const style = { opacity: animatedProps.opacity }\n\n        if (!onClick) {\n            return { style }\n        }\n\n        return {\n            style: { ...style, cursor: 'pointer' },\n            onClick: (event: React.MouseEvent<SVGGElement, MouseEvent>) => onClick(event, value),\n        }\n    }, [animatedProps.opacity, onClick, value])\n\n    return (\n        <animated.g transform={animatedProps.transform} {...props}>\n            <line x1={0} x2={lineX} y1={0} y2={lineY} style={theme.line} />\n            <Text\n                dominantBaseline={textBaseline}\n                textAnchor={textAnchor}\n                transform={animatedProps.textTransform}\n                style={theme.text}\n            >\n                {`${value}`}\n            </Text>\n        </animated.g>\n    )\n}\n\nconst memoizedAxisTick = memo(AxisTick) as typeof AxisTick\n\nexport { memoizedAxisTick as AxisTick }\n", "import { AxisProps } from './types'\n\nexport const defaultAxisProps: Pick<\n    Required<AxisProps>,\n    'tickSize' | 'tickPadding' | 'tickRotation' | 'legendPosition' | 'legendOffset'\n> = {\n    tickSize: 5,\n    tickPadding: 5,\n    tickRotation: 0,\n    legendPosition: 'middle',\n    legendOffset: 0,\n}\n", "import { useMotionConfig } from '@nivo/core'\nimport { useTheme, useExtendedAxisTheme } from '@nivo/theming'\nimport { Text } from '@nivo/text'\nimport { AnyScale, ScaleValue } from '@nivo/scales'\nimport { animated, useSpring, useTransition } from '@react-spring/web'\nimport * as React from 'react'\nimport { memo, useCallback, useMemo } from 'react'\nimport { computeCartesianTicks, getFormatter } from '../compute'\nimport { AxisProps } from '../types'\nimport { AxisTick } from './AxisTick'\nimport { defaultAxisProps } from '../defaults'\n\nexport const NonMemoizedAxis = <Value extends ScaleValue>({\n    axis,\n    scale,\n    x = 0,\n    y = 0,\n    length,\n    ticksPosition,\n    tickValues,\n    tickSize = defaultAxisProps.tickSize,\n    tickPadding = defaultAxisProps.tickPadding,\n    tickRotation = defaultAxisProps.tickRotation,\n    format,\n    renderTick = AxisTick,\n    truncateTickAt,\n    legend,\n    legendPosition = defaultAxisProps.legendPosition,\n    legendOffset = defaultAxisProps.legendOffset,\n    style,\n    onClick,\n    ariaHidden,\n}: AxisProps<Value> & {\n    axis: 'x' | 'y'\n    scale: AnyScale\n    x?: number\n    y?: number\n    length: number\n    onClick?: (event: React.MouseEvent<SVGGElement, MouseEvent>, value: Value | string) => void\n}) => {\n    const theme = useTheme()\n    const axisTheme = useExtendedAxisTheme(theme.axis, style)\n\n    const formatValue = useMemo(() => getFormatter(format, scale), [format, scale])\n\n    const { ticks, textAlign, textBaseline } = computeCartesianTicks({\n        axis,\n        scale,\n        ticksPosition,\n        tickValues,\n        tickSize,\n        tickPadding,\n        tickRotation,\n        truncateTickAt,\n    })\n\n    let legendNode = null\n    if (legend !== undefined) {\n        let legendX = 0\n        let legendY = 0\n        let legendRotation = 0\n        let textAnchor\n\n        if (axis === 'y') {\n            legendRotation = -90\n            legendX = legendOffset\n            if (legendPosition === 'start') {\n                textAnchor = 'start'\n                legendY = length\n            } else if (legendPosition === 'middle') {\n                textAnchor = 'middle'\n                legendY = length / 2\n            } else if (legendPosition === 'end') {\n                textAnchor = 'end'\n            }\n        } else {\n            legendY = legendOffset\n            if (legendPosition === 'start') {\n                textAnchor = 'start'\n            } else if (legendPosition === 'middle') {\n                textAnchor = 'middle'\n                legendX = length / 2\n            } else if (legendPosition === 'end') {\n                textAnchor = 'end'\n                legendX = length\n            }\n        }\n\n        legendNode = (\n            <>\n                <Text\n                    transform={`translate(${legendX}, ${legendY}) rotate(${legendRotation})`}\n                    textAnchor={textAnchor}\n                    style={{\n                        ...axisTheme.legend.text,\n                        dominantBaseline: 'central',\n                    }}\n                >\n                    {legend}\n                </Text>\n            </>\n        )\n    }\n\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        transform: `translate(${x},${y})`,\n        lineX2: axis === 'x' ? length : 0,\n        lineY2: axis === 'x' ? 0 : length,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    const getAnimatedProps = useCallback(\n        (tick: (typeof ticks)[0]) => {\n            return {\n                opacity: 1,\n                transform: `translate(${tick.x},${tick.y})`,\n                textTransform: `translate(${tick.textX},${tick.textY}) rotate(${tickRotation})`,\n            }\n        },\n        [tickRotation]\n    )\n    const getFromAnimatedProps = useCallback(\n        (tick: (typeof ticks)[0]) => ({\n            opacity: 0,\n            transform: `translate(${tick.x},${tick.y})`,\n            textTransform: `translate(${tick.textX},${tick.textY}) rotate(${tickRotation})`,\n        }),\n        [tickRotation]\n    )\n\n    const transition = useTransition<\n        (typeof ticks)[0],\n        { opacity: number; transform: string; textTransform: string }\n    >(ticks, {\n        keys: tick => tick.key,\n        initial: getAnimatedProps,\n        from: getFromAnimatedProps,\n        enter: getAnimatedProps,\n        update: getAnimatedProps,\n        leave: {\n            opacity: 0,\n        },\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <animated.g transform={animatedProps.transform} aria-hidden={ariaHidden}>\n            {transition((transitionProps, tick, _state, tickIndex) => {\n                return React.createElement(renderTick, {\n                    tickIndex,\n                    format: formatValue,\n                    rotate: tickRotation,\n                    textBaseline,\n                    textAnchor: textAlign,\n                    truncateTickAt: truncateTickAt,\n                    animatedProps: transitionProps,\n                    theme: axisTheme.ticks,\n                    ...tick,\n                    ...(onClick ? { onClick } : {}),\n                })\n            })}\n            <animated.line\n                style={axisTheme.domain.line}\n                x1={0}\n                x2={animatedProps.lineX2}\n                y1={0}\n                y2={animatedProps.lineY2}\n            />\n            {legendNode}\n        </animated.g>\n    )\n}\n\nexport const Axis = memo(NonMemoizedAxis) as typeof NonMemoizedAxis\n", "export const positions = ['top', 'right', 'bottom', 'left'] as const\n", "import { memo } from 'react'\nimport { ScaleValue, AnyScale } from '@nivo/scales'\nimport { Axis } from './Axis'\nimport { positions } from '../props'\nimport { AxisProps } from '../types'\n\nexport const Axes = memo(\n    <X extends ScaleValue, Y extends ScaleValue>({\n        xScale,\n        yScale,\n        width,\n        height,\n        top,\n        right,\n        bottom,\n        left,\n    }: {\n        xScale: AnyScale\n        yScale: AnyScale\n        width: number\n        height: number\n        top?: AxisProps<X> | null\n        right?: AxisProps<Y> | null\n        bottom?: AxisProps<X> | null\n        left?: AxisProps<Y> | null\n    }) => {\n        const axes = { top, right, bottom, left }\n\n        return (\n            <>\n                {positions.map(position => {\n                    const axis = axes[position] as typeof position extends 'bottom' | 'top'\n                        ? AxisProps<X> | undefined\n                        : AxisProps<Y> | undefined\n\n                    if (!axis) return null\n\n                    const isXAxis = position === 'top' || position === 'bottom'\n                    const ticksPosition =\n                        position === 'top' || position === 'left' ? 'before' : 'after'\n\n                    return (\n                        <Axis\n                            key={position}\n                            {...axis}\n                            axis={isXAxis ? 'x' : 'y'}\n                            x={position === 'right' ? width : 0}\n                            y={position === 'bottom' ? height : 0}\n                            scale={isXAxis ? xScale : yScale}\n                            length={isXAxis ? width : height}\n                            ticksPosition={ticksPosition}\n                            truncateTickAt={axis.truncateTickAt}\n                        />\n                    )\n                })}\n            </>\n        )\n    }\n)\n", "import { memo, SVGAttributes } from 'react'\nimport { SpringValues, animated } from '@react-spring/web'\nimport { useTheme } from '@nivo/theming'\n\nexport const GridLine = memo(\n    ({\n        animatedProps,\n    }: {\n        animatedProps: SpringValues<{\n            opacity: number\n            x1: number\n            x2: number\n            y1: number\n            y2: number\n        }>\n    }) => {\n        const theme = useTheme()\n\n        return (\n            <animated.line\n                {...animatedProps}\n                {...(theme.grid.line as SVGAttributes<SVGLineElement>)}\n            />\n        )\n    }\n)\n", "import { memo } from 'react'\nimport { useTransition } from '@react-spring/web'\nimport { useMotionConfig } from '@nivo/core'\nimport { GridLine } from './GridLine'\nimport { Line } from '../types'\n\nexport const GridLines = memo(({ lines }: { lines: Line[] }) => {\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const transition = useTransition<Line, Record<'opacity' | 'x1' | 'x2' | 'y1' | 'y2', number>>(\n        lines,\n        {\n            keys: line => line.key,\n            initial: line => ({\n                opacity: 1,\n                x1: line.x1,\n                x2: line.x2,\n                y1: line.y1,\n                y2: line.y2,\n            }),\n            from: line => ({\n                opacity: 0,\n                x1: line.x1,\n                x2: line.x2,\n                y1: line.y1,\n                y2: line.y2,\n            }),\n            enter: line => ({\n                opacity: 1,\n                x1: line.x1,\n                x2: line.x2,\n                y1: line.y1,\n                y2: line.y2,\n            }),\n            update: line => ({\n                opacity: 1,\n                x1: line.x1,\n                x2: line.x2,\n                y1: line.y1,\n                y2: line.y2,\n            }),\n            leave: {\n                opacity: 0,\n            },\n            config: springConfig,\n            immediate: !animate,\n        }\n    )\n\n    return (\n        <g>\n            {transition((animatedProps, line) => (\n                <GridLine {...line} key={line.key} animatedProps={animatedProps} />\n            ))}\n        </g>\n    )\n})\n", "import { useMemo, memo } from 'react'\nimport { ScaleValue, AnyScale, TicksSpec } from '@nivo/scales'\nimport { GridLines } from './GridLines'\nimport { computeGridLines } from '../compute'\n\nexport const Grid = memo(\n    <X extends ScaleValue, Y extends ScaleValue>({\n        width,\n        height,\n        xScale,\n        yScale,\n        xValues,\n        yValues,\n    }: {\n        width: number\n        height: number\n        xScale?: AnyScale | null\n        xValues?: TicksSpec<X>\n        yScale?: AnyScale | null\n        yValues?: TicksSpec<Y>\n    }) => {\n        const xLines = useMemo(() => {\n            if (!xScale) return false\n\n            return computeGridLines({\n                width,\n                height,\n                scale: xScale,\n                axis: 'x',\n                values: xValues,\n            })\n        }, [xScale, xValues, width, height])\n\n        const yLines = useMemo(() => {\n            if (!yScale) return false\n\n            return computeGridLines({\n                width,\n                height,\n                scale: yScale,\n                axis: 'y',\n                values: yValues,\n            })\n        }, [height, width, yScale, yValues])\n\n        return (\n            <>\n                {xLines && <GridLines lines={xLines} />}\n                {yLines && <GridLines lines={yLines} />}\n            </>\n        )\n    }\n)\n", "import { degreesToRadians } from '@nivo/core'\nimport { Theme, PartialTheme, extendAxisTheme } from '@nivo/theming'\nimport { setCanvasFont, drawCanvasText } from '@nivo/text'\nimport { ScaleValue, AnyScale, TicksSpec } from '@nivo/scales'\nimport { defaultAxisProps } from './defaults'\nimport { computeCartesianTicks, getFormatter, computeGridLines } from './compute'\nimport { positions } from './props'\nimport { AxisLegendPosition, CanvasAxisProps, ValueFormatter } from './types'\n\nexport const renderAxisToCanvas = <Value extends ScaleValue>(\n    ctx: CanvasRenderingContext2D,\n    {\n        axis,\n        scale,\n        x = 0,\n        y = 0,\n        length,\n        ticksPosition,\n        tickValues,\n        tickSize = defaultAxisProps.tickSize,\n        tickPadding = defaultAxisProps.tickPadding,\n        tickRotation = defaultAxisProps.tickRotation,\n        format: _format,\n        legend,\n        legendPosition = defaultAxisProps.legendPosition,\n        legendOffset = defaultAxisProps.legendOffset,\n        theme,\n        style,\n    }: {\n        axis: 'x' | 'y'\n        scale: AnyScale\n        x?: number\n        y?: number\n        length: number\n        ticksPosition: 'before' | 'after'\n        tickValues?: TicksSpec<Value>\n        tickSize?: number\n        tickPadding?: number\n        tickRotation?: number\n        format?: string | ValueFormatter<Value>\n        legend?: string\n        legendPosition?: AxisLegendPosition\n        legendOffset?: number\n        theme: Theme\n        style?: PartialTheme['axis']\n    }\n) => {\n    const { ticks, textAlign, textBaseline } = computeCartesianTicks({\n        axis,\n        scale,\n        ticksPosition,\n        tickValues,\n        tickSize,\n        tickPadding,\n        tickRotation,\n        engine: 'canvas',\n    })\n\n    ctx.save()\n    ctx.translate(x, y)\n\n    const axisTheme = extendAxisTheme(theme.axis, style)\n\n    ctx.textAlign = textAlign\n    ctx.textBaseline = textBaseline\n\n    setCanvasFont(ctx, axisTheme.ticks.text)\n\n    const domainLineWidth = axisTheme.domain.line.strokeWidth ?? 0\n    if (typeof domainLineWidth !== 'string' && domainLineWidth > 0) {\n        ctx.lineWidth = domainLineWidth\n        ctx.lineCap = 'square'\n\n        if (axisTheme.domain.line.stroke) {\n            ctx.strokeStyle = axisTheme.domain.line.stroke\n        }\n\n        ctx.beginPath()\n        ctx.moveTo(0, 0)\n        ctx.lineTo(axis === 'x' ? length : 0, axis === 'x' ? 0 : length)\n        ctx.stroke()\n    }\n\n    const format = typeof _format === 'function' ? _format : (value: unknown) => `${value}`\n\n    const tickLineWidth = axisTheme.ticks.line.strokeWidth ?? 0\n    const shouldRenderTickLine = typeof tickLineWidth !== 'string' && tickLineWidth > 0\n    ticks.forEach(tick => {\n        if (shouldRenderTickLine) {\n            ctx.lineWidth = tickLineWidth\n            ctx.lineCap = 'square'\n\n            if (axisTheme.ticks.line.stroke) {\n                ctx.strokeStyle = axisTheme.ticks.line.stroke\n            }\n\n            ctx.beginPath()\n            ctx.moveTo(tick.x, tick.y)\n            ctx.lineTo(tick.x + tick.lineX, tick.y + tick.lineY)\n            ctx.stroke()\n        }\n\n        const value = format(tick.value)\n\n        ctx.save()\n        ctx.translate(tick.x + tick.textX, tick.y + tick.textY)\n        ctx.rotate(degreesToRadians(tickRotation))\n\n        drawCanvasText(ctx, axisTheme.ticks.text, `${value}`)\n\n        ctx.fillText(`${value}`, 0, 0)\n        ctx.restore()\n    })\n\n    if (legend !== undefined) {\n        let legendX = 0\n        let legendY = 0\n        let legendRotation = 0\n        let textAlign: CanvasTextAlign = 'center'\n\n        if (axis === 'y') {\n            legendRotation = -90\n            legendX = legendOffset\n            if (legendPosition === 'start') {\n                textAlign = 'start'\n                legendY = length\n            } else if (legendPosition === 'middle') {\n                textAlign = 'center'\n                legendY = length / 2\n            } else if (legendPosition === 'end') {\n                textAlign = 'end'\n            }\n        } else {\n            legendY = legendOffset\n            if (legendPosition === 'start') {\n                textAlign = 'start'\n            } else if (legendPosition === 'middle') {\n                textAlign = 'center'\n                legendX = length / 2\n            } else if (legendPosition === 'end') {\n                textAlign = 'end'\n                legendX = length\n            }\n        }\n\n        ctx.translate(legendX, legendY)\n        ctx.rotate(degreesToRadians(legendRotation))\n        setCanvasFont(ctx, axisTheme.legend.text)\n\n        if (axisTheme.legend.text.fill) {\n            ctx.fillStyle = axisTheme.legend.text.fill\n        }\n\n        ctx.textAlign = textAlign\n        ctx.textBaseline = 'middle'\n        drawCanvasText(ctx, axisTheme.legend.text, legend)\n    }\n\n    ctx.restore()\n}\n\nexport const renderAxesToCanvas = <X extends ScaleValue, Y extends ScaleValue>(\n    ctx: CanvasRenderingContext2D,\n    {\n        xScale,\n        yScale,\n        width,\n        height,\n        top,\n        right,\n        bottom,\n        left,\n        theme,\n    }: {\n        xScale: AnyScale\n        yScale: AnyScale\n        width: number\n        height: number\n        top?: CanvasAxisProps<X> | null\n        right?: CanvasAxisProps<Y> | null\n        bottom?: CanvasAxisProps<X> | null\n        left?: CanvasAxisProps<Y> | null\n        theme: Theme\n    }\n) => {\n    const axes = { top, right, bottom, left }\n\n    positions.forEach(position => {\n        const axis = axes[position] as typeof position extends 'bottom' | 'top'\n            ? CanvasAxisProps<X> | undefined\n            : CanvasAxisProps<Y> | undefined\n\n        if (!axis) return null\n\n        const isXAxis = position === 'top' || position === 'bottom'\n        const ticksPosition = position === 'top' || position === 'left' ? 'before' : 'after'\n        const scale = isXAxis ? xScale : yScale\n        const format = getFormatter(axis.format, scale)\n\n        renderAxisToCanvas(ctx, {\n            ...axis,\n            axis: isXAxis ? 'x' : 'y',\n            x: position === 'right' ? width : 0,\n            y: position === 'bottom' ? height : 0,\n            scale,\n            format,\n            length: isXAxis ? width : height,\n            ticksPosition,\n            theme,\n        })\n    })\n}\n\nexport const renderGridLinesToCanvas = <Value extends ScaleValue>(\n    ctx: CanvasRenderingContext2D,\n    {\n        width,\n        height,\n        scale,\n        axis,\n        values,\n    }: {\n        width: number\n        height: number\n        scale: AnyScale\n        axis: 'x' | 'y'\n        values?: TicksSpec<Value>\n    }\n) => {\n    const lines = computeGridLines({ width, height, scale, axis, values })\n\n    lines.forEach(line => {\n        ctx.beginPath()\n        ctx.moveTo(line.x1, line.y1)\n        ctx.lineTo(line.x2, line.y2)\n        ctx.stroke()\n    })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAoBf,aAAS,KAAK,OAAO;AACnB,aAAQ,SAAS,MAAM,SAAU,SAAS,KAAK,IAAI,CAAC;AAAA,IACtD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AAGnB,QAAI,UAAU;AASd,aAAS,WAAW,OAAO;AACzB,aAAO,aAAa,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,IACrD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,YAAY;AADhB,QAEI,WAAW;AAGf,QAAI,aAAa,YAAY,SAAS;AAmBtC,QAAI,SAAS,aAAa,UAAU,UAAU,IAAI;AAElD,WAAO,UAAU;AAAA;AAAA;;;;;;;;;;;;;;;;;ICZJA,IAA6C,CACtD,SAAAC,IAAAA;AAAI,SAAIA,GAAKC,gBAAgB,CAAA;AAAE,GAC/B,SAAAD,IAAAA;AAAI,SAAIA,GAAKE,WAAW,CAAA;AAAE,GAC1B,SAAAF,IAAAA;AAAI,SAAIA,GAAKG,WAAW,CAAA;AAAE,GAC1B,SAAAH,IAAAA;AAAI,SAAIA,GAAKI,SAAS,CAAA;AAAE,GACxB,SAAAJ,IAAAA;AAAI,SAAIA,GAAKK,QAAQ,CAAA;AAAE,GACvB,SAAAL,IAAAA;AAAI,SAAIA,GAAKM,SAAS,CAAA;AAAE,CAAA;IAGfC,IAA2E,EACpFC,aAAa,CAAA,GACbC,QAAQV,EAAiBW,MAAM,GAAG,CAAA,GAClCC,QAAQZ,EAAiBW,MAAM,GAAG,CAAA,GAClCE,MAAMb,EAAiBW,MAAM,GAAG,CAAA,GAChCG,KAAKd,EAAiBW,MAAM,GAAG,CAAA,GAC/BI,OAAOf,EAAiBW,MAAM,GAAG,CAAA,GACjCK,MAAMhB,EAAiBW,MAAM,GAAG,CAAA,EAAA;IAGvBM,IAAwB,SAACC,IAAAA;AAAyB,SAAK,SAACjB,IAAAA;AAKjE,WAJAO,EAAuBU,EAAAA,EAAWC,QAAQ,SAAAC,IAAAA;AACtCA,MAAAA,GAAOnB,EAAAA;IACX,CAAA,GAEOA;EAAAA;AACV;IAEYoB,IAAuB,SAAHC,IAAAA;AAQ3B,MAAAC,KAAAD,GAPFE,QAAAA,KAAAA,WAAMD,KAAG,WAAQA,IAAAE,KAAAH,GACjBJ,WAAAA,KAAAA,WAASO,KAAG,gBAAaA,IAAAC,IAAAJ,GACzBK,QAAAA,IAAAA,WAAMD,KAAOA,GAMPE,IAAcX,EAAsBC,EAAAA;AAE1C,SAAO,SAACW,IAAAA;AACJ,QAAA,WAAIA,GACA,QAAOA;AAGX,QAAe,aAAXL,MAAuBK,cAAiBC,KACxC,QAAOF,EAAYC,EAAAA;AAGvB,QAAME,KAAYJ,IAASK,SAASR,EAAAA,IAAUS,UAAUT,EAAAA;AACxD,WAAOI,EAAYG,GAAUF,EAAAA,CAAAA;EAAAA;AAErC;IC5DaK,IAAiD,EAC1DC,MAAM,UACNC,KAAK,GACLC,KAAK,QACLC,SAAAA,OACAC,SAAAA,OACAC,OAAAA,OACAC,MAAAA,MACAC,OAAAA,MAAO;IAGEC,IAAoB,SAAHrB,IAU1BsB,IACAC,IACAC,IAAAA;AACC,MACGC,IACgBC,IAMhBC,GACgBC,GATnBC,IAAA7B,GAXGc,KAAAA,KAAAA,WAAGe,IAAGjB,EAAoBE,MAAGe,GAAAC,IAAA9B,GAC7Be,KAAAA,IAAAA,WAAGe,IAAGlB,EAAoBG,MAAGe,GAAAC,IAAA/B,GAC7BgB,SAAAA,IAAAA,WAAOe,IAAGnB,EAAoBI,UAAOe,GAAAC,IAAAhC,GACrCiB,SAAAA,IAAAA,WAAOe,IAAGpB,EAAoBK,UAAOe,GAAAC,IAAAjC,GACrCkB,OAAAA,IAAAA,WAAKe,IAAGrB,EAAoBM,QAAKe,GAAAC,KAAAlC,GACjCmB,MAAAA,KAAAA,WAAIe,KAAGtB,EAAoBO,OAAIe,IAAAC,KAAAnC,GAC/BoB,OAAAA,KAAAA,WAAKe,KAAGvB,EAAoBQ,QAAKe;AAOzB,aAARrB,KACAW,KAAAA,SAAWT,IAAAA,SAAgBU,KAAIJ,GAAKc,cAAUV,KAAI,IAAKJ,GAAKR,MAE5DW,KAAWX;AAIH,aAARC,IACAY,IAAAA,SAAWX,IAAAA,SAAgBY,IAAIN,GAAKe,cAAUT,IAAI,IAAKN,GAAKP,MAE5DY,IAAWZ;AAGf,MAAMuB,KAAQC,OAAAA,EACTC,MAAe,QAAThB,KAAe,CAAC,GAAGD,EAAAA,IAAQ,CAACA,IAAM,CAAA,CAAA,EACxCkB,YAAYrB,KAAQsB,gBAAmBC,cAAAA,EACvCC,OAAO3B,IAAU,CAACU,GAAUF,EAAAA,IAAY,CAACA,IAAUE,CAAAA,CAAAA,EACnDT,MAAMA,CAAAA;AAKX,SAAA,SAHIC,KAAemB,GAAMnB,KAAAA,IACA,YAAA,OAATA,MAAmBmB,GAAMnB,KAAKA,EAAAA,GAEvC0B,EAAgBP,IAAOtB,CAAAA;AAClC;IAEa6B,IAAkB,SAC3BP,IACAtB,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA;AAEA,MAAM8B,KAAaR;AAInB,SAHAQ,GAAWjC,OAAO,UAClBiC,GAAW9B,UAAUA,IAEd8B;AACX;IC7DaC,KAAmB,SAC5BC,IACA1B,IACAC,IAAAA;AAEA,MAEMuB,KAFQG,MAAAA,EAAoBT,MAAM,CAAC,GAAGjB,EAAAA,CAAAA,EAAOqB,OAAOtB,GAAK4B,GAAAA;AAK/D,SAFAJ,GAAWjC,OAAO,SAEXiC;AACX;ICXaK,KAA6C,EACtDC,MAAM,QACNC,OAAAA,MAAO;IAGEC,KAAkB,SAAHC,IAExBC,IACAC,IACAC,IAAAA;AACC,MAAAC,KAAAJ,GAJCF,OAAAA,KAAAA,WAAKM,KAAGR,GAAkBE,QAAKM,IAK3BC,IAAQC,KAAAA,EACTC,MAAe,QAATJ,KAAe,CAAC,GAAGD,EAAAA,IAAQ,CAACA,IAAM,CAAA,CAAA,EACxCM,OAAOP,GAAKQ,GAAAA,EACZX,MAAMA,EAAAA;AAEX,SAAOY,GAAqBL,CAAAA;AAChC;IAEaK,KAAgB,SAA4BL,IAAAA;AACrD,MAAMM,KAAaN;AAGnB,SAFAM,GAAWd,OAAO,QAEXc;AACX;ICvBaC,KAA6C,EACtDf,MAAM,QACNgB,QAAQ,UACRC,WAAW,eACXC,KAAK,QACLC,KAAK,QACLC,QAAAA,MACAC,MAAAA,MAAM;IAGGC,KAAkB,SAAHnB,IASxBC,IACAC,IAAAA;AACC,MAGGkB,IASAC,IAZHC,KAAAtB,GATGa,QAAAA,IAAAA,WAAMS,KAAGV,GAAkBC,SAAMS,IAAAC,IAAAvB,GACjCc,WAAAA,IAAAA,WAASS,IAAGX,GAAkBE,YAASS,GAAAC,IAAAxB,GACvCe,KAAAA,IAAAA,WAAGS,IAAGZ,GAAkBG,MAAGS,GAAAC,IAAAzB,GAC3BgB,KAAAA,IAAAA,WAAGS,IAAGb,GAAkBI,MAAGS,GAAAC,IAAA1B,GAC3BiB,QAAAA,IAAAA,WAAMS,IAAGd,GAAkBK,SAAMS,GAAAC,IAAA3B,GACjCkB,MAAAA,IAAAA,WAAIS,IAAGf,GAAkBM,OAAIS,GAK3BC,IAAYC,EAAqB,EAAEhB,QAAAA,GAAQC,WAAAA,GAAWG,QAAAA,EAAAA,CAAAA;AAIxDG,EAAAA,KADQ,WAARL,IACWa,EAAU3B,GAAKc,GAAAA,IACR,aAAXF,IACIe,EAAUb,CAAAA,IAEVA,GAKXM,KADQ,WAARL,IACWY,EAAU3B,GAAKe,GAAAA,IACR,aAAXH,IACIe,EAAUZ,CAAAA,IAEVA;AAGf,MAAMX,IAAQY,IAASa,QAAAA,IAAaC,KAAAA;AAEpC1B,IAAME,MAAM,CAAC,GAAGL,EAAAA,CAAAA,GAEZkB,MAAYC,MAAUhB,EAAMG,OAAO,CAACY,IAAUC,EAAAA,CAAAA,GAAAA,SAE9CH,IAAeb,EAAMa,KAAAA,IACA,YAAA,OAATA,KAAqC,YAAA,OAATA,KAAmBb,EAAMa,KAAKA,CAAAA;AAE1E,MAAMP,KAAaN;AAKnB,SAHAM,GAAWd,OAAO,QAClBc,GAAWM,SAASA,GAEbN;AACX;IC1DaqB,KAA2C,EACpDnC,MAAM,OACNoC,MAAM,IACNlB,KAAK,QACLC,KAAK,QACLlB,OAAAA,OACAoC,SAAAA,OACAhB,MAAAA,KAAM;IAGGiB,KAAiB,SAAHnC,IASvBC,IACAC,IACAC,IAAAA;AACC,MAMGiC,IANHC,KAAArC,GAVGiC,MAAAA,IAAAA,WAAII,KAAGL,GAAiBC,OAAII,IAAAb,IAAAxB,GAC5Be,KAAAA,IAAAA,WAAGS,IAAGQ,GAAiBjB,MAAGS,GAAAC,IAAAzB,GAC1BgB,KAAAA,KAAAA,WAAGS,IAAGO,GAAiBhB,MAAGS,GAAArB,IAAAJ,GAC1BF,OAAAA,IAAAA,WAAKM,IAAG4B,GAAiBlC,QAAKM,GAAAkC,IAAAtC,GAC9BkC,SAAAA,IAAAA,WAAOI,IAAGN,GAAiBE,UAAOI,GAAAX,IAAA3B,GAClCkB,MAAAA,IAAAA,WAAIS,IAAGK,GAAiBd,OAAIS;AAOhC,MADgB1B,GAAKQ,IAAI8B,KAAK,SAAAC,IAAAA;AAAC,WAAU,MAANA;EAAAA,CAAAA,EAE/B,OAAM,IAAIC,MAAK,mDAAA;AAInB,MAgBIrB,GAOAC,GAvBAqB,IAAAA;AAYJ,MAXAzC,GAAKQ,IACAkC,OAAO,SAAAH,IAAAA;AAAC,WAAS,QAALA;EAAS,CAAA,EACrBI,QAAQ,SAAAJ,IAAAA;AACDE,UAAAA,WACAN,KACAA,KAAOS,KAAKT,KAAKI,EAAAA,IACVK,KAAKT,KAAKI,EAAAA,MAAOJ,OACxBM,IAAAA;EAER,CAAA,GAEAA,EACA,OAAM,IAAID,MAAK,mEAAA;AAKfrB,MADQ,WAARL,IACWd,GAAKc,MAELA,GAKXM,IADQ,WAARL,KACWf,GAAKe,MAELA;AAGf,MAAMX,KAAQyC,IAAAA,EAA2Bb,KAAKA,CAAAA,GAExC1B,KAAiB,QAATJ,KAAe,CAAC,GAAGD,EAAAA,IAAQ,CAACA,IAAM,CAAA;AAAA,WAC5CJ,IAAgBO,GAAM0C,WAAWxC,EAAAA,IAChCF,GAAME,MAAMA,EAAAA,GAAAA,SAEb2B,IAAkB7B,GAAMG,OAAO,CAACa,GAAUD,CAAAA,CAAAA,IACzCf,GAAMG,OAAO,CAACY,GAAUC,CAAAA,CAAAA,GAAAA,SAEzBH,IAAeb,GAAMa,KAAAA,IAEA,YAAA,OAATA,KAAmBb,GAAMa,KAAKA,CAAAA;AAE9C,MAAMP,KAAaN;AAGnB,SAFAM,GAAWd,OAAO,OAEXc;AACX;IC5EaqC,KAAiD,EAC1DnD,MAAM,UACNoD,UAAU,GACVlC,KAAK,QACLC,KAAK,QACLlB,OAAAA,OACAoC,SAAAA,OACAhB,MAAAA,KAAM;IAGGgC,KAAoB,SAAHlD,IAS1BC,IACAC,IACAC,IAAAA;AACC,MACGiB,IAOAC,IARH8B,IAAAnD,GAVGiD,UAAAA,IAAAA,WAAQE,IAAGH,GAAoBC,WAAQE,GAAA3B,IAAAxB,GACvCe,KAAAA,IAAAA,WAAGS,IAAGwB,GAAoBjC,MAAGS,GAAAC,KAAAzB,GAC7BgB,KAAAA,IAAAA,WAAGS,KAAGuB,GAAoBhC,MAAGS,IAAArB,IAAAJ,GAC7BF,OAAAA,IAAAA,WAAKM,IAAG4C,GAAoBlD,QAAKM,GAAAkC,IAAAtC,GACjCkC,SAAAA,IAAAA,WAAOI,IAAGU,GAAoBd,UAAOI,GAAAX,IAAA3B,GACrCkB,MAAAA,IAAAA,WAAIS,IAAGqB,GAAoB9B,OAAIS;AAQ/BP,EAAAA,KADQ,WAARL,IACWd,GAAKc,MAELA,GAKXM,KADQ,WAARL,IACWf,GAAKe,MAELA;AAGf,MAAMX,IAAQ+C,OAAAA,EAA8BH,SAASA,CAAAA,GAE/C1C,IAAiB,QAATJ,KAAe,CAAC,GAAGD,EAAAA,IAAQ,CAACA,IAAM,CAAA;AAAA,WAC5CJ,IAAgBO,EAAM0C,WAAWxC,CAAAA,IAChCF,EAAME,MAAMA,CAAAA,GAAAA,SAEb2B,IAAkB7B,EAAMG,OAAO,CAACa,IAAUD,EAAAA,CAAAA,IACzCf,EAAMG,OAAO,CAACY,IAAUC,EAAAA,CAAAA,GAAAA,SAEzBH,IAAeb,EAAMa,KAAAA,IACA,YAAA,OAATA,KAAmBb,EAAMa,KAAKA,CAAAA;AAE9C,MAAMP,KAAaN;AAGnB,SAFAM,GAAWd,OAAO,UAEXc;AACX;ICFa0C,KAAe,SAAClD,IAAAA;AAAe,SAA0B,QAATA,KAAe,MAAM;AAAG;IAExEmD,KAAgB,SAACC,IAAoBC,IAAAA;AAAkB,SAAKD,OAAMC;AAAC;IACnEC,KAAoB,SAACF,IAASC,IAAAA;AAAO,SAAKD,GAAEG,QAAAA,MAAcF,GAAEE,QAAAA;AAAS;AAE3E,SAASC,GACZC,IACA3D,IACAC,IACAC,IAAAA;AAEA,UAAQyD,GAAK/D,MAAAA;IACT,KAAK;AACD,aAAOgE,EAAkBD,IAAM3D,IAAMC,IAAMC,EAAAA;IAC/C,KAAK;AACD,aAAO2D,GAAuCF,GAAM3D,IAAMC,EAAAA;IAC9D,KAAK;AACD,aAAOH,GAAsC6D,IAAM3D,IAAMC,IAAMC,EAAAA;IACnE,KAAK;AACD,aAAOgB,GAAgByC,IAAM3D,IAAMC,EAAAA;IACvC,KAAK;AACD,aAAOiC,GAAeyB,IAAM3D,IAAMC,IAAMC,EAAAA;IAC5C,KAAK;AACD,aAAO+C,GAAkBU,IAAM3D,IAAMC,IAAMC,EAAAA;IAC/C;AACI,YAAM,IAAIsC,MAAM,oBAAA;EAAA;AAE5B;AASA,IAOMsB,KAAuB,SACzBC,IACA7D,IACAE,IAAAA;AACgB,MAAA4D;AAChB,MAAI,aAAa5D,MAASA,GAAM6D,SAAS;AACrC,QAAMC,KAAeH,GAAM/D,KAAc,QAATE,KAAe,aAAa,UAAA;AAC5D,WAAIgE,QAAAA,KACO,OAGJ9D,GAAM8D,EAAAA;EACjB;AAEA,SAA8BF,SAA9BA,KAAO5D,GAAM2D,GAAM/D,KAAKE,EAAAA,CAAAA,KAAM8D,KAAI;AACtC;AAtBA,IA+BaG,KAA2B,SACpCC,IACAC,IACAC,IACAC,IACAC,IAAAA;AAGA,MAAMC,KAAeL,GAAOM,IAAI,SAAAC,IAAAA;AAAK,WAvCnB,SAClBA,IAAAA;AAAkB,aAAAC,EAAAA,CAAAA,GAEfD,IAAK,EACR3E,MAAM2E,GAAM3E,KAAK0E,IAAI,SAAAG,IAAAA;AAAC,eAAK,EAAE7E,MAAI4E,EAAA,CAAA,GAAOC,EAAAA,EAAAA;MAAAA,CAAAA,EAAAA,CAAAA;IAAO,EAmCcF,EAAAA;EAAAA,CAAAA,GAGvDG,IAAKC,GAAuBN,IAAcJ,IAAYC,EAAAA;AAGxD,eAAaD,MAAAA,SAAcA,GAAWJ,WACtCe,GAAaF,GAAiBL,EAAAA,GAI9B,aAAaH,MAAAA,SAAcA,GAAWL,WACtCgB,GAAaH,GAAiBL,EAAAA;AAIlC,MAAMS,IAASxB,GAAqBW,IAAYS,EAAGK,GAAGZ,IAAO,GAAA,GACvDa,IAAS1B,GAAqBY,IAAYQ,EAAGO,GAAGb,IAAQ,GAAA,GAGxDc,IAAwCb,GAAaC,IAAI,SAAAC,IAAAA;AAAK,WAAAC,EAAAA,CAAAA,GAC7DD,IAAK,EACR3E,MAAM2E,GAAM3E,KAAK0E,IAAI,SAAAX,IAAAA;AAAK,aAAAa,EAAAA,CAAAA,GACnBb,IAAK,EACRwB,UAAU,EACNJ,GAAGrB,GAAqBC,IAAO,KAAKmB,CAAAA,GACpCG,GAAGvB,GAAqBC,IAAO,KAAKqB,CAAAA,EAAAA,EAAAA,CAAAA;IAAAA,CAAAA,EAAAA,CAAAA;EAEzC,CAAA;AAGP,SAAAR,EAAAA,CAAAA,GACOE,GAAE,EACLV,QAAQkB,GACRJ,QAAAA,GACAE,QAAAA,EAAAA,CAAAA;AAER;AA5EA,IA8EaL,KAAmB,SAC5BX,IACAC,IACAC,IAAAA;AAAqB,SACnB,EACFa,GAAGK,GAAgCpB,IAAQ,KAAKC,EAAAA,GAChDgB,GAAGG,GAAgCpB,IAAQ,KAAKE,EAAAA,EAAAA;AACnD;AArFD,IA2FakB,KAAqB,SAC9BpB,IACAlE,IACAuF,GAAoBC,GAAAA;AAUnB,MAAA3F,IAAAA,WAAA2F,IADG,CAAE,IAAAA,GAAAC,IAAA5F,EAPF6F,UAAAA,KAAAA,WAAQD,IAAG,SAAAd,IAAAA;AAAC,WAAIA,GAAE7E,KAAKE,EAAAA;EAAK,IAAAyF,GAAAE,IAAA9F,EAC5B+F,UAAAA,IAAAA,WAAQD,IAAG,SAAChB,IAAGtC,IAAAA;AACXsC,IAAAA,GAAE7E,KAAKE,EAAAA,IAAQqC;EAClB,IAAAsD;AAML,MAAuB,aAAnBJ,EAAU7F,KACVwE,CAAAA,GAAOzB,QAAQ,SAAAgC,IAAAA;AACXA,IAAAA,GAAM3E,KAAK2C,QAAQ,SAAAkC,IAAAA;AACf,UAAMkB,KAAQH,GAASf,EAAAA;AAEnBkB,MAAAA,MACAD,EAASjB,IAAGmB,WAAWC,OAAOF,EAAAA,CAAAA,CAAAA;IAEtC,CAAA;EACJ,CAAA;WAC0B,WAAnBN,EAAU7F,QAAwC,aAArB6F,EAAU7E,QAAqB;AAGnE,QAAMsF,IAAYtE,EAAqB6D,CAAAA;AAEvCrB,IAAAA,GAAOzB,QAAQ,SAAAgC,IAAAA;AACXA,MAAAA,GAAM3E,KAAK2C,QAAQ,SAAAkC,IAAAA;AACf,YAAMkB,KAAQH,GAASf,EAAAA;AAEnBkB,QAAAA,MACAD,EAASjB,IAAGqB,EAAUH,EAAAA,CAAAA;MAE9B,CAAA;IACJ,CAAA;EACJ;AAEA,MAAMI,IAAoB,CAAA;AAQ1B,UANA/B,GAAOzB,QAAQ,SAAAgC,IAAAA;AACXA,IAAAA,GAAM3E,KAAK2C,QAAQ,SAAAkC,IAAAA;AACfsB,QAAOC,KAAKR,GAASf,EAAAA,CAAAA;IACzB,CAAA;EACJ,CAAA,GAEQY,EAAU7F,MAAAA;IACd,KAAK;AACD,UAAMY,QAAM6F,cAAAA,aAERC,YAAAA,SAAKH,CAAAA,EAAoBzD,OAAO,SAAAH,IAAAA;AAAC,eAAU,SAANA;MAAAA,CAAAA,GACrC,SAAAA,IAAAA;AAAC,eAAIA;MAAC,CAAA;AAGV,aAAO,EAAE/B,KAAAA,GAAKM,KAAK8B,KAAK9B,IAAGyF,MAAR3D,MAAYpC,CAAAA,GAAMO,KAAK6B,KAAK7B,IAAGwF,MAAR3D,MAAYpC,CAAAA,EAAAA;IAE1D,KAAK;AACD,UAAMA,QAAMgG,cAAAA,SAAOL,GAAkB,SAAA5D,IAAAA;AAAC,eAAIA,GAAEkB,QAAAA;MAAAA,CAAAA,EACvCgD,MAAM,CAAA,EACNC,KAAK,SAACpD,IAAGC,IAAAA;AAAC,eAAKA,GAAEE,QAAAA,IAAYH,GAAEG,QAAAA;MAAAA,CAAAA,EAC/BxB,QAAAA;AAEL,aAAO,EAAEzB,KAAAA,GAAKM,KAAKN,EAAI,CAAA,GAAIO,SAAK4F,YAAAA,SAAKnG,CAAAA,EAAAA;IAEzC;AACI,UAAMA,QAAM8F,YAAAA,SAAKH,CAAAA;AAEjB,aAAO,EAAE3F,KAAAA,GAAKM,KAAKN,EAAI,CAAA,GAAIO,SAAK4F,YAAAA,SAAKnG,CAAAA,EAAAA;EAAAA;AAGjD;AAnKA,IAqKaoG,KAAY,SACrB1G,IACA4E,IACAV,IAAAA;AAEA,MAAMyC,KAAYzD,GAAalD,EAAAA,GACzBM,IAAgB,CAAA;AAEtBsE,EAAAA,GAAG+B,EAAAA,EAAWrG,IAAImC,QAAQ,SAAAJ,IAAAA;AACtB,QAAMuE,QAAWC,cAAAA,SAAOxE,EAAAA,IAAKiB,KAAoBH,IAC3C2D,IAA8B,CAAA;AAEpC5C,IAAAA,GAAOzB,QAAQ,SAAAgC,IAAAA;AACX,UAAMZ,KAAQY,GAAM3E,KAAKiH,KAAK,SAAApC,IAAAA;AAAC,eAAIiC,EAAQjC,GAAE7E,KAAK6G,EAAAA,GAAYtE,EAAAA;MAAAA,CAAAA,GAC1DwD,IAAQ,MACRmB,KAAa;AAEjB,UAAA,WAAInD,IAAqB;AAGrB,YAAc,UADdgC,IAAQhC,GAAM/D,KAAKE,EAAAA,IACC;AAChB,cAAMiH,QAAOR,YAAAA,SAAKK,CAAAA;AAAAA,qBACdG,IACAD,KAAanB,IACG,SAAToB,MACPD,KAAaC,IAAOpB;QAE5B;AAEAhC,QAAAA,GAAM/D,KAAc,QAATE,KAAe,aAAa,UAAA,IAAcgH;MACzD;AAEAF,QAAMZ,KAAKc,EAAAA,GAEQ,SAAfA,MACA1G,EAAI4F,KAAKc,EAAAA;IAEjB,CAAA;EACJ,CAAA,GAEApC,GAAG5E,EAAAA,EAAMkH,aAAaxE,KAAK9B,IAAGyF,MAAR3D,MAAYpC,CAAAA,GAClCsE,GAAG5E,EAAAA,EAAMmH,aAAazE,KAAK7B,IAAGwF,MAAR3D,MAAYpC,CAAAA;AACtC;AA/MA,IAiNMwE,KAAS,SACXF,IACAV,IAAAA;AAA2B,SAC1BwC,GAAgB,KAAK9B,IAAIV,EAAAA;AAAO;AApNrC,IAsNMa,KAAS,SACXH,IACAV,IAAAA;AAA2B,SAC1BwC,GAAgB,KAAK9B,IAAIV,EAAAA;AAAO;AAzNrC,ICnDakD,KAAc,SAAQlH,IAAAA;AAC/B,MAAMmH,KAAYnH,GAAMmH,UAAAA;AAExB,MAAkB,MAAdA,GAAiB,QAAOnH;AAE5B,MAAIoH,KAASD,KAAY;AAKzB,SAJInH,GAAMP,MAAAA,MACN2H,KAAS5E,KAAK/C,MAAM2H,EAAAA,IAGjB,SAAkB3C,IAAAA;AAAI,QAAAb;AAAA,YAAc,SAATA,KAAC5D,GAAMyE,EAAAA,KAAEb,KAAI,KAAKwD;EAAM;AAC9D;ADwCA,ICxBMC,KAA6E,EAC/EC,aAAa,CAACC,qBAAiBC,mBAAAA,GAC/BC,QAAQ,CAACC,gBAAYC,cAAAA,GACrBC,QAAQ,CAACC,gBAAYC,iBAAAA,GACrBC,MAAM,CAACC,cAAUC,eAAAA,GACjBC,KAAK,CAnBOC,YACZ,SAAAC,IAAAA;AAAI,SAAIA,GAAKC,SAAS,GAAG,GAAG,GAAG,CAAA;AAAE,GACjC,SAACD,IAAME,IAAAA;AAAI,SAAKF,GAAKG,QAAQH,GAAKI,QAAAA,IAAYF,EAAAA;AAAK,GACnD,SAACG,IAAOC,IAAAA;AAAG,UAAMA,GAAIrF,QAAAA,IAAYoF,GAAMpF,QAAAA,KAAa;AAAK,GACzD,SAAA+E,IAAAA;AAAI,SAAI5F,KAAKmG,MAAMP,GAAK/E,QAAAA,IAAY,KAAA;AAAM,CAAA,GAG/B8E,YACX,SAAAC,IAAAA;AAAI,SAAIA,GAAKQ,YAAY,GAAG,GAAG,GAAG,CAAA;AAAE,GACpC,SAACR,IAAME,IAAAA;AAAI,SAAKF,GAAKS,WAAWT,GAAKU,WAAAA,IAAeR,EAAAA;AAAK,GACzD,SAACG,IAAOC,IAAAA;AAAG,UAAMA,GAAIrF,QAAAA,IAAYoF,GAAMpF,QAAAA,KAAa;AAAK,GACzD,SAAA+E,IAAAA;AAAI,SAAI5F,KAAKmG,MAAMP,GAAK/E,QAAAA,IAAY,KAAA;AAAM,CAAA,CAAA,GAS1C0F,MAAM,CAACC,QAAUC,SAAAA,GACjBC,QAAQ,CAACC,QAAYC,SAAAA,GACrBC,QAAQ,CAACC,QAAYC,SAAAA,GACrBC,SAAS,CAACC,SAAaC,UAAAA,GACvBC,WAAW,CAACC,WAAeC,YAAAA,GAC3BC,UAAU,CAACC,UAAcC,WAAAA,GACzBC,QAAQ,CAACC,QAAYC,SAAAA,GACrBC,UAAU,CAACC,UAAcC,WAAAA,GACzBC,OAAO,CAACC,eAAWC,gBAAAA,GACnBC,MAAM,CAACC,cAAUC,eAAAA,EAAAA;ADSrB,ICNMC,KAAYC,OAAOC,KAAK1D,EAAAA;ADM9B,ICLM2D,KAAqB,IAAIC,OAAgCJ,2BAAAA,GAAUK,KAAK,GAAA,IAAI,QAAQ,GAAA;ADK1F,ICAaC,KAAgB,SACzBnL,IACAuD,IAAAA;AAGA,MAAI6H,MAAMC,QAAQ9H,EAAAA,EACd,QAAOA;AAGX,MAAoB,YAAA,OAATA,MAAqB,YAAYvD,IAAO;AAE/C,QAAMsL,KAAU/H,GAAKgI,MAAMP,EAAAA;AAE3B,QAAIM,IAAS;AACT,UAASE,KAAgBF,GAAO,CAAA,GAAf9L,KAAQ8L,GAAO,CAAA,GAI1BG,KAAWpE,GAAW7H,EAAAA,EAAMQ,GAAMY,SAAS,IAAI,CAAA;AAErD,UAAa,UAATpB,IAAgB;AAAA,YAAAkM,GAAAC,GAChBC,IAA8B5L,GAAMG,OAAAA,GAA7BsI,IAAKmD,EAAA,CAAA,GAAEC,KAAYD,EAAA,CAAA,GACpBE,IAAO,IAAIC,KAAKF,EAAAA;AAKtB,eAFAC,EAAKvD,QAAQuD,EAAKtD,QAAAA,IAAY,CAAA,GAEgCkD,SAA9DA,IAA0C,SAA1CC,IAAOF,GAASO,MAAMC,OAAa,QAANT,KAAAA,KAAU,CAAA,CAAA,KAAA,SAAhCG,EAAqCzL,MAAMuI,GAAOqD,CAAAA,KAAKJ,IAAI,CAAA;MACtE;AAEA,UAAA,WAAIF,GACA,QAAOxL,GAAMkM,MAAMT,EAAAA;AAGvB,UAAMU,IAAWV,GAASO,MAAMC,OAAOT,EAAAA,CAAAA;AAEvC,UAAIW,EACA,QAAOnM,GAAMkM,MAAMC,CAAAA;IAE3B;AAEA,UAAM,IAAI/J,MAA6BmB,yBAAAA,EAAAA;EAC3C;AAGA,MAAI,WAAWvD,IAAO;AAElB,QAAA,WAAIuD,GACA,QAAOvD,GAAMkM,MAAAA;AAIjB,QAtDa,YAAA,QADFvG,IAuDGpC,OAtDW6I,SAASzG,CAAAA,KAAUnD,KAAKmG,MAAMhD,CAAAA,MAAWA,EAuD9D,QAAO3F,GAAMkM,MAAM3I,EAAAA;EAE3B;AA1Dc,MAACoC;AA6Df,SAAO3F,GAAMG,OAAAA;AACjB;;;;;;;AEnJA,uBAAwC;AAKxC,IAAI,iBAAiB;AACrB,SAAS,oBAAoB,MAAM,OAAO;AACxC,MAAI,SAAS,QAAQ,OAAO,UAAU,aAAa,UAAU,GAAI,QAAO;AACxE,MAAI,OAAO,UAAU,YAAY,UAAU,KAAK,CAAC,eAAe,KAAK,IAAI,KAAK,EAAE,iBAAiB,eAAe,IAAI,KAAK,iBAAiB,IAAI;AAC5I,WAAO,QAAQ;AACjB,UAAQ,KAAK,OAAO,KAAK;AAC3B;AACA,IAAI,iBAAiB,CAAC;AACtB,SAAS,oBAAoB,UAAU,OAAO;AAC5C,MAAI,CAAC,SAAS,YAAY,CAAC,SAAS,cAAc;AAChD,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,SAAS,aAAa,YAAY,SAAS,cAAc,SAAS,WAAW,aAAa;AAClH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,IACpC,CAAC,SAAS,mBAAmB,SAAS,aAAa,IAAI,IAAI,OAAO,eAAe,IAAI,MAAM,eAAe,IAAI,IAAI,KAAK;AAAA,MACrH;AAAA;AAAA,MAEA,CAACkM,OAAM,MAAMA,GAAE,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,aAAS,cAAc;AAAA,EACzB;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,eAAe,IAAI,GAAG;AAC9B,YAAM,QAAQ,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,iBAAS,MAAM,YAAY,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,iBAAS,MAAM,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,MAAMC,OAAM;AACzB,aAAS,aAAa,MAAM,OAAOA,EAAC,CAAC;AAAA,EACvC,CAAC;AACD,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,eAAe,QAAQ;AACzB,aAAS,aAAa;AAAA,EACxB;AACA,MAAI,YAAY,QAAQ;AACtB,aAAS,aAAa,WAAW,OAAO;AAAA,EAC1C;AACF;AACA,IAAI,mBAAmB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AACA,IAAI,YAAY,CAAC,QAAQ,QAAQ,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AACvF,IAAI,WAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAC1C,mBAAmB,OAAO,KAAK,gBAAgB,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,WAAS,QAAQ,CAAC,WAAW,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AACrE,SAAO;AACT,GAAG,gBAAgB;AAgBnB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,UAAU,CAAC,OAAO,SAAS,GAAG,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,OAAO;AAC7E,IAAI,kBAAkB,CAAC,OAAO,OAAO,GAAG,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC,MAAM,gBAAgB,GAAG,EAAE,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,KAAK,WAAW,KAAK,MAAM;AACvJ,IAAI,gBAAgB,cAAc,eAAe;AAAA,EAC/C,YAAY,EAAE,GAAG,GAAG,GAAAC,IAAG,GAAG,MAAM,GAAG;AACjC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,CAAC;AACpB,QAAI,KAAK,KAAKA,IAAG;AACf,aAAO,KAAK,CAAC,KAAK,GAAG,KAAK,GAAGA,MAAK,CAAC,CAAC;AACpC,iBAAW,KAAK,CAAC,QAAQ;AAAA,QACvB,eAAe,IAAI,IAAI,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,QAEzD,gBAAgB,KAAK,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AACA,aAAS,OAAO,CAAC,OAAO,QAAQ;AAC9B,UAAI,QAAQ,aAAa;AACvB,eAAO,KAAK,CAAC,SAAS,EAAE,CAAC;AACzB,mBAAW,KAAK,CAAC,cAAc,CAAC,WAAW,cAAc,EAAE,CAAC;AAAA,MAC9D,WAAW,cAAc,KAAK,GAAG,GAAG;AAClC,eAAO,MAAM,GAAG;AAChB,YAAI,GAAG,IAAI,KAAK,EAAG;AACnB,cAAM,OAAO,aAAa,KAAK,GAAG,IAAI,OAAO,cAAc,KAAK,GAAG,IAAI,QAAQ;AAC/E,eAAO,KAAK,QAAQ,KAAK,CAAC;AAC1B,mBAAW;AAAA,UACT,QAAQ,aAAa,CAAC,CAAC,IAAI,IAAIC,KAAI,GAAG,MAAM;AAAA,YAC1C,YAAY,EAAE,IAAI,EAAE,IAAIA,GAAE,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,YAChD,gBAAgB,KAAK,CAAC;AAAA,UACxB,IAAI,CAAC,UAAU;AAAA,YACb,GAAG,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACtD,gBAAgB,OAAO,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY,IAAI,eAAe,QAAQ,UAAU;AAAA,IACzD;AACA,UAAM,KAAK;AAAA,EACb;AACF;AACA,IAAI,iBAAiB,cAAc,WAAW;AAAA,EAC5C,YAAY,QAAQ,YAAY;AAC9B,UAAM;AACN,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM;AACJ,WAAO,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA,EACA,OAAO;AACL,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,SAAK,KAAK,QAAQ,CAAC,OAAOF,OAAM;AAC9B,YAAM,OAAO,cAAc,MAAM,CAAC,CAAC;AACnC,YAAM,CAACG,IAAG,EAAE,IAAI,KAAK,WAAWH,EAAC;AAAA,QAC/B,GAAG,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,aAAa;AAAA,MAC/C;AACA,mBAAa,MAAMG;AACnB,iBAAW,YAAY;AAAA,IACzB,CAAC;AACD,WAAO,WAAW,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,iBAAiB,OAAO,IAAI;AAAA,QACjE;AAAA,MACF;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,oBAAoB,OAAO,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,uBAAmB,MAAM,KAAK;AAAA,EAChC;AACF;AAGA,IAAI,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,gBAAQ,OAAO;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA;AACF,CAAC;AACD,IAAI,OAAO,WAAW,YAAY;AAAA,EAChC;AAAA,EACA,qBAAqB,CAAC,UAAU,IAAI,cAAc,KAAK;AAAA;AAAA,EAEvD,mBAAmB,CAAC,EAAE,WAAW,YAAY,GAAG,MAAM,MAAM;AAC9D,CAAC;AACD,IAAI,WAAW,KAAK;;;;;;;;;;;;;ACnXpB,IAEaC,IAAwB,SAAHC,IAAAA;AAoB5B,MAUEC,IA7BJC,KAAIF,GAAJE,MACAC,KAAKH,GAALG,OACAC,KAAaJ,GAAbI,eACAC,IAAUL,GAAVK,YACAC,IAAQN,GAARM,UACAC,IAAWP,GAAXO,aACAC,IAAYR,GAAZQ,cACAC,IAAcT,GAAdS,gBAAcC,KAAAV,GACdW,QAAAA,IAAAA,WAAMD,KAAG,QAAKA,IAYRE,IAASC,GAAqBV,IAAOE,CAAAA,GAErCS,IAAYC,GAAkBJ,CAAAA,GAE9BK,IAAW,eAAeb,KAAQc,GAAYd,EAAAA,IAASA,IACvDe,IAAO,EAAEC,OAAO,GAAGC,OAAO,EAAA,GAC1BC,IAAO,EAAEC,OAAO,GAAGC,OAAO,EAAA,GAE1BC,IAA4B,YAAA,OAAbC,YAAyC,UAAjBA,SAASC,KAElDC,KAA6Bb,EAAUc,MAAMC,QAC7CC,KAAmChB,EAAUiB,SAASF;AAE7C,UAAT3B,MACAD,KAAY,SAAA+B,IAAAA;AAAC,QAAAC;AAAA,WAAK,EAAEC,GAAc,SAAbD,KAAEjB,EAASgB,EAAAA,KAAEC,KAAI,GAAGE,GAAG,EAAA;EAAA,GAE5CjB,EAAKE,QAAQd,KAA8B,YAAlBF,KAA4B,IAAA,KACrDiB,EAAKE,SAASjB,IAAWC,MAAkC,YAAlBH,KAA4B,IAAA,KAGjE0B,KADkB,YAAlB1B,KACeU,EAAUiB,SAASK,MAEnBtB,EAAUiB,SAASM,QAGjB,MAAjB7B,IACAmB,KAAYb,EAAUc,MAAMC,SAET,YAAlBzB,MAA6BI,IAAe,KAC1B,aAAlBJ,MAA8BI,IAAe,KAE9CmB,KAAYb,EAAUc,MAAMJ,IAAQ,SAAS,OAAA,GAC7CM,KAAehB,EAAUiB,SAASF,WAEf,YAAlBzB,MAA6BI,IAAe,KAC1B,aAAlBJ,MAA8BI,IAAe,OAE9CmB,KAAYb,EAAUc,MAAMJ,IAAQ,UAAU,MAAA,GAC9CM,KAAehB,EAAUiB,SAASF,YAGtC5B,KAAY,SAAA+B,IAAAA;AAAC,QAAAM;AAAA,WAAK,EAAEJ,GAAG,GAAGC,GAAcG,SAAbA,KAAEtB,EAASgB,EAAAA,KAAEM,KAAI,EAAA;EAAA,GAE5CpB,EAAKC,QAAQb,KAA8B,YAAlBF,KAA4B,IAAA,KACrDiB,EAAKC,SAAShB,IAAWC,MAAkC,YAAlBH,KAA4B,IAAA,KAGjEuB,KADkB,YAAlBvB,KACYU,EAAUc,MAAMW,OAEhBzB,EAAUc,MAAMY;AAyBpC,SAAO,EACHC,OAbU7B,EAAO8B,IAAI,SAACC,IAAAA;AACtB,QAAMC,KACe,YAAA,OAAVD,KAXM,SAACA,IAAAA;AAClB,UAAME,KAAcC,OAAOH,EAAAA,EAAOI;AAElC,aAAItC,KAAkBA,IAAiB,KAAKoC,KAAcpC,IACtD,KAAUqC,OAAOH,EAAAA,EAAOK,MAAM,GAAGvC,CAAAA,EAAgBwC,OAAO,KAAA,IAE5D,KAAUN;IAAAA,EAKoCA,EAAAA,IAA8BA;AAC5E,WAAAO,EAAA,EACIC,KAAKR,cAAiBS,OAAI,KAAMT,GAAMU,QAAAA,IAAS,KAAQV,IACvDA,OAAOC,GAAAA,GACJ3C,GAAU0C,EAAAA,GACVzB,GACAG,CAAAA;EAEX,CAAA,GAIIM,WAAAA,IACAG,cAAAA,GAAAA;AAER;AAtGA,IAwGawB,KAAe,SACxBC,IACApD,IAAAA;AAEA,MAAA,WAAWoD,MAA4C,cAAA,OAAXA,GAAuB,QAAOA;AAE1E,MAAmB,WAAfpD,GAAMqD,MAAiB;AACvB,QAAMC,KAAYC,WAAWH,EAAAA;AAE7B,WAAQ,SAACvB,IAAAA;AAAM,aAAKyB,GAAUzB,cAAaoB,OAAOpB,KAAI,IAAIoB,KAAKpB,EAAAA,CAAAA;IAAG;EACtE;AAEA,SAAO2B,OAASJ,EAAAA;AACpB;AArHA,IAuHaK,IAAmB,SAAHC,IAAAA;AAYvB,MAnIclB,IAwHhBmB,KAAKD,GAALC,OACAC,KAAMF,GAANE,QACA5D,KAAK0D,GAAL1D,OACAD,KAAI2D,GAAJ3D,MACQ8D,IAAOH,GAAfjD,QASMA,KArIU+B,KAoImBqB,GApIcC,MAAMC,QAAQvB,EAAAA,IAoIjBqB,IAAAA,WACjBnD,GAAqBV,IAAO6D,CAAAA,GACnDhD,IAAW,eAAeb,KAAQc,GAAYd,EAAAA,IAASA,IAEvDgE,IACO,QAATjE,KACMU,EAAO8B,IAAI,SAACC,IAAAA;AAAY,QAAAyB,IAAAC;AAAA,WAAM,EAC1BlB,KAAKR,cAAiBS,OAAI,KAAMT,GAAMU,QAAAA,IAAS,KAAQV,IACvD2B,IAAmB,SAAjBF,KAAEpD,EAAS2B,EAAAA,KAAMyB,KAAI,GACvBG,IAAmB,SAAjBF,KAAErD,EAAS2B,EAAAA,KAAM0B,KAAI,GACvBG,IAAI,GACJC,IAAIV,GAAAA;EACN,CAAA,IACFnD,EAAO8B,IAAI,SAACC,IAAAA;AAAY,QAAA+B,IAAAC;AAAA,WAAM,EAC1BxB,KAAKR,cAAiBS,OAAI,KAAMT,GAAMU,QAAAA,IAAS,KAAQV,IACvD2B,IAAI,GACJC,IAAIT,IACJU,IAAmB,SAAjBE,KAAE1D,EAAS2B,EAAAA,KAAM+B,KAAI,GACvBD,IAAmBE,SAAjBA,KAAE3D,EAAS2B,EAAAA,KAAMgC,KAAI,EAAA;EAC1B,CAAA;AAEX,SAAOR;AACX;AA1JA,ICyCMS,SAAmBC,aAAAA,MAzCR,SAAH7E,IAAAA;AAUc,MAAA8E,IATjBC,KAAM/E,GAAb2C,OACAY,KAAMvD,GAANuD,QACApC,KAAKnB,GAALmB,OACAC,IAAKpB,GAALoB,OACA4D,IAAOhF,GAAPgF,SACAlD,IAAY9B,GAAZ8B,cACAmD,IAAUjF,GAAViF,YACAC,IAAKlF,GAALkF,OACAC,IAAanF,GAAbmF,eAEMxC,IAAwBmC,SAAnBA,KAAS,QAANvB,KAAAA,SAAAA,GAASwB,EAAAA,KAAOD,KAAIC,IAE5BK,QAAQC,aAAAA,SAAQ,WAAA;AAClB,QAAMC,KAAQ,EAAEC,SAASJ,EAAcI,QAAAA;AAEvC,WAAKP,IAIE,EACHM,OAAKpC,EAAA,CAAA,GAAOoC,IAAK,EAAEE,QAAQ,UAAA,CAAA,GAC3BR,SAAS,SAACS,IAAAA;AAAgD,aAAKT,EAAQS,IAAO9C,CAAAA;IAAM,EAAA,IAL7E,EAAE2C,OAAAA,GAAAA;EAOhB,GAAE,CAACH,EAAcI,SAASP,GAASrC,CAAAA,CAAAA;AAEpC,aACI+C,mBAAAA,MAACC,SAASC,GAAC1C,EAAA,EAAC2C,WAAWV,EAAcU,UAAAA,GAAeT,GAAK,EAAAU,UAAAA,KACrDC,mBAAAA,KAAA,QAAA,EAAMzB,IAAI,GAAGC,IAAIpD,IAAOqD,IAAI,GAAGC,IAAIrD,GAAOkE,OAAOJ,EAAMhE,KAAAA,CAAAA,OACvD6E,mBAAAA,KAACC,GAAI,EACDC,kBAAkBnE,GAClBmD,YAAYA,GACZY,WAAWV,EAAce,eACzBZ,OAAOJ,EAAM7D,MAAKyE,UAEdnD,KAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA,CAAAA;AAIpB,CAAA;ADvCA,IELawD,KAGT,EACA7F,UAAU,GACVC,aAAa,GACbC,cAAc,GACd4F,gBAAgB,UAChBC,cAAc,EAAA;AFHlB,IGKaC,IAAkB,SAAHtG,IAAAA;AA2BtB,MA1BFE,KAAIF,GAAJE,MACAC,KAAKH,GAALG,OAAKoG,IAAAvG,GACLkC,GAAAA,IAAAA,WAACqE,IAAG,IAACA,GAAAC,IAAAxG,GACLmC,GAAAA,IAAAA,WAACqE,IAAG,IAACA,GACLzD,IAAM/C,GAAN+C,QACA3C,IAAaJ,GAAbI,eACAC,IAAUL,GAAVK,YAAUoG,IAAAzG,GACVM,UAAAA,KAAAA,WAAQmG,IAAGN,GAAiB7F,WAAQmG,GAAAC,KAAA1G,GACpCO,aAAAA,KAAAA,WAAWmG,KAAGP,GAAiB5F,cAAWmG,IAAAC,KAAA3G,GAC1CQ,cAAAA,KAAAA,WAAYmG,KAAGR,GAAiB3F,eAAYmG,IAC5CpD,KAAMvD,GAANuD,QAAMqD,KAAA5G,GACN6G,YAAAA,KAAAA,WAAUD,KAAGE,KAAQF,IACrBnG,KAAcT,GAAdS,gBACAsG,KAAM/G,GAAN+G,QAAMC,KAAAhH,GACNoG,gBAAAA,IAAAA,WAAcY,KAAGb,GAAiBC,iBAAcY,IAAAC,IAAAjH,GAChDqG,cAAAA,IAAAA,WAAYY,IAAGd,GAAiBE,eAAYY,GAC5C3B,IAAKtF,GAALsF,OACAN,KAAOhF,GAAPgF,SACAkC,IAAUlH,GAAVkH,YASMhC,KAAQiC,EAAAA,GACRC,KAAYC,EAAqBnC,GAAMhF,MAAMoF,CAAAA,GAE7CgC,QAAcjC,aAAAA,SAAQ,WAAA;AAAA,WAAM/B,GAAaC,IAAQpD,EAAAA;EAAM,GAAE,CAACoD,IAAQpD,EAAAA,CAAAA,GAExEoH,KAA2CxH,EAAsB,EAC7DG,MAAAA,IACAC,OAAAA,IACAC,eAAAA,GACAC,YAAAA,GACAC,UAAAA,IACAC,aAAAA,IACAC,cAAAA,IACAC,gBAAAA,GAAAA,CAAAA,GARIgC,IAAK8E,GAAL9E,OAAOd,KAAS4F,GAAT5F,WAAWG,IAAYyF,GAAZzF,cAWtB0F,KAAa;AACjB,MAAA,WAAIT,IAAsB;AACtB,QAGI9B,IAHAwC,KAAU,GACVC,KAAU,GACVC,KAAiB;AAGR,YAATzH,MACAyH,KAAAA,KACAF,KAAUpB,GACa,YAAnBD,KACAnB,KAAa,SACbyC,KAAU3E,KACgB,aAAnBqD,KACPnB,KAAa,UACbyC,KAAU3E,IAAS,KACO,UAAnBqD,MACPnB,KAAa,WAGjByC,KAAUrB,GACa,YAAnBD,IACAnB,KAAa,UACa,aAAnBmB,KACPnB,KAAa,UACbwC,KAAU1E,IAAS,KACO,UAAnBqD,MACPnB,KAAa,OACbwC,KAAU1E,KAIlByE,SACIzB,mBAAAA,KAAA6B,mBAAAA,UAAA,EAAA9B,cACIC,mBAAAA,KAACC,GAAI,EACDH,WAAAA,eAAwB4B,KAAO,OAAKC,KAAO,cAAYC,KAAkB,KACzE1C,YAAYA,IACZK,OAAKpC,EAAA,CAAA,GACEkE,GAAUL,OAAO1F,MAAI,EACxB4E,kBAAkB,UAAA,CAAA,GACpBH,UAEDiB,GAAAA,CAAAA,EAAAA,CAAAA;EAIjB;AAEA,MAAAc,KAA0CC,GAAAA,GAAlCC,KAAOF,GAAPE,SAAiBC,KAAYH,GAApBI,QAEX9C,KAAgB+C,UAAU,EAC5BrC,WAAwB3D,eAAAA,IAAKC,MAAAA,IAAI,KACjCgG,QAAiB,QAATjI,KAAe6C,IAAS,GAChCqF,QAAiB,QAATlI,KAAe,IAAI6C,GAC3BkF,QAAQD,IACRK,WAAAA,CAAYN,GAAAA,CAAAA,GAGVO,SAAmBC,aAAAA,aACrB,SAACC,IAAAA;AACG,WAAO,EACHjD,SAAS,GACTM,WAAS,eAAe2C,GAAKtG,IAAAA,MAAKsG,GAAKrG,IAAI,KAC3C+D,eAAa,eAAesC,GAAKlH,QAAAA,MAASkH,GAAKjH,QAAK,cAAYf,KAAY,IAAA;EAEpF,GACA,CAACA,EAAAA,CAAAA,GAECiI,SAAuBF,aAAAA,aACzB,SAACC,IAAAA;AAAuB,WAAM,EAC1BjD,SAAS,GACTM,WAAS,eAAe2C,GAAKtG,IAAAA,MAAKsG,GAAKrG,IAAI,KAC3C+D,eAAa,eAAesC,GAAKlH,QAAAA,MAASkH,GAAKjH,QAAK,cAAYf,KAAY,IAAA;EAC/E,GACD,CAACA,EAAAA,CAAAA,GAGCkI,KAAaC,cAGjBlG,GAAO,EACLmG,MAAM,SAAAJ,IAAAA;AAAI,WAAIA,GAAKrF;EAAG,GACtB0F,SAASP,IACTQ,MAAML,IACNM,OAAOT,IACPU,QAAQV,IACRW,OAAO,EACH1D,SAAS,EAAA,GAEb0C,QAAQD,IACRK,WAAAA,CAAYN,GAAAA,CAAAA;AAGhB,aACIrC,mBAAAA,MAACC,SAASC,GAAC,EAACC,WAAWV,GAAcU,WAAW,eAAaqB,GAAWpB,UAAA,CACnE4C,GAAW,SAACQ,IAAiBV,IAAMW,IAAQC,IAAAA;AACxC,WAAaC,iBAAcxC,IAAU3D,EAAA,EACjCkG,WAAAA,IACA7F,QAAQ+D,GACRgC,QAAQ9I,IACRsB,cAAAA,GACAmD,YAAYtD,IACZlB,gBAAgBA,IAChB0E,eAAe+D,IACfhE,OAAOkC,GAAU3E,MAAAA,GACd+F,IACCxD,KAAU,EAAEA,SAAAA,GAAAA,IAAY,CAAA,CAAA,CAAA;EAEpC,CAAA,OACAe,mBAAAA,KAACJ,SAASzE,MAAI,EACVoE,OAAO8B,GAAUmC,OAAOrI,MACxBoD,IAAI,GACJC,IAAIY,GAAcgD,QAClB3D,IAAI,GACJC,IAAIU,GAAciD,OAAAA,CAAAA,GAErBZ,EAAAA,EAAAA,CAAAA;AAGb;AHxKA,IG0KagC,QAAO3E,aAAAA,MAAKyB,CAAAA;AH1KzB,IIPamD,KAAY,CAAC,OAAO,SAAS,UAAU,MAAA;AJOpD,IKDaC,QAAO7E,aAAAA,MAChB,SAAA7E,IAAAA;AAkBM,MAjBF2J,KAAM3J,GAAN2J,QACAC,KAAM5J,GAAN4J,QACA9F,KAAK9D,GAAL8D,OACAC,KAAM/D,GAAN+D,QAeM8F,KAAO,EAAEzH,KAdZpC,GAAHoC,KAcoBI,OAbfxC,GAALwC,OAa2BH,QAZrBrC,GAANqC,QAYmCE,MAX/BvC,GAAJuC,KAAAA;AAaA,aACIwD,mBAAAA,KAAA6B,mBAAAA,UAAA,EAAA9B,UACK2D,GAAU/G,IAAI,SAAA1B,IAAAA;AACX,QAAMd,IAAO2J,GAAK7I,EAAAA;AAIlB,QAAA,CAAKd,EAAM,QAAO;AAElB,QAAM4J,IAAuB,UAAb9I,MAAmC,aAAbA;AAItC,eACI+E,mBAAAA,KAACyD,GAAItG,EAAAA,CAAAA,GAEGhD,GAAI,EACRA,MAAM4J,IAAU,MAAM,KACtB5H,GAAgB,YAAblB,KAAuB8C,KAAQ,GAClC3B,GAAgB,aAAbnB,KAAwB+C,KAAS,GACpC5D,OAAO2J,IAAUH,KAASC,IAC1B7G,QAAQ+G,IAAUhG,KAAQC,IAC1B3D,eAXS,UAAbY,MAAmC,WAAbA,KAAsB,WAAW,SAYnDP,gBAAgBP,EAAKO,eAAAA,CAAAA,GARhBO,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAc7B,CAAA;ALlDJ,IMHa+I,QAAWlF,aAAAA,MACpB,SAAA7E,IAAAA;AAUM,MATFmF,KAAanF,GAAbmF,eAUMD,KAAQiC,EAAAA;AAEd,aACIpB,mBAAAA,KAACJ,SAASzE,MAAIgC,EAAA,CAAA,GACNiC,IACCD,GAAM8E,KAAK9I,IAAAA,CAAAA;AAG5B,CAAA;ANjBJ,IODa+I,QAAYpF,aAAAA,MAAK,SAAA7E,IAAAA;AAAkC,MAA/BmE,KAAKnE,GAALmE,OAC7B0D,KAA0CC,GAAAA,GAAlCC,KAAOF,GAAPE,SAAiBC,KAAYH,GAApBI,QAEXS,IAAaC,cACfxE,IACA,EACIyE,MAAM,SAAA1H,IAAAA;AAAI,WAAIA,GAAKiC;EAAG,GACtB0F,SAAS,SAAA3H,IAAAA;AAAI,WAAK,EACdqE,SAAS,GACTjB,IAAIpD,GAAKoD,IACTC,IAAIrD,GAAKqD,IACTC,IAAItD,GAAKsD,IACTC,IAAIvD,GAAKuD,GAAAA;EACX,GACFqE,MAAM,SAAA5H,IAAAA;AAAI,WAAK,EACXqE,SAAS,GACTjB,IAAIpD,GAAKoD,IACTC,IAAIrD,GAAKqD,IACTC,IAAItD,GAAKsD,IACTC,IAAIvD,GAAKuD,GAAAA;EACX,GACFsE,OAAO,SAAA7H,IAAAA;AAAI,WAAK,EACZqE,SAAS,GACTjB,IAAIpD,GAAKoD,IACTC,IAAIrD,GAAKqD,IACTC,IAAItD,GAAKsD,IACTC,IAAIvD,GAAKuD,GAAAA;EACX,GACFuE,QAAQ,SAAA9H,IAAAA;AAAI,WAAK,EACbqE,SAAS,GACTjB,IAAIpD,GAAKoD,IACTC,IAAIrD,GAAKqD,IACTC,IAAItD,GAAKsD,IACTC,IAAIvD,GAAKuD,GAAAA;EACX,GACFwE,OAAO,EACH1D,SAAS,EAAA,GAEb0C,QAAQD,IACRK,WAAAA,CAAYN,GAAAA,CAAAA;AAIpB,aACIhC,mBAAAA,KAAA,KAAA,EAAAD,UACK4C,EAAW,SAACvD,IAAejE,IAAAA;AAAI,eAC5BgJ,aAAAA,eAACH,GAAQ7G,EAAAA,CAAAA,GAAKhC,IAAI,EAAEiC,KAAKjC,GAAKiC,KAAKgC,eAAeA,GAAAA,CAAAA,CAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAIlE,CAAA;APjDA,IQFagF,QAAOtF,aAAAA,MAChB,SAAA7E,IAAAA;AAcM,MAbF8D,KAAK9D,GAAL8D,OACAC,KAAM/D,GAAN+D,QACA4F,KAAM3J,GAAN2J,QACAC,KAAM5J,GAAN4J,QACAQ,IAAOpK,GAAPoK,SACAC,IAAOrK,GAAPqK,SASMC,QAASjF,aAAAA,SAAQ,WAAA;AACnB,WAAA,CAAA,CAAKsE,MAEE/F,EAAiB,EACpBE,OAAAA,IACAC,QAAAA,IACA5D,OAAOwJ,IACPzJ,MAAM,KACNU,QAAQwJ,EAAAA,CAAAA;EAEf,GAAE,CAACT,IAAQS,GAAStG,IAAOC,EAAAA,CAAAA,GAEtBwG,QAASlF,aAAAA,SAAQ,WAAA;AACnB,WAAA,CAAA,CAAKuE,MAEEhG,EAAiB,EACpBE,OAAAA,IACAC,QAAAA,IACA5D,OAAOyJ,IACP1J,MAAM,KACNU,QAAQyJ,EAAAA,CAAAA;EAEf,GAAE,CAACtG,IAAQD,IAAO8F,IAAQS,CAAAA,CAAAA;AAE3B,aACI3E,mBAAAA,MAAAkC,mBAAAA,UAAA,EAAA9B,UACKwE,CAAAA,SAAUvE,mBAAAA,KAACkE,GAAS,EAAC9F,OAAOmG,EAAAA,CAAAA,GAC5BC,SAAUxE,mBAAAA,KAACkE,GAAS,EAAC9F,OAAOoG,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAGzC,CAAA;AR5CJ,ISEaC,IAAqB,SAC9BC,IAA6BzK,IAAAA;AAoC5B,MAAA0K,IAAAC,IAlCGzK,KAAIF,GAAJE,MACAC,KAAKH,GAALG,OAAKoG,IAAAvG,GACLkC,GAAAA,IAAAA,WAACqE,IAAG,IAACA,GAAAC,IAAAxG,GACLmC,GAAAA,KAAAA,WAACqE,IAAG,IAACA,GACLzD,IAAM/C,GAAN+C,QACA3C,IAAaJ,GAAbI,eACAC,IAAUL,GAAVK,YAAUoG,IAAAzG,GACVM,UAAAA,IAAAA,WAAQmG,IAAGN,GAAiB7F,WAAQmG,GAAAC,IAAA1G,GACpCO,aAAAA,IAAAA,WAAWmG,IAAGP,GAAiB5F,cAAWmG,GAAAC,KAAA3G,GAC1CQ,cAAAA,KAAAA,WAAYmG,KAAGR,GAAiB3F,eAAYmG,IACpC7B,KAAO9E,GAAfuD,QACAwD,KAAM/G,GAAN+G,QAAMC,KAAAhH,GACNoG,gBAAAA,KAAAA,WAAcY,KAAGb,GAAiBC,iBAAcY,IAAAC,KAAAjH,GAChDqG,cAAAA,KAAAA,WAAYY,KAAGd,GAAiBE,eAAYY,IAC5C/B,KAAKlF,GAALkF,OACAI,KAAKtF,GAALsF,OAoBJiC,KAA2CxH,EAAsB,EAC7DG,MAAAA,IACAC,OAAAA,IACAC,eAAAA,GACAC,YAAAA,GACAC,UAAAA,GACAC,aAAAA,GACAC,cAAAA,IACAG,QAAQ,SAAA,CAAA,GARJ8B,KAAK8E,GAAL9E,OAAOd,KAAS4F,GAAT5F,WAAWG,KAAYyF,GAAZzF;AAW1B2I,EAAAA,GAAIG,KAAAA,GACJH,GAAIxK,UAAUiC,GAAGC,EAAAA;AAEjB,MAAMiF,KAAYyD,EAAgB3F,GAAMhF,MAAMoF,EAAAA;AAE9CmF,EAAAA,GAAI9I,YAAYA,IAChB8I,GAAI3I,eAAeA,IAEnBgJ,EAAcL,IAAKrD,GAAU3E,MAAMpB,IAAAA;AAEnC,MAAM0J,KAAmDL,SAApCA,KAAGtD,GAAUmC,OAAOrI,KAAK8J,eAAWN,KAAI;AAC9B,cAAA,OAApBK,MAAgCA,KAAkB,MACzDN,GAAIQ,YAAYF,IAChBN,GAAIS,UAAU,UAEV9D,GAAUmC,OAAOrI,KAAKiK,WACtBV,GAAIW,cAAchE,GAAUmC,OAAOrI,KAAKiK,SAG5CV,GAAIY,UAAAA,GACJZ,GAAIa,OAAO,GAAG,CAAA,GACdb,GAAIc,OAAgB,QAATrL,KAAe6C,IAAS,GAAY,QAAT7C,KAAe,IAAI6C,CAAAA,GACzD0H,GAAIU,OAAAA;AAGR,MAAM5H,KAA4B,cAAA,OAAZuB,KAAyBA,KAAU,SAACnC,IAAAA;AAAc,WAAA,KAAQA;EAAAA,GAE1E6I,IAAgDb,SAAnCA,KAAGvD,GAAU3E,MAAMvB,KAAK8J,eAAWL,KAAI,GACpDc,IAAgD,YAAA,OAAlBD,KAA8BA,IAAgB;AA4BlF,MA3BA/I,GAAMiJ,QAAQ,SAAAlD,IAAAA;AACNiD,UACAhB,GAAIQ,YAAYO,GAChBf,GAAIS,UAAU,UAEV9D,GAAU3E,MAAMvB,KAAKiK,WACrBV,GAAIW,cAAchE,GAAU3E,MAAMvB,KAAKiK,SAG3CV,GAAIY,UAAAA,GACJZ,GAAIa,OAAO9C,GAAKtG,GAAGsG,GAAKrG,CAAAA,GACxBsI,GAAIc,OAAO/C,GAAKtG,IAAIsG,GAAKrH,OAAOqH,GAAKrG,IAAIqG,GAAKpH,KAAAA,GAC9CqJ,GAAIU,OAAAA;AAGR,QAAMxI,KAAQY,GAAOiF,GAAK7F,KAAAA;AAE1B8H,IAAAA,GAAIG,KAAAA,GACJH,GAAIxK,UAAUuI,GAAKtG,IAAIsG,GAAKlH,OAAOkH,GAAKrG,IAAIqG,GAAKjH,KAAAA,GACjDkJ,GAAInB,OAAOqC,GAAiBnL,EAAAA,CAAAA,GAE5BoL,EAAenB,IAAKrD,GAAU3E,MAAMpB,MAASsB,KAAAA,EAAAA,GAE7C8H,GAAIoB,SAAYlJ,KAAAA,IAAS,GAAG,CAAA,GAC5B8H,GAAIqB,QAAAA;EACR,CAAA,GAAA,WAEI/E,IAAsB;AACtB,QAAIU,IAAU,GACVC,IAAU,GACVC,KAAiB,GACjBhG,IAA6B;AAEpB,YAATzB,MACAyH,KAAAA,KACAF,IAAUpB,IACa,YAAnBD,MACAzE,IAAY,SACZ+F,IAAU3E,KACgB,aAAnBqD,MACPzE,IAAY,UACZ+F,IAAU3E,IAAS,KACO,UAAnBqD,OACPzE,IAAY,WAGhB+F,IAAUrB,IACa,YAAnBD,KACAzE,IAAY,UACc,aAAnByE,MACPzE,IAAY,UACZ8F,IAAU1E,IAAS,KACO,UAAnBqD,OACPzE,IAAY,OACZ8F,IAAU1E,KAIlB0H,GAAIxK,UAAUwH,GAASC,CAAAA,GACvB+C,GAAInB,OAAOqC,GAAiBhE,EAAAA,CAAAA,GAC5BmD,EAAcL,IAAKrD,GAAUL,OAAO1F,IAAAA,GAEhC+F,GAAUL,OAAO1F,KAAK0K,SACtBtB,GAAIuB,YAAY5E,GAAUL,OAAO1F,KAAK0K,OAG1CtB,GAAI9I,YAAYA,GAChB8I,GAAI3I,eAAe,UACnB8J,EAAenB,IAAKrD,GAAUL,OAAO1F,MAAM0F,EAAAA;EAC/C;AAEA0D,EAAAA,GAAIqB,QAAAA;AACR;ATxJA,IS0JaG,KAAqB,SAC9BxB,IAA6B5G,IAAAA;AAsB5B,MApBG8F,KAAM9F,GAAN8F,QACAC,KAAM/F,GAAN+F,QACA9F,KAAKD,GAALC,OACAC,KAAMF,GAANE,QACA3B,IAAGyB,GAAHzB,KACAI,IAAKqB,GAALrB,OACAH,IAAMwB,GAANxB,QACAE,IAAIsB,GAAJtB,MACA2C,IAAKrB,GAALqB,OAaE2E,KAAO,EAAEzH,KAAAA,GAAKI,OAAAA,GAAOH,QAAAA,GAAQE,MAAAA,EAAAA;AAEnCkH,EAAAA,GAAUiC,QAAQ,SAAA1K,IAAAA;AACd,QAAMd,KAAO2J,GAAK7I,EAAAA;AAIlB,QAAA,CAAKd,GAAM,QAAO;AAElB,QAAM4J,KAAuB,UAAb9I,MAAmC,aAAbA,IAChCZ,KAA6B,UAAbY,MAAmC,WAAbA,KAAsB,WAAW,SACvEb,KAAQ2J,KAAUH,KAASC,IAC3BrG,IAASD,GAAapD,GAAKqD,QAAQpD,EAAAA;AAEzCqK,MAAmBC,IAAGvH,EAAAA,CAAAA,GACfhD,IAAI,EACPA,MAAM4J,KAAU,MAAM,KACtB5H,GAAgB,YAAblB,KAAuB8C,KAAQ,GAClC3B,GAAgB,aAAbnB,KAAwB+C,KAAS,GACpC5D,OAAAA,IACAoD,QAAAA,GACAR,QAAQ+G,KAAUhG,KAAQC,IAC1B3D,eAAAA,IACA8E,OAAAA,EAAAA,CAAAA,CAAAA;EAER,CAAA;AACJ;AT5MA,IS8MagH,IAA0B,SACnCzB,IAA6B0B,IAAAA;AAc5B,MAZGrI,KAAKqI,GAALrI,OACAC,KAAMoI,GAANpI,QACA5D,KAAKgM,GAALhM,OACAD,KAAIiM,GAAJjM,MACAU,IAAMuL,GAANvL;AASUgD,IAAiB,EAAEE,OAAAA,IAAOC,QAAAA,IAAQ5D,OAAAA,IAAOD,MAAAA,IAAMU,QAAAA,EAAAA,CAAAA,EAEvD8K,QAAQ,SAAAxK,IAAAA;AACVuJ,IAAAA,GAAIY,UAAAA,GACJZ,GAAIa,OAAOpK,GAAKoD,IAAIpD,GAAKsD,EAAAA,GACzBiG,GAAIc,OAAOrK,GAAKqD,IAAIrD,GAAKuD,EAAAA,GACzBgG,GAAIU,OAAAA;EACR,CAAA;AACJ;", "names": ["precisionCutOffs", "date", "setMilliseconds", "setSeconds", "setMinutes", "setHours", "setDate", "setMonth", "precisionCutOffsByType", "millisecond", "second", "slice", "minute", "hour", "day", "month", "year", "createPrecisionMethod", "precision", "for<PERSON>ach", "cutOff", "createDateNormalizer", "_ref", "_ref$format", "format", "_ref$precision", "_ref$useUTC", "useUTC", "precisionFn", "value", "Date", "parseTime", "utcParse", "timeParse", "linearScaleDefaults", "type", "min", "max", "stacked", "reverse", "clamp", "nice", "round", "createLinearScale", "data", "size", "axis", "minValue", "_data$minStacked", "maxValue", "_data$maxStacked", "_ref$min", "_ref$max", "_ref$stacked", "_ref$reverse", "_ref$clamp", "_ref$nice", "_ref$round", "minStacked", "maxStacked", "scale", "scaleLinear", "range", "interpolate", "interpolateRound", "interpolateNumber", "domain", "castLinearScale", "typedScale", "createPointScale", "_spec", "scalePoint", "all", "bandScaleDefaults", "type", "round", "createBandScale", "_ref", "data", "size", "axis", "_ref$round", "scale", "scaleBand", "range", "domain", "all", "castBandScale", "typedScale", "timeScaleDefaults", "format", "precision", "min", "max", "useUTC", "nice", "createTimeScale", "minValue", "maxValue", "_ref$format", "_ref$precision", "_ref$min", "_ref$max", "_ref$useUTC", "_ref$nice", "normalize", "createDateNormalizer", "scaleUtc", "scaleTime", "logScaleDefaults", "base", "reverse", "createLogScale", "sign", "_ref$base", "_ref$reverse", "some", "v", "Error", "hasMixedSign", "filter", "for<PERSON>ach", "Math", "scaleLog", "rangeRound", "symlogScaleDefaults", "constant", "createSymlogScale", "_ref$constant", "scaleSymlog", "getOtherAxis", "compareValues", "a", "b", "compareDateValues", "getTime", "computeScale", "spec", "createLinearScale", "createPointScale", "getDatumAxisPosition", "datum", "_scale", "stacked", "stackedValue", "computeXYScalesForSeries", "series", "xScaleSpec", "yScaleSpec", "width", "height", "nestedSeries", "map", "serie", "_extends", "d", "xy", "generateSeriesXY", "stackX", "stackY", "xScale", "x", "yScale", "y", "computedSeries", "position", "generateSeriesAxis", "scaleSpec", "_temp", "_ref$getValue", "getValue", "_ref$setValue", "setValue", "value", "parseFloat", "String", "parseTime", "values", "push", "sortBy", "uniq", "apply", "uniqBy", "slice", "sort", "last", "stackAxis", "otherAxis", "compare", "isDate", "stack", "find", "stackValue", "head", "minStacked", "maxStacked", "centerScale", "bandwidth", "offset", "timeByType", "millisecond", "timeMillisecond", "utcMillisecond", "second", "timeSecond", "utcSecond", "minute", "timeMinute", "utcMinute", "hour", "timeHour", "utcHour", "day", "timeInterval", "date", "setHours", "step", "setDate", "getDate", "start", "end", "floor", "setUTCHours", "setUTCDate", "getUTCDate", "week", "timeWeek", "utcWeek", "sunday", "timeSunday", "utcSunday", "monday", "timeMonday", "utcMonday", "tuesday", "timeTuesday", "utcTuesday", "wednesday", "timeWednesday", "utcWednesday", "thursday", "timeThursday", "utcThursday", "friday", "timeFriday", "utcFriday", "saturday", "timeSaturday", "utcSaturday", "month", "timeMonth", "utcMonth", "year", "timeYear", "utcYear", "timeTypes", "Object", "keys", "timeIntervalRegexp", "RegExp", "join", "getScaleTicks", "Array", "isArray", "matches", "match", "amount", "timeType", "_timeType$every$range", "_timeType$every", "_scale$domain", "originalStop", "stop", "Date", "every", "Number", "ticks", "interval", "isFinite", "n", "i", "z", "z2", "t", "computeCartesianTicks", "_ref", "translate", "axis", "scale", "ticksPosition", "tickValues", "tickSize", "tickPadding", "tickRotation", "truncateTickAt", "_ref$engine", "engine", "values", "getScaleTicks", "textProps", "textPropsByEngine", "position", "centerScale", "line", "lineX", "lineY", "text", "textX", "textY", "isRTL", "document", "dir", "textAlign", "align", "center", "textBaseline", "baseline", "d", "_position", "x", "y", "top", "bottom", "_position2", "left", "right", "ticks", "map", "value", "processedValue", "valueLength", "String", "length", "slice", "concat", "_extends", "key", "Date", "valueOf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "format", "type", "formatter", "timeFormat", "d3Format", "computeGridLines", "_ref2", "width", "height", "_values", "Array", "isArray", "lines", "_position3", "_position4", "x1", "x2", "y1", "y2", "_position5", "_position6", "memoizedAxisTick", "memo", "_format", "_value", "onClick", "textAnchor", "theme", "animatedProps", "props", "useMemo", "style", "opacity", "cursor", "event", "_jsxs", "animated", "g", "transform", "children", "_jsx", "Text", "dominantBaseline", "textTransform", "defaultAxisProps", "legendPosition", "legendOffset", "NonMemoizedAxis", "_ref$x", "_ref$y", "_ref$tickSize", "_ref$tickPadding", "_ref$tickRotation", "_ref$renderTick", "renderTick", "AxisTick", "legend", "_ref$legendPosition", "_ref$legendOffset", "ariaHidden", "useTheme", "axisTheme", "useExtendedAxisTheme", "formatValue", "_computeCartesianTick", "legendNode", "legendX", "legendY", "legendRotation", "_Fragment", "_useMotionConfig", "useMotionConfig", "animate", "springConfig", "config", "useSpring", "lineX2", "lineY2", "immediate", "getAnimatedProps", "useCallback", "tick", "getFromAnimatedProps", "transition", "useTransition", "keys", "initial", "from", "enter", "update", "leave", "transitionProps", "_state", "tickIndex", "createElement", "rotate", "domain", "Axis", "positions", "Axes", "xScale", "yScale", "axes", "isXAxis", "GridLine", "grid", "GridLines", "_createElement", "Grid", "xValues", "yV<PERSON><PERSON>", "xLines", "yLines", "renderAxisToCanvas", "ctx", "_axisTheme$domain$lin", "_axisTheme$ticks$line", "save", "extendAxisTheme", "setCanvasFont", "domainLineWidth", "strokeWidth", "lineWidth", "lineCap", "stroke", "strokeStyle", "beginPath", "moveTo", "lineTo", "tickLine<PERSON>idth", "shouldRenderTickLine", "for<PERSON>ach", "degreesToRadians", "drawCanvasText", "fillText", "restore", "fill", "fillStyle", "renderAxesToCanvas", "renderGridLinesToCanvas", "_ref3"]}