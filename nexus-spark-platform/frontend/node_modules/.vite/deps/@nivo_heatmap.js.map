{"version": 3, "sources": ["../../lodash/_baseFilter.js", "../../lodash/filter.js", "../../@nivo/annotations/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs", "../../@nivo/annotations/src/props.ts", "../../@nivo/annotations/src/utils.ts", "../../@nivo/annotations/src/compute.ts", "../../@nivo/annotations/src/hooks.ts", "../../@nivo/annotations/src/AnnotationNote.tsx", "../../@nivo/annotations/src/AnnotationLink.tsx", "../../@nivo/annotations/src/CircleAnnotationOutline.tsx", "../../@nivo/annotations/src/DotAnnotationOutline.tsx", "../../@nivo/annotations/src/RectAnnotationOutline.tsx", "../../@nivo/annotations/src/Annotation.tsx", "../../@nivo/annotations/src/canvas.ts", "../../@nivo/heatmap/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs", "../../@nivo/heatmap/src/HeatMapTooltip.tsx", "../../@nivo/heatmap/src/defaults.ts", "../../@nivo/heatmap/src/compute.ts", "../../@nivo/heatmap/src/hooks.ts", "../../@nivo/heatmap/src/HeatMapCellRect.tsx", "../../@nivo/heatmap/src/HeatMapCellCircle.tsx", "../../@nivo/heatmap/src/HeatMapCells.tsx", "../../@nivo/heatmap/src/HeatMapCellAnnotations.tsx", "../../@nivo/heatmap/src/HeatMap.tsx", "../../@nivo/heatmap/src/ResponsiveHeatMap.tsx", "../../@nivo/heatmap/src/canvas.tsx", "../../@nivo/heatmap/src/HeatMapCanvas.tsx", "../../@nivo/heatmap/src/ResponsiveHeatMapCanvas.tsx"], "sourcesContent": ["var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function(value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\n\nmodule.exports = baseFilter;\n", "var arrayFilter = require('./_arrayFilter'),\n    baseFilter = require('./_baseFilter'),\n    baseIteratee = require('./_baseIteratee'),\n    isArray = require('./isArray');\n\n/**\n * Iterates over elements of `collection`, returning an array of all elements\n * `predicate` returns truthy for. The predicate is invoked with three\n * arguments: (value, index|key, collection).\n *\n * **Note:** Unlike `_.remove`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n * @see _.reject\n * @example\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': true },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * _.filter(users, function(o) { return !o.active; });\n * // => objects for ['fred']\n *\n * // The `_.matches` iteratee shorthand.\n * _.filter(users, { 'age': 36, 'active': true });\n * // => objects for ['barney']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.filter(users, ['active', false]);\n * // => objects for ['fred']\n *\n * // The `_.property` iteratee shorthand.\n * _.filter(users, 'active');\n * // => objects for ['barney']\n *\n * // Combining several predicates using `_.overEvery` or `_.overSome`.\n * _.filter(users, _.overSome([{ 'age': 36 }, ['age', 40]]));\n * // => objects for ['fred', 'barney']\n */\nfunction filter(collection, predicate) {\n  var func = isArray(collection) ? arrayFilter : baseFilter;\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nmodule.exports = filter;\n", "// src/index.ts\nimport { Globals } from \"@react-spring/core\";\nimport { unstable_batchedUpdates } from \"react-dom\";\nimport { createStringInterpolator, colors } from \"@react-spring/shared\";\nimport { createHost } from \"@react-spring/animated\";\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\") return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\nimport { AnimatedObject } from \"@react-spring/animated\";\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver\n} from \"@react-spring/shared\";\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    eachProp(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (is.und(value)) return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push(toArray(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0]);\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    callFluidObservers(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\nexport * from \"@react-spring/core\";\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors\n});\nvar host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\nexport {\n  animated as a,\n  animated\n};\n", "export const defaultProps = {\n    dotSize: 4,\n    noteWidth: 120,\n    noteTextOffset: 8,\n    animate: true,\n}\n", "import {\n    AnnotationSpec,\n    CircleAnnotationSpec,\n    DotAnnotationSpec,\n    Note,\n    NoteCanvas,\n    NoteSvg,\n    RectAnnotationSpec,\n} from './types'\nimport { isValidElement } from 'react'\n\nexport const isSvgNote = <Datum>(note: Note<Datum>): note is NoteSvg<Datum> => {\n    const noteType = typeof note\n\n    return (\n        isValidElement(note) ||\n        noteType === 'string' ||\n        noteType === 'function' ||\n        noteType === 'object'\n    )\n}\n\nexport const isCanvasNote = <Datum>(note: Note<Datum>): note is NoteCanvas<Datum> => {\n    const noteType = typeof note\n\n    return noteType === 'string' || noteType === 'function'\n}\n\nexport const isCircleAnnotation = <Datum>(\n    annotationSpec: AnnotationSpec<Datum>\n): annotationSpec is CircleAnnotationSpec<Datum> => annotationSpec.type === 'circle'\n\nexport const isDotAnnotation = <Datum>(\n    annotationSpec: AnnotationSpec<Datum>\n): annotationSpec is DotAnnotationSpec<Datum> => annotationSpec.type === 'dot'\n\nexport const isRectAnnotation = <Datum>(\n    annotationSpec: AnnotationSpec<Datum>\n): annotationSpec is RectAnnotationSpec<Datum> => annotationSpec.type === 'rect'\n", "import filter from 'lodash/filter.js'\nimport isNumber from 'lodash/isNumber.js'\nimport omit from 'lodash/omit.js'\nimport {\n    radiansToDegrees,\n    normalizeAngleDegrees,\n    degreesToRadians,\n    positionFromAngle,\n} from '@nivo/core'\nimport { defaultProps } from './props'\nimport {\n    AnnotationPositionGetter,\n    AnnotationDimensionsGetter,\n    BoundAnnotation,\n    AnnotationMatcher,\n    AnnotationInstructions,\n} from './types'\nimport { isCircleAnnotation, isRectAnnotation } from './utils'\n\nexport const bindAnnotations = <\n    Datum = {\n        x: number\n        y: number\n    },\n>({\n    data,\n    annotations,\n    getPosition,\n    getDimensions,\n}: {\n    data: readonly Datum[]\n    annotations: readonly AnnotationMatcher<Datum>[]\n    getPosition: AnnotationPositionGetter<Datum>\n    getDimensions: AnnotationDimensionsGetter<Datum>\n}): BoundAnnotation<Datum>[] =>\n    annotations.reduce((acc: BoundAnnotation<Datum>[], annotation) => {\n        const offset = annotation.offset || 0\n\n        return [\n            ...acc,\n            ...filter<Datum>(data, annotation.match).map(datum => {\n                const position = getPosition(datum)\n                const dimensions = getDimensions(datum)\n\n                if (isCircleAnnotation(annotation) || isRectAnnotation(annotation)) {\n                    dimensions.size = dimensions.size + offset * 2\n                    dimensions.width = dimensions.width + offset * 2\n                    dimensions.height = dimensions.height + offset * 2\n                }\n\n                // acc.push({\n                //     ...omit(annotation, ['match', 'offset']),\n                //     ...position,\n                //     ...dimensions,\n                //     size: annotation.size || dimensions.size,\n                //     datum,\n                // } as any)\n                // return [\n                //     ...acc,\n                //     {\n                //         ...omit(annotation, ['match', 'offset']),\n                //         ...position,\n                //         ...dimensions,\n                //         size: annotation.size || dimensions.size,\n                //         datum,\n                //     },\n                // ]\n                return {\n                    ...omit(annotation, ['match', 'offset']),\n                    ...position,\n                    ...dimensions,\n                    size: annotation.size || dimensions.size,\n                    datum,\n                } as Required<BoundAnnotation<Datum>>\n            }),\n        ]\n\n        // return acc\n    }, [])\n\nexport const getLinkAngle = (\n    sourceX: number,\n    sourceY: number,\n    targetX: number,\n    targetY: number\n) => {\n    const angle = Math.atan2(targetY - sourceY, targetX - sourceX)\n\n    return normalizeAngleDegrees(radiansToDegrees(angle))\n}\n\nexport const computeAnnotation = <Datum>(\n    annotation: BoundAnnotation<Datum>\n): AnnotationInstructions => {\n    const {\n        x,\n        y,\n        noteX,\n        noteY,\n        noteWidth = defaultProps.noteWidth,\n        noteTextOffset = defaultProps.noteTextOffset,\n    } = annotation\n\n    let computedNoteX: number\n    let computedNoteY: number\n\n    if (isNumber(noteX)) {\n        computedNoteX = x + noteX\n    } else if (noteX.abs !== undefined) {\n        computedNoteX = noteX.abs\n    } else {\n        throw new Error(`noteX should be either a number or an object containing an 'abs' property`)\n    }\n\n    if (isNumber(noteY)) {\n        computedNoteY = y + noteY\n    } else if (noteY.abs !== undefined) {\n        computedNoteY = noteY.abs\n    } else {\n        throw new Error(`noteY should be either a number or an object containing an 'abs' property`)\n    }\n\n    let computedX = x\n    let computedY = y\n\n    const angle = getLinkAngle(x, y, computedNoteX, computedNoteY)\n\n    if (isCircleAnnotation<Datum>(annotation)) {\n        const position = positionFromAngle(degreesToRadians(angle), annotation.size / 2)\n        computedX += position.x\n        computedY += position.y\n    }\n\n    if (isRectAnnotation<Datum>(annotation)) {\n        const eighth = Math.round((angle + 90) / 45) % 8\n        if (eighth === 0) {\n            computedY -= annotation.height / 2\n        }\n        if (eighth === 1) {\n            computedX += annotation.width / 2\n            computedY -= annotation.height / 2\n        }\n        if (eighth === 2) {\n            computedX += annotation.width / 2\n        }\n        if (eighth === 3) {\n            computedX += annotation.width / 2\n            computedY += annotation.height / 2\n        }\n        if (eighth === 4) {\n            computedY += annotation.height / 2\n        }\n        if (eighth === 5) {\n            computedX -= annotation.width / 2\n            computedY += annotation.height / 2\n        }\n        if (eighth === 6) {\n            computedX -= annotation.width / 2\n        }\n        if (eighth === 7) {\n            computedX -= annotation.width / 2\n            computedY -= annotation.height / 2\n        }\n    }\n\n    let textX = computedNoteX\n    const textY = computedNoteY - noteTextOffset\n\n    let noteLineX = computedNoteX\n    const noteLineY = computedNoteY\n\n    if ((angle + 90) % 360 > 180) {\n        textX -= noteWidth\n        noteLineX -= noteWidth\n    } else {\n        noteLineX += noteWidth\n    }\n\n    return {\n        points: [\n            [computedX, computedY],\n            [computedNoteX, computedNoteY],\n            [noteLineX, noteLineY],\n        ] as [number, number][],\n        text: [textX, textY],\n        angle: angle + 90,\n    }\n}\n", "import { useMemo } from 'react'\nimport { bindAnnotations, computeAnnotation } from './compute'\nimport {\n    AnnotationDimensionsGetter,\n    AnnotationMatcher,\n    AnnotationPositionGetter,\n    BoundAnnotation,\n} from './types'\n\n/**\n * Bind annotations to a dataset.\n */\nexport const useAnnotations = <Datum>({\n    data,\n    annotations,\n    getPosition,\n    getDimensions,\n}: {\n    data: readonly Datum[]\n    annotations: readonly AnnotationMatcher<Datum>[]\n    getPosition: AnnotationPositionGetter<Datum>\n    getDimensions: AnnotationDimensionsGetter<Datum>\n}) =>\n    useMemo(\n        () =>\n            bindAnnotations<Datum>({\n                data,\n                annotations,\n                getPosition,\n                getDimensions,\n            }),\n        [data, annotations, getPosition, getDimensions]\n    )\n\nexport const useComputedAnnotations = <Datum>({\n    annotations,\n}: {\n    annotations: readonly BoundAnnotation<Datum>[]\n}) =>\n    useMemo(\n        () =>\n            annotations.map(annotation => ({\n                ...annotation,\n                computed: computeAnnotation<Datum>({\n                    ...annotation,\n                }),\n            })),\n        [annotations]\n    )\n\nexport const useComputedAnnotation = <Datum>(annotation: BoundAnnotation<Datum>) =>\n    useMemo(() => computeAnnotation<Datum>(annotation), [annotation])\n", "import { createElement } from 'react'\nimport omit from 'lodash/omit.js'\nimport { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { NoteSvg } from './types'\n\nexport const AnnotationNote = <Datum,>({\n    datum,\n    x,\n    y,\n    note,\n}: {\n    datum: Datum\n    x: number\n    y: number\n    note: NoteSvg<Datum>\n}) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x,\n        y,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    if (typeof note === 'function') {\n        return createElement(note, { x, y, datum })\n    }\n\n    return (\n        <>\n            {theme.annotations.text.outlineWidth > 0 && (\n                <animated.text\n                    x={animatedProps.x}\n                    y={animatedProps.y}\n                    style={{\n                        ...theme.annotations.text,\n                        strokeLinejoin: 'round',\n                        strokeWidth: theme.annotations.text.outlineWidth * 2,\n                        stroke: theme.annotations.text.outlineColor,\n                    }}\n                >\n                    {note}\n                </animated.text>\n            )}\n            <animated.text\n                x={animatedProps.x}\n                y={animatedProps.y}\n                style={omit(theme.annotations.text, ['outlineWidth', 'outlineColor'])}\n            >\n                {note}\n            </animated.text>\n        </>\n    )\n}\n", "import { useMemo } from 'react'\nimport { animated } from '@react-spring/web'\nimport { useAnimatedPath } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\n\nexport const AnnotationLink = ({\n    points,\n    isOutline = false,\n}: {\n    points: [number, number][]\n    isOutline?: boolean\n}) => {\n    const theme = useTheme()\n\n    const path = useMemo(() => {\n        const [firstPoint, ...otherPoints] = points\n\n        return otherPoints.reduce(\n            (acc, [x, y]) => `${acc} L${x},${y}`,\n            `M${firstPoint[0]},${firstPoint[1]}`\n        )\n    }, [points])\n\n    const animatedPath = useAnimatedPath(path)\n\n    if (isOutline && theme.annotations.link.outlineWidth <= 0) {\n        return null\n    }\n\n    const style = { ...theme.annotations.link }\n    if (isOutline) {\n        style.strokeLinecap = 'square'\n        style.strokeWidth =\n            theme.annotations.link.strokeWidth + theme.annotations.link.outlineWidth * 2\n        style.stroke = theme.annotations.link.outlineColor\n        style.opacity = theme.annotations.link.outlineOpacity\n    }\n\n    return <animated.path fill=\"none\" d={animatedPath} style={style} />\n}\n", "import { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\n\nexport const CircleAnnotationOutline = ({ x, y, size }: { x: number; y: number; size: number }) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x,\n        y,\n        radius: size / 2,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <>\n            {theme.annotations.outline.outlineWidth > 0 && (\n                <animated.circle\n                    cx={animatedProps.x}\n                    cy={animatedProps.y}\n                    r={animatedProps.radius}\n                    style={{\n                        ...theme.annotations.outline,\n                        fill: 'none',\n                        strokeWidth:\n                            theme.annotations.outline.strokeWidth +\n                            theme.annotations.outline.outlineWidth * 2,\n                        stroke: theme.annotations.outline.outlineColor,\n                        opacity: theme.annotations.outline.outlineOpacity,\n                    }}\n                />\n            )}\n            <animated.circle\n                cx={animatedProps.x}\n                cy={animatedProps.y}\n                r={animatedProps.radius}\n                style={theme.annotations.outline}\n            />\n        </>\n    )\n}\n", "import { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { defaultProps } from './props'\n\nexport const DotAnnotationOutline = ({\n    x,\n    y,\n    size = defaultProps.dotSize,\n}: {\n    x: number\n    y: number\n    size?: number\n}) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x,\n        y,\n        radius: size / 2,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <>\n            {theme.annotations.outline.outlineWidth > 0 && (\n                <animated.circle\n                    cx={animatedProps.x}\n                    cy={animatedProps.y}\n                    r={animatedProps.radius}\n                    style={{\n                        ...theme.annotations.outline,\n                        fill: 'none',\n                        strokeWidth: theme.annotations.outline.outlineWidth * 2,\n                        stroke: theme.annotations.outline.outlineColor,\n                        opacity: theme.annotations.outline.outlineOpacity,\n                    }}\n                />\n            )}\n            <animated.circle\n                cx={animatedProps.x}\n                cy={animatedProps.y}\n                r={animatedProps.radius}\n                style={theme.annotations.symbol}\n            />\n        </>\n    )\n}\n", "import { useSpring, animated } from '@react-spring/web'\nimport { useMotionConfig } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\n\nexport const RectAnnotationOutline = ({\n    x,\n    y,\n    width,\n    height,\n    borderRadius = 6,\n}: {\n    x: number\n    y: number\n    width: number\n    height: number\n    borderRadius?: number\n}) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        x: x - width / 2,\n        y: y - height / 2,\n        width,\n        height,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <>\n            {theme.annotations.outline.outlineWidth > 0 && (\n                <animated.rect\n                    x={animatedProps.x}\n                    y={animatedProps.y}\n                    rx={borderRadius}\n                    ry={borderRadius}\n                    width={animatedProps.width}\n                    height={animatedProps.height}\n                    style={{\n                        ...theme.annotations.outline,\n                        fill: 'none',\n                        strokeWidth:\n                            theme.annotations.outline.strokeWidth +\n                            theme.annotations.outline.outlineWidth * 2,\n                        stroke: theme.annotations.outline.outlineColor,\n                        opacity: theme.annotations.outline.outlineOpacity,\n                    }}\n                />\n            )}\n            <animated.rect\n                x={animatedProps.x}\n                y={animatedProps.y}\n                rx={borderRadius}\n                ry={borderRadius}\n                width={animatedProps.width}\n                height={animatedProps.height}\n                style={theme.annotations.outline}\n            />\n        </>\n    )\n}\n", "import { useComputedAnnotation } from './hooks'\nimport { AnnotationNote } from './AnnotationNote'\nimport { AnnotationLink } from './AnnotationLink'\nimport { CircleAnnotationOutline } from './CircleAnnotationOutline'\nimport { DotAnnotationOutline } from './DotAnnotationOutline'\nimport { RectAnnotationOutline } from './RectAnnotationOutline'\nimport { BoundAnnotation } from './types'\nimport { isCircleAnnotation, isDotAnnotation, isRectAnnotation, isSvgNote } from './utils'\n\nexport const Annotation = <Datum,>(annotation: BoundAnnotation<Datum>) => {\n    const { datum, x, y, note } = annotation\n    const computed = useComputedAnnotation(annotation)\n\n    if (!isSvgNote(note)) {\n        throw new Error('note should be a valid react element')\n    }\n\n    return (\n        <>\n            <AnnotationLink points={computed.points} isOutline={true} />\n            {isCircleAnnotation(annotation) && (\n                <CircleAnnotationOutline x={x} y={y} size={annotation.size} />\n            )}\n            {isDotAnnotation(annotation) && (\n                <DotAnnotationOutline x={x} y={y} size={annotation.size} />\n            )}\n            {isRectAnnotation(annotation) && (\n                <RectAnnotationOutline\n                    x={x}\n                    y={y}\n                    width={annotation.width}\n                    height={annotation.height}\n                    borderRadius={annotation.borderRadius}\n                />\n            )}\n            <AnnotationLink points={computed.points} />\n            <AnnotationNote datum={datum} x={computed.text[0]} y={computed.text[1]} note={note} />\n        </>\n    )\n}\n", "import { Theme } from '@nivo/theming'\nimport { ComputedAnnotation } from './types'\nimport { isCanvasNote, isCircleAnnotation, isDotAnnotation, isRectAnnotation } from './utils'\n\nconst drawPoints = (ctx: CanvasRenderingContext2D, points: [number, number][]) => {\n    points.forEach(([x, y], index) => {\n        if (index === 0) {\n            ctx.moveTo(x, y)\n        } else {\n            ctx.lineTo(x, y)\n        }\n    })\n}\n\nexport const renderAnnotationsToCanvas = <Datum>(\n    ctx: CanvasRenderingContext2D,\n    {\n        annotations,\n        theme,\n    }: {\n        annotations: ComputedAnnotation<Datum>[]\n        theme: Theme\n    }\n) => {\n    if (annotations.length === 0) return\n\n    ctx.save()\n    annotations.forEach(annotation => {\n        if (!isCanvasNote(annotation.note)) {\n            throw new Error('note is invalid for canvas implementation')\n        }\n\n        if (theme.annotations.link.outlineWidth > 0) {\n            ctx.lineCap = 'square'\n            ctx.strokeStyle = theme.annotations.link.outlineColor\n            ctx.lineWidth =\n                theme.annotations.link.strokeWidth + theme.annotations.link.outlineWidth * 2\n            ctx.beginPath()\n            drawPoints(ctx, annotation.computed.points)\n            ctx.stroke()\n            ctx.lineCap = 'butt'\n        }\n\n        if (isCircleAnnotation(annotation) && theme.annotations.outline.outlineWidth > 0) {\n            ctx.strokeStyle = theme.annotations.outline.outlineColor\n            ctx.lineWidth =\n                theme.annotations.outline.strokeWidth + theme.annotations.outline.outlineWidth * 2\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.stroke()\n        }\n\n        if (isDotAnnotation(annotation) && theme.annotations.symbol.outlineWidth > 0) {\n            ctx.strokeStyle = theme.annotations.symbol.outlineColor\n            ctx.lineWidth = theme.annotations.symbol.outlineWidth * 2\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.stroke()\n        }\n\n        if (isRectAnnotation(annotation) && theme.annotations.outline.outlineWidth > 0) {\n            ctx.strokeStyle = theme.annotations.outline.outlineColor\n            ctx.lineWidth =\n                theme.annotations.outline.strokeWidth + theme.annotations.outline.outlineWidth * 2\n            ctx.beginPath()\n            ctx.rect(\n                annotation.x - annotation.width / 2,\n                annotation.y - annotation.height / 2,\n                annotation.width,\n                annotation.height\n            )\n            ctx.stroke()\n        }\n\n        ctx.strokeStyle = theme.annotations.link.stroke\n        ctx.lineWidth = theme.annotations.link.strokeWidth\n        ctx.beginPath()\n        drawPoints(ctx, annotation.computed.points)\n        ctx.stroke()\n\n        if (isCircleAnnotation(annotation)) {\n            ctx.strokeStyle = theme.annotations.outline.stroke\n            ctx.lineWidth = theme.annotations.outline.strokeWidth\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.stroke()\n        }\n\n        if (isDotAnnotation(annotation)) {\n            ctx.fillStyle = theme.annotations.symbol.fill\n            ctx.beginPath()\n            ctx.arc(annotation.x, annotation.y, annotation.size / 2, 0, 2 * Math.PI)\n            ctx.fill()\n        }\n\n        if (isRectAnnotation(annotation)) {\n            ctx.strokeStyle = theme.annotations.outline.stroke\n            ctx.lineWidth = theme.annotations.outline.strokeWidth\n            ctx.beginPath()\n            ctx.rect(\n                annotation.x - annotation.width / 2,\n                annotation.y - annotation.height / 2,\n                annotation.width,\n                annotation.height\n            )\n            ctx.stroke()\n        }\n\n        if (typeof annotation.note === 'function') {\n            annotation.note(ctx, {\n                datum: annotation.datum,\n                x: annotation.computed.text[0],\n                y: annotation.computed.text[1],\n                theme,\n            })\n        } else {\n            ctx.font = `${theme.annotations.text.fontSize}px ${theme.annotations.text.fontFamily}`\n            ctx.textAlign = 'left'\n            ctx.textBaseline = 'alphabetic'\n\n            ctx.fillStyle = theme.annotations.text.fill\n            ctx.strokeStyle = theme.annotations.text.outlineColor\n            ctx.lineWidth = theme.annotations.text.outlineWidth * 2\n\n            if (theme.annotations.text.outlineWidth > 0) {\n                ctx.lineJoin = 'round'\n                ctx.strokeText(\n                    annotation.note,\n                    annotation.computed.text[0],\n                    annotation.computed.text[1]\n                )\n                ctx.lineJoin = 'miter'\n            }\n            ctx.fillText(annotation.note, annotation.computed.text[0], annotation.computed.text[1])\n        }\n    })\n    ctx.restore()\n}\n", "// src/index.ts\nimport { Globals } from \"@react-spring/core\";\nimport { unstable_batchedUpdates } from \"react-dom\";\nimport { createStringInterpolator, colors } from \"@react-spring/shared\";\nimport { createHost } from \"@react-spring/animated\";\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\") return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\nimport { AnimatedObject } from \"@react-spring/animated\";\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver\n} from \"@react-spring/shared\";\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    eachProp(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (is.und(value)) return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push(toArray(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0]);\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    callFluidObservers(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\nexport * from \"@react-spring/core\";\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors\n});\nvar host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\nexport {\n  animated as a,\n  animated\n};\n", "import { memo } from 'react'\nimport { BasicTooltip } from '@nivo/tooltip'\nimport { HeatMapDatum, TooltipProps } from './types'\n\nconst NonMemoizedHeatMapTooltip = <Datum extends HeatMapDatum>({ cell }: TooltipProps<Datum>) => {\n    if (cell.formattedValue === null) return null\n\n    return (\n        <BasicTooltip\n            id={`${cell.serieId} - ${cell.data.x}`}\n            value={cell.formattedValue}\n            enableChip={true}\n            color={cell.color}\n        />\n    )\n}\n\nexport const HeatMapTooltip = memo(NonMemoizedHeatMapTooltip) as typeof NonMemoizedHeatMapTooltip\n", "import { DefaultHeatMapDatum, HeatMapCommonProps, LayerId } from './types'\nimport { HeatMapTooltip } from './HeatMapTooltip'\n\nexport const commonDefaultProps: Omit<\n    HeatMapCommonProps<DefaultHeatMapDatum>,\n    | 'margin'\n    | 'theme'\n    | 'valueFormat'\n    | 'onClick'\n    | 'renderWrapper'\n    | 'role'\n    | 'ariaLabel'\n    | 'ariaLabelledBy'\n    | 'ariaDescribedBy'\n> & {\n    layers: LayerId[]\n} = {\n    layers: ['grid', 'axes', 'cells', 'legends', 'annotations'],\n\n    forceSquare: false,\n    xInnerPadding: 0,\n    xOuterPadding: 0,\n    yInnerPadding: 0,\n    yOuterPadding: 0,\n    sizeVariation: false,\n\n    opacity: 1,\n    activeOpacity: 1,\n    inactiveOpacity: 0.15,\n    borderWidth: 0,\n    borderColor: { from: 'color', modifiers: [['darker', 0.8]] },\n\n    enableGridX: false,\n    enableGridY: false,\n\n    enableLabels: true,\n    label: 'formattedValue',\n    labelTextColor: { from: 'color', modifiers: [['darker', 2]] },\n\n    colors: {\n        type: 'sequential',\n        scheme: 'brown_blueGreen',\n    },\n    emptyColor: '#000000',\n\n    legends: [],\n    annotations: [],\n\n    isInteractive: true,\n    hoverTarget: 'rowColumn',\n    tooltip: HeatMapTooltip,\n\n    animate: true,\n    motionConfig: 'gentle' as const,\n}\n\nexport const svgDefaultProps = {\n    ...commonDefaultProps,\n    axisTop: {},\n    axisRight: null,\n    axisBottom: null,\n    axisLeft: {},\n    borderRadius: 0,\n    cellComponent: 'rect' as const,\n}\n\nexport const canvasDefaultProps = {\n    ...commonDefaultProps,\n    axisTop: {},\n    axisRight: null,\n    axisBottom: null,\n    axisLeft: {},\n    renderCell: 'rect' as const,\n    pixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1,\n}\n", "import { scaleBand, scaleLinear } from 'd3-scale'\nimport { castBandScale } from '@nivo/scales'\nimport {\n    ComputedCell,\n    HeatMapCommonProps,\n    HeatMapDataProps,\n    HeatMapDatum,\n    SizeVariationConfig,\n} from './types'\n\nexport const computeLayout = ({\n    width: _width,\n    height: _height,\n    rows,\n    columns,\n    forceSquare,\n}: {\n    width: number\n    height: number\n    rows: number\n    columns: number\n    forceSquare: boolean\n}) => {\n    let width = _width\n    let height = _height\n\n    let offsetX = 0\n    let offsetY = 0\n\n    if (forceSquare) {\n        const cellWidth = Math.max(_width / columns, 0)\n        const cellHeight = Math.max(_height / rows, 0)\n        const cellSize = Math.min(cellWidth, cellHeight)\n\n        width = cellSize * columns\n        height = cellSize * rows\n\n        offsetX = (_width - width) / 2\n        offsetY = (_height - height) / 2\n    }\n\n    return {\n        offsetX,\n        offsetY,\n        width,\n        height,\n    }\n}\n\nexport const computeCells = <Datum extends HeatMapDatum, ExtraProps extends object>({\n    data,\n    width: _width,\n    height: _height,\n    xInnerPadding,\n    xOuterPadding,\n    yInnerPadding,\n    yOuterPadding,\n    forceSquare,\n}: {\n    data: HeatMapDataProps<Datum, ExtraProps>['data']\n    width: number\n    height: number\n} & Pick<\n    HeatMapCommonProps<Datum>,\n    'xOuterPadding' | 'xInnerPadding' | 'yOuterPadding' | 'yInnerPadding' | 'forceSquare'\n>) => {\n    const xValuesSet = new Set<Datum['x']>()\n    const serieIds: string[] = []\n    const allValues: number[] = []\n\n    const cells: Pick<ComputedCell<Datum>, 'id' | 'serieId' | 'value' | 'data'>[] = []\n\n    data.forEach(serie => {\n        serieIds.push(serie.id)\n\n        serie.data.forEach(datum => {\n            xValuesSet.add(datum.x)\n\n            let value: number | null = null\n            if (datum.y !== undefined && datum.y !== null) {\n                allValues.push(datum.y)\n                value = datum.y\n            }\n\n            cells.push({\n                id: `${serie.id}.${datum.x}`,\n                serieId: serie.id,\n                value,\n                data: datum,\n            })\n        })\n    })\n\n    const xValues = Array.from(xValuesSet)\n\n    const { width, height, offsetX, offsetY } = computeLayout({\n        width: _width,\n        height: _height,\n        columns: xValues.length,\n        rows: serieIds.length,\n        forceSquare,\n    })\n\n    const xScale = castBandScale<Datum['x']>(\n        scaleBand<Datum['x']>()\n            .domain(xValues)\n            .range([0, width])\n            .paddingOuter(xOuterPadding)\n            .paddingInner(xInnerPadding)\n    )\n\n    const yScale = castBandScale<string>(\n        scaleBand<string>()\n            .domain(serieIds)\n            .range([0, height])\n            .paddingOuter(yOuterPadding)\n            .paddingInner(yInnerPadding)\n    )\n\n    const cellWidth = xScale.bandwidth()\n    const cellHeight = yScale.bandwidth()\n\n    const cellsWithPosition: Omit<\n        ComputedCell<Datum>,\n        'formattedValue' | 'color' | 'opacity' | 'borderColor' | 'label' | 'labelTextColor'\n    >[] = cells.map(cell => ({\n        ...cell,\n        x: xScale(cell.data.x)! + cellWidth / 2,\n        y: yScale(cell.serieId)! + cellHeight / 2,\n        width: cellWidth,\n        height: cellHeight,\n    }))\n\n    return {\n        width,\n        height,\n        offsetX,\n        offsetY,\n        xScale,\n        yScale,\n        minValue: Math.min(...allValues),\n        maxValue: Math.max(...allValues),\n        cells: cellsWithPosition,\n    }\n}\n\nexport const computeSizeScale = (\n    size: false | SizeVariationConfig,\n    min: number,\n    max: number\n): ((value: number | null) => number) => {\n    if (!size) return () => 1\n\n    const scale = scaleLinear()\n        .domain(size.values ? size.values : [min, max])\n        .range(size.sizes)\n\n    return (value: number | null) => {\n        if (value === null) return 1\n        return scale(value)\n    }\n}\n\nexport const getCellAnnotationPosition = <Datum extends HeatMapDatum>(\n    cell: ComputedCell<Datum>\n) => ({\n    x: cell.x,\n    y: cell.y,\n})\n\nexport const getCellAnnotationDimensions = <Datum extends HeatMapDatum>(\n    cell: ComputedCell<Datum>\n) => ({\n    size: Math.max(cell.width, cell.height),\n    width: cell.width,\n    height: cell.height,\n})\n", "import { useMemo, useCallback, useState } from 'react'\nimport { usePropertyAccessor, useValueFormatter } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { useInheritedColor, getContinuousColorScale } from '@nivo/colors'\nimport { AnnotationMatcher, useAnnotations } from '@nivo/annotations'\nimport {\n    ComputedCell,\n    DefaultHeatMapDatum,\n    HeatMapCommonProps,\n    HeatMapDataProps,\n    HeatMapDatum,\n    SizeVariationConfig,\n} from './types'\nimport { commonDefaultProps } from './defaults'\nimport {\n    computeCells,\n    computeSizeScale,\n    getCellAnnotationPosition,\n    getCellAnnotationDimensions,\n} from './compute'\n\nexport const useComputeCells = <Datum extends HeatMapDatum, ExtraProps extends object>({\n    data,\n    width,\n    height,\n    xInnerPadding,\n    xOuterPadding,\n    yInnerPadding,\n    yOuterPadding,\n    forceSquare,\n}: {\n    data: HeatMapDataProps<Datum, ExtraProps>['data']\n    width: number\n    height: number\n} & Pick<\n    HeatMapCommonProps<Datum>,\n    'xOuterPadding' | 'xInnerPadding' | 'yOuterPadding' | 'yInnerPadding' | 'forceSquare'\n>) =>\n    useMemo(\n        () =>\n            computeCells<Datum, ExtraProps>({\n                data,\n                width,\n                height,\n                xInnerPadding,\n                xOuterPadding,\n                yInnerPadding,\n                yOuterPadding,\n                forceSquare,\n            }),\n        [\n            data,\n            width,\n            height,\n            xInnerPadding,\n            xOuterPadding,\n            yInnerPadding,\n            yOuterPadding,\n            forceSquare,\n        ]\n    )\n\nconst isHoverTargetByType = {\n    cell: <Datum extends HeatMapDatum>(\n        cell: Omit<\n            ComputedCell<Datum>,\n            'formattedValue' | 'color' | 'opacity' | 'borderColor' | 'label' | 'labelTextColor'\n        >,\n        current: ComputedCell<Datum>\n    ) => cell.id === current.id,\n    row: <Datum extends HeatMapDatum>(\n        cell: Omit<\n            ComputedCell<Datum>,\n            'formattedValue' | 'color' | 'opacity' | 'borderColor' | 'label' | 'labelTextColor'\n        >,\n        current: ComputedCell<Datum>\n    ) => cell.serieId === current.serieId,\n    column: <Datum extends HeatMapDatum>(\n        cell: Omit<\n            ComputedCell<Datum>,\n            'formattedValue' | 'color' | 'opacity' | 'borderColor' | 'label' | 'labelTextColor'\n        >,\n        current: ComputedCell<Datum>\n    ) => cell.data.x === current.data.x,\n    rowColumn: <Datum extends HeatMapDatum>(\n        cell: Omit<\n            ComputedCell<Datum>,\n            'formattedValue' | 'color' | 'opacity' | 'borderColor' | 'label' | 'labelTextColor'\n        >,\n        current: ComputedCell<Datum>\n    ) => cell.serieId === current.serieId || cell.data.x === current.data.x,\n}\n\nconst useSizeScale = (\n    size: false | SizeVariationConfig,\n    min: number,\n    max: number\n): ((value: number | null) => number) =>\n    useMemo(() => computeSizeScale(size, min, max), [size, min, max])\n\nconst useCellsStyle = <Datum extends HeatMapDatum = DefaultHeatMapDatum>({\n    cells,\n    minValue,\n    maxValue,\n    sizeVariation,\n    colors,\n    emptyColor,\n    opacity,\n    activeOpacity,\n    inactiveOpacity,\n    borderColor,\n    label,\n    labelTextColor,\n    valueFormat,\n    activeIds,\n}: {\n    cells: Omit<\n        ComputedCell<Datum>,\n        'formattedValue' | 'color' | 'opacity' | 'borderColor' | 'label' | 'labelTextColor'\n    >[]\n    minValue: number\n    maxValue: number\n    valueFormat?: HeatMapCommonProps<Datum>['valueFormat']\n    activeIds: string[]\n} & Pick<\n    HeatMapCommonProps<Datum>,\n    | 'sizeVariation'\n    | 'colors'\n    | 'emptyColor'\n    | 'opacity'\n    | 'activeOpacity'\n    | 'inactiveOpacity'\n    | 'borderColor'\n    | 'label'\n    | 'labelTextColor'\n>) => {\n    const getSize = useSizeScale(sizeVariation, minValue, maxValue)\n\n    const colorScale = useMemo(() => {\n        if (typeof colors === 'function') return null\n\n        return getContinuousColorScale(colors, {\n            min: minValue,\n            max: maxValue,\n        })\n    }, [colors, minValue, maxValue])\n\n    const getColor = useCallback(\n        (cell: Omit<ComputedCell<Datum>, 'color' | 'opacity' | 'borderColor'>) => {\n            if (cell.value !== null) {\n                if (typeof colors === 'function') return colors(cell)\n                if (colorScale !== null) return colorScale(cell.value)\n            }\n\n            return emptyColor\n        },\n        [colors, colorScale, emptyColor]\n    )\n    const theme = useTheme()\n    const getBorderColor = useInheritedColor(borderColor, theme)\n    const getLabelTextColor = useInheritedColor(labelTextColor, theme)\n\n    const formatValue = useValueFormatter(valueFormat)\n    const getLabel = usePropertyAccessor(label)\n\n    const styledCells = useMemo(\n        () =>\n            cells.map(cell => {\n                let computedOpacity = opacity\n                if (activeIds.length > 0) {\n                    computedOpacity = activeIds.includes(cell.id) ? activeOpacity : inactiveOpacity\n                }\n\n                const sizeMultiplier = getSize(cell.value)\n\n                const computedCell = {\n                    ...cell,\n                    width: cell.width * sizeMultiplier,\n                    height: cell.height * sizeMultiplier,\n                    formattedValue: cell.value !== null ? formatValue(cell.value) : null,\n                    opacity: computedOpacity,\n                } as ComputedCell<Datum>\n\n                computedCell.label = getLabel(computedCell)\n                computedCell.color = getColor(computedCell)\n                computedCell.borderColor = getBorderColor(computedCell)\n                computedCell.labelTextColor = getLabelTextColor(computedCell)\n\n                return computedCell\n            }),\n        [\n            cells,\n            getSize,\n            getColor,\n            getBorderColor,\n            getLabelTextColor,\n            formatValue,\n            getLabel,\n            activeIds,\n            opacity,\n            activeOpacity,\n            inactiveOpacity,\n        ]\n    )\n\n    return {\n        cells: styledCells,\n        colorScale,\n    }\n}\n\nexport const useHeatMap = <\n    Datum extends HeatMapDatum = DefaultHeatMapDatum,\n    ExtraProps extends object = Record<string, never>,\n>({\n    data,\n    valueFormat,\n    width: _width,\n    height: _height,\n    xOuterPadding = commonDefaultProps.xOuterPadding,\n    xInnerPadding = commonDefaultProps.xInnerPadding,\n    yOuterPadding = commonDefaultProps.yOuterPadding,\n    yInnerPadding = commonDefaultProps.yInnerPadding,\n    forceSquare = commonDefaultProps.forceSquare,\n    sizeVariation = commonDefaultProps.sizeVariation,\n    colors = commonDefaultProps.colors as HeatMapCommonProps<Datum>['colors'],\n    emptyColor = commonDefaultProps.emptyColor,\n    opacity = commonDefaultProps.opacity,\n    activeOpacity = commonDefaultProps.activeOpacity,\n    inactiveOpacity = commonDefaultProps.inactiveOpacity,\n    borderColor = commonDefaultProps.borderColor as HeatMapCommonProps<Datum>['borderColor'],\n    label = commonDefaultProps.label as HeatMapCommonProps<Datum>['label'],\n    labelTextColor = commonDefaultProps.labelTextColor as HeatMapCommonProps<Datum>['labelTextColor'],\n    hoverTarget = commonDefaultProps.hoverTarget,\n}: {\n    data: HeatMapDataProps<Datum, ExtraProps>['data']\n    width: number\n    height: number\n} & Partial<\n    Pick<\n        HeatMapCommonProps<Datum>,\n        | 'valueFormat'\n        | 'xOuterPadding'\n        | 'xInnerPadding'\n        | 'yOuterPadding'\n        | 'yInnerPadding'\n        | 'forceSquare'\n        | 'sizeVariation'\n        | 'colors'\n        | 'emptyColor'\n        | 'opacity'\n        | 'activeOpacity'\n        | 'inactiveOpacity'\n        | 'borderColor'\n        | 'label'\n        | 'labelTextColor'\n        | 'hoverTarget'\n    >\n>) => {\n    const [activeCell, setActiveCell] = useState<ComputedCell<Datum> | null>(null)\n\n    const { width, height, offsetX, offsetY, cells, xScale, yScale, minValue, maxValue } =\n        useComputeCells<Datum, ExtraProps>({\n            data,\n            width: _width,\n            height: _height,\n            xOuterPadding,\n            xInnerPadding,\n            yOuterPadding,\n            yInnerPadding,\n            forceSquare,\n        })\n\n    const activeIds = useMemo(() => {\n        if (!activeCell) return []\n\n        const isHoverTarget = isHoverTargetByType[hoverTarget]\n\n        return cells.filter(cell => isHoverTarget(cell, activeCell)).map(cell => cell.id)\n    }, [cells, activeCell, hoverTarget])\n\n    const { cells: computedCells, colorScale } = useCellsStyle<Datum>({\n        cells,\n        minValue,\n        maxValue,\n        sizeVariation,\n        colors,\n        emptyColor,\n        opacity,\n        activeOpacity,\n        inactiveOpacity,\n        borderColor,\n        label,\n        labelTextColor,\n        valueFormat,\n        activeIds,\n    })\n\n    return {\n        width,\n        height,\n        offsetX,\n        offsetY,\n        cells: computedCells,\n        xScale,\n        yScale,\n        colorScale,\n        activeCell,\n        setActiveCell,\n    }\n}\n\nexport const useCellAnnotations = <Datum extends HeatMapDatum>(\n    cells: ComputedCell<Datum>[],\n    annotations: AnnotationMatcher<ComputedCell<Datum>>[]\n) =>\n    useAnnotations<ComputedCell<Datum>>({\n        data: cells,\n        annotations,\n        getPosition: getCellAnnotationPosition,\n        getDimensions: getCellAnnotationDimensions,\n    })\n", "import { memo, useMemo } from 'react'\nimport { animated, to } from '@react-spring/web'\nimport { useTheme } from '@nivo/theming'\nimport { Text } from '@nivo/text'\nimport { CellComponentProps, HeatMapDatum } from './types'\n\nconst NonMemoizedHeatMapCellRect = <Datum extends HeatMapDatum>({\n    cell,\n    borderWidth,\n    borderRadius,\n    animatedProps,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n    enableLabels,\n}: CellComponentProps<Datum>) => {\n    const theme = useTheme()\n\n    const handlers = useMemo(\n        () => ({\n            onMouseEnter: onMouseEnter ? onMouseEnter(cell) : undefined,\n            onMouseMove: onMouseMove ? onMouseMove(cell) : undefined,\n            onMouseLeave: onMouseLeave ? onMouseLeave(cell) : undefined,\n            onClick: onClick ? onClick(cell) : undefined,\n        }),\n        [cell, onMouseEnter, onMouseMove, onMouseLeave, onClick]\n    )\n\n    return (\n        <animated.g\n            data-testid={`cell.${cell.id}`}\n            style={{ cursor: 'pointer' }}\n            opacity={animatedProps.opacity}\n            {...handlers}\n            transform={to(\n                [animatedProps.x, animatedProps.y, animatedProps.scale],\n                (x, y, scale) => `translate(${x}, ${y}) scale(${scale})`\n            )}\n        >\n            <animated.rect\n                transform={to(\n                    [animatedProps.width, animatedProps.height],\n                    (width, height) => `translate(${width * -0.5}, ${height * -0.5})`\n                )}\n                key={cell.id}\n                fill={animatedProps.color}\n                width={animatedProps.width}\n                height={animatedProps.height}\n                stroke={animatedProps.borderColor}\n                strokeWidth={borderWidth}\n                rx={borderRadius}\n                ry={borderRadius}\n            />\n            {enableLabels && (\n                <Text\n                    textAnchor=\"middle\"\n                    dominantBaseline=\"central\"\n                    fill={animatedProps.labelTextColor}\n                    style={{\n                        ...theme.labels.text,\n                        fill: undefined,\n                        userSelect: 'none',\n                    }}\n                >\n                    {cell.label}\n                </Text>\n            )}\n        </animated.g>\n    )\n}\n\nexport const HeatMapCellRect = memo(NonMemoizedHeatMapCellRect) as typeof NonMemoizedHeatMapCellRect\n", "import { memo, useMemo } from 'react'\nimport { animated, to } from '@react-spring/web'\nimport { useTheme } from '@nivo/theming'\nimport { Text } from '@nivo/text'\nimport { HeatMapDatum, CellComponentProps } from './types'\n\nconst NonMemoizedHeatMapCellCircle = <Datum extends HeatMapDatum>({\n    cell,\n    borderWidth,\n    animatedProps,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n    enableLabels,\n}: CellComponentProps<Datum>) => {\n    const theme = useTheme()\n\n    const handlers = useMemo(\n        () => ({\n            onMouseEnter: onMouseEnter ? onMouseEnter(cell) : undefined,\n            onMouseMove: onMouseMove ? onMouseMove(cell) : undefined,\n            onMouseLeave: onMouseLeave ? onMouseLeave(cell) : undefined,\n            onClick: onClick ? onClick(cell) : undefined,\n        }),\n        [cell, onMouseEnter, onMouseMove, onMouseLeave, onClick]\n    )\n\n    return (\n        <animated.g\n            data-testid={`cell.${cell.id}`}\n            style={{ cursor: 'pointer' }}\n            opacity={animatedProps.opacity}\n            {...handlers}\n            transform={to([animatedProps.x, animatedProps.y], (x, y) => `translate(${x}, ${y})`)}\n        >\n            <animated.circle\n                r={to(\n                    [animatedProps.width, animatedProps.height],\n                    (width, height) => Math.min(width, height) / 2\n                )}\n                fill={animatedProps.color}\n                fillOpacity={animatedProps.opacity}\n                strokeWidth={borderWidth}\n                stroke={animatedProps.borderColor}\n            />\n            {enableLabels && (\n                <Text\n                    dominantBaseline=\"central\"\n                    textAnchor=\"middle\"\n                    fill={animatedProps.labelTextColor}\n                    style={{\n                        ...theme.labels.text,\n                        fill: undefined,\n                    }}\n                >\n                    {cell.label}\n                </Text>\n            )}\n        </animated.g>\n    )\n}\n\nexport const HeatMapCellCircle = memo(\n    NonMemoizedHeatMapCellCircle\n) as typeof NonMemoizedHeatMapCellCircle\n", "import { createElement, MouseEvent, useMemo } from 'react'\nimport { useTransition } from '@react-spring/web'\nimport { useMotionConfig } from '@nivo/core'\nimport { useTooltip } from '@nivo/tooltip'\nimport {\n    CellComponent,\n    ComputedCell,\n    HeatMapDatum,\n    HeatMapSvgProps,\n    CellAnimatedProps,\n} from './types'\nimport { HeatMapCellRect } from './HeatMapCellRect'\nimport { HeatMapCellCircle } from './HeatMapCellCircle'\n\ninterface HeatMapCellsProps<Datum extends HeatMapDatum, ExtraProps extends object> {\n    cells: ComputedCell<Datum>[]\n    cellComponent: NonNullable<HeatMapSvgProps<Datum, ExtraProps>['cellComponent']>\n    borderRadius: NonNullable<HeatMapSvgProps<Datum, ExtraProps>['borderRadius']>\n    borderWidth: NonNullable<HeatMapSvgProps<Datum, ExtraProps>['borderWidth']>\n    isInteractive: NonNullable<HeatMapSvgProps<Datum, ExtraProps>['isInteractive']>\n    setActiveCell: (cell: ComputedCell<Datum> | null) => void\n    onMouseEnter: HeatMapSvgProps<Datum, ExtraProps>['onMouseEnter']\n    onMouseMove: HeatMapSvgProps<Datum, ExtraProps>['onMouseMove']\n    onMouseLeave: HeatMapSvgProps<Datum, ExtraProps>['onMouseLeave']\n    onClick: HeatMapSvgProps<Datum, ExtraProps>['onClick']\n    tooltip: NonNullable<HeatMapSvgProps<Datum, ExtraProps>['tooltip']>\n    enableLabels: NonNullable<HeatMapSvgProps<Datum, ExtraProps>['enableLabels']>\n}\n\nconst enterTransition = <Datum extends HeatMapDatum>(cell: ComputedCell<Datum>) => ({\n    x: cell.x,\n    y: cell.y,\n    width: cell.width,\n    height: cell.height,\n    color: cell.color,\n    opacity: 0,\n    borderColor: cell.borderColor,\n    labelTextColor: cell.labelTextColor,\n    scale: 0,\n})\n\nconst regularTransition = <Datum extends HeatMapDatum>(cell: ComputedCell<Datum>) => ({\n    x: cell.x,\n    y: cell.y,\n    width: cell.width,\n    height: cell.height,\n    color: cell.color,\n    opacity: cell.opacity,\n    borderColor: cell.borderColor,\n    labelTextColor: cell.labelTextColor,\n    scale: 1,\n})\n\nconst exitTransition = <Datum extends HeatMapDatum>(cell: ComputedCell<Datum>) => ({\n    x: cell.x,\n    y: cell.y,\n    width: cell.width,\n    height: cell.height,\n    color: cell.color,\n    opacity: 0,\n    borderColor: cell.borderColor,\n    labelTextColor: cell.labelTextColor,\n    scale: 0,\n})\n\nexport const HeatMapCells = <Datum extends HeatMapDatum, ExtraProps extends object>({\n    cells,\n    cellComponent,\n    borderRadius,\n    borderWidth,\n    isInteractive,\n    setActiveCell,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n    tooltip,\n    enableLabels,\n}: HeatMapCellsProps<Datum, ExtraProps>) => {\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const transition = useTransition<ComputedCell<Datum>, CellAnimatedProps>(cells, {\n        keys: (cell: ComputedCell<Datum>) => cell.id,\n        initial: regularTransition,\n        from: enterTransition,\n        enter: regularTransition,\n        update: regularTransition,\n        leave: exitTransition,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleMouseEnter = useMemo(() => {\n        if (!isInteractive) return undefined\n\n        return (cell: ComputedCell<Datum>) => (event: MouseEvent) => {\n            showTooltipFromEvent(createElement(tooltip, { cell }), event)\n            setActiveCell(cell)\n            onMouseEnter?.(cell, event)\n        }\n    }, [isInteractive, showTooltipFromEvent, tooltip, setActiveCell, onMouseEnter])\n\n    const handleMouseMove = useMemo(() => {\n        if (!isInteractive) return undefined\n\n        return (cell: ComputedCell<Datum>) => (event: MouseEvent) => {\n            showTooltipFromEvent(createElement(tooltip, { cell }), event)\n            onMouseMove?.(cell, event)\n        }\n    }, [isInteractive, showTooltipFromEvent, tooltip, onMouseMove])\n\n    const handleMouseLeave = useMemo(() => {\n        if (!isInteractive) return undefined\n\n        return (cell: ComputedCell<Datum>) => (event: MouseEvent) => {\n            hideTooltip()\n            setActiveCell(null)\n            onMouseLeave?.(cell, event)\n        }\n    }, [isInteractive, hideTooltip, setActiveCell, onMouseLeave])\n\n    const handleClick = useMemo(() => {\n        if (!isInteractive) return undefined\n\n        return (cell: ComputedCell<Datum>) => (event: MouseEvent) => {\n            onClick?.(cell, event)\n        }\n    }, [isInteractive, onClick])\n\n    let Cell: CellComponent<Datum>\n    if (cellComponent === 'rect') {\n        Cell = HeatMapCellRect\n    } else if (cellComponent === 'circle') {\n        Cell = HeatMapCellCircle\n    } else {\n        Cell = cellComponent\n    }\n\n    return (\n        <>\n            {transition((animatedProps, cell) =>\n                createElement(Cell, {\n                    cell,\n                    borderRadius,\n                    borderWidth,\n                    animatedProps,\n                    enableLabels,\n                    onMouseEnter: handleMouseEnter,\n                    onMouseMove: handleMouseMove,\n                    onMouseLeave: handleMouseLeave,\n                    onClick: handleClick,\n                })\n            )}\n        </>\n    )\n}\n", "import { Annotation } from '@nivo/annotations'\nimport { ComputedCell, HeatMapCommonProps, HeatMapDatum } from './types'\nimport { useCellAnnotations } from './hooks'\n\ninterface HeatMapCellAnnotationsProps<Datum extends HeatMapDatum> {\n    cells: ComputedCell<Datum>[]\n    annotations: NonNullable<HeatMapCommonProps<Datum>['annotations']>\n}\n\nexport const HeatMapCellAnnotations = <Datum extends HeatMapDatum>({\n    cells,\n    annotations,\n}: HeatMapCellAnnotationsProps<Datum>) => {\n    const boundAnnotations = useCellAnnotations<Datum>(cells, annotations)\n\n    return (\n        <>\n            {boundAnnotations.map((annotation, i) => (\n                <Annotation key={i} {...annotation} />\n            ))}\n        </>\n    )\n}\n", "import { ReactNode, Fragment, createElement, useMemo, forwardRef, Ref, ReactElement } from 'react'\nimport { SvgWrapper, Container, useDimensions, WithChartRef } from '@nivo/core'\nimport { Axes, Grid } from '@nivo/axes'\nimport { AnchoredContinuousColorsLegendSvg } from '@nivo/legends'\nimport {\n    DefaultHeatMapDatum,\n    HeatMapDatum,\n    HeatMapCommonProps,\n    HeatMapSvgProps,\n    LayerId,\n    CustomLayerProps,\n} from './types'\nimport { useHeatMap } from './hooks'\nimport { svgDefaultProps } from './defaults'\nimport { HeatMapCells } from './HeatMapCells'\nimport { HeatMapCellAnnotations } from './HeatMapCellAnnotations'\n\ntype InnerHeatMapProps<Datum extends HeatMapDatum, ExtraProps extends object> = Omit<\n    HeatMapSvgProps<Datum, ExtraProps>,\n    'animate' | 'motionConfig' | 'renderWrapper' | 'theme'\n> & {\n    forwardedRef: Ref<SVGSVGElement>\n}\n\nconst InnerHeatMap = <Datum extends HeatMapDatum, ExtraProps extends object>({\n    data,\n    layers = svgDefaultProps.layers,\n    valueFormat,\n    width,\n    height,\n    margin: partialMargin,\n    forceSquare = svgDefaultProps.forceSquare,\n    xInnerPadding = svgDefaultProps.xInnerPadding,\n    xOuterPadding = svgDefaultProps.xOuterPadding,\n    yInnerPadding = svgDefaultProps.yInnerPadding,\n    yOuterPadding = svgDefaultProps.yOuterPadding,\n    sizeVariation = svgDefaultProps.sizeVariation,\n    cellComponent = svgDefaultProps.cellComponent as NonNullable<\n        HeatMapSvgProps<Datum, ExtraProps>['cellComponent']\n    >,\n    opacity = svgDefaultProps.opacity,\n    activeOpacity = svgDefaultProps.activeOpacity,\n    inactiveOpacity = svgDefaultProps.inactiveOpacity,\n    borderRadius = svgDefaultProps.borderRadius,\n    borderWidth = svgDefaultProps.borderWidth,\n    borderColor = svgDefaultProps.borderColor as HeatMapCommonProps<Datum>['borderColor'],\n    enableGridX = svgDefaultProps.enableGridX,\n    enableGridY = svgDefaultProps.enableGridY,\n    axisTop = svgDefaultProps.axisTop,\n    axisRight = svgDefaultProps.axisRight,\n    axisBottom = svgDefaultProps.axisBottom,\n    axisLeft = svgDefaultProps.axisLeft,\n    enableLabels = svgDefaultProps.enableLabels,\n    label = svgDefaultProps.label as HeatMapCommonProps<Datum>['label'],\n    labelTextColor = svgDefaultProps.labelTextColor as HeatMapCommonProps<Datum>['labelTextColor'],\n    colors = svgDefaultProps.colors as HeatMapCommonProps<Datum>['colors'],\n    emptyColor = svgDefaultProps.emptyColor,\n    legends = svgDefaultProps.legends,\n    annotations = svgDefaultProps.annotations as HeatMapCommonProps<Datum>['annotations'],\n    isInteractive = svgDefaultProps.isInteractive,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onClick,\n    hoverTarget = svgDefaultProps.hoverTarget,\n    tooltip = svgDefaultProps.tooltip as HeatMapCommonProps<Datum>['tooltip'],\n    role,\n    ariaLabel,\n    ariaLabelledBy,\n    ariaDescribedBy,\n    forwardedRef,\n}: InnerHeatMapProps<Datum, ExtraProps>) => {\n    const {\n        margin: _margin,\n        innerWidth: _innerWidth,\n        innerHeight: _innerHeight,\n        outerWidth,\n        outerHeight,\n    } = useDimensions(width, height, partialMargin)\n\n    const {\n        width: innerWidth,\n        height: innerHeight,\n        offsetX,\n        offsetY,\n        xScale,\n        yScale,\n        cells,\n        colorScale,\n        activeCell,\n        setActiveCell,\n    } = useHeatMap<Datum, ExtraProps>({\n        data,\n        valueFormat,\n        width: _innerWidth,\n        height: _innerHeight,\n        forceSquare,\n        xInnerPadding,\n        xOuterPadding,\n        yInnerPadding,\n        yOuterPadding,\n        sizeVariation,\n        colors,\n        emptyColor,\n        opacity,\n        activeOpacity,\n        inactiveOpacity,\n        borderColor,\n        label,\n        labelTextColor,\n        hoverTarget,\n    })\n\n    const margin = useMemo(\n        () => ({\n            ..._margin,\n            top: _margin.top + offsetY,\n            left: _margin.left + offsetX,\n        }),\n        [_margin, offsetX, offsetY]\n    )\n\n    const layerById: Record<LayerId, ReactNode> = {\n        grid: null,\n        axes: null,\n        cells: null,\n        legends: null,\n        annotations: null,\n    }\n\n    if (layers.includes('grid')) {\n        layerById.grid = (\n            <Grid\n                key=\"grid\"\n                width={innerWidth} // - offsetX * 2\n                height={innerHeight} // - offsetY * 2\n                xScale={enableGridX ? xScale : null}\n                yScale={enableGridY ? yScale : null}\n            />\n        )\n    }\n\n    if (layers.includes('axes')) {\n        layerById.axes = (\n            <Axes\n                key=\"axes\"\n                xScale={xScale}\n                yScale={yScale}\n                width={innerWidth} // - offsetX * 2\n                height={innerHeight} // - offsetY * 2\n                top={axisTop}\n                right={axisRight}\n                bottom={axisBottom}\n                left={axisLeft}\n            />\n        )\n    }\n\n    if (layers.includes('cells')) {\n        layerById.cells = (\n            <Fragment key=\"cells\">\n                <HeatMapCells<Datum, ExtraProps>\n                    cells={cells}\n                    cellComponent={cellComponent}\n                    borderRadius={borderRadius}\n                    borderWidth={borderWidth}\n                    isInteractive={isInteractive}\n                    setActiveCell={setActiveCell}\n                    onMouseEnter={onMouseEnter}\n                    onMouseMove={onMouseMove}\n                    onMouseLeave={onMouseLeave}\n                    onClick={onClick}\n                    tooltip={tooltip}\n                    enableLabels={enableLabels}\n                />\n            </Fragment>\n        )\n    }\n\n    if (layers.includes('legends') && colorScale !== null) {\n        layerById.legends = (\n            <Fragment key=\"legends\">\n                {legends.map((legend, index) => (\n                    <AnchoredContinuousColorsLegendSvg\n                        {...legend}\n                        key={index}\n                        containerWidth={innerWidth}\n                        containerHeight={innerHeight}\n                        scale={colorScale}\n                    />\n                ))}\n            </Fragment>\n        )\n    }\n\n    if (layers.includes('annotations') && annotations.length > 0) {\n        layerById.annotations = (\n            <HeatMapCellAnnotations<Datum>\n                key=\"annotations\"\n                cells={cells}\n                annotations={annotations}\n            />\n        )\n    }\n\n    const customLayerProps: CustomLayerProps<Datum> = {\n        cells,\n        activeCell,\n        setActiveCell,\n    }\n\n    return (\n        <SvgWrapper\n            width={outerWidth}\n            height={outerHeight}\n            margin={Object.assign({}, margin, {\n                top: margin.top, //+ offsetY,\n                left: margin.left, // + offsetX,\n            })}\n            role={role}\n            ariaLabel={ariaLabel}\n            ariaLabelledBy={ariaLabelledBy}\n            ariaDescribedBy={ariaDescribedBy}\n            ref={forwardedRef}\n        >\n            {layers.map((layer, i) => {\n                if (typeof layer === 'function') {\n                    return <Fragment key={i}>{createElement(layer, customLayerProps)}</Fragment>\n                }\n\n                return layerById?.[layer] ?? null\n            })}\n        </SvgWrapper>\n    )\n}\n\nexport const HeatMap = forwardRef(\n    <\n        Datum extends HeatMapDatum = DefaultHeatMapDatum,\n        ExtraProps extends object = Record<string, never>,\n    >(\n        {\n            isInteractive = svgDefaultProps.isInteractive,\n            animate = svgDefaultProps.animate,\n            motionConfig = svgDefaultProps.motionConfig,\n            theme,\n            renderWrapper,\n            ...otherProps\n        }: HeatMapSvgProps<Datum, ExtraProps>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <Container\n            {...{\n                animate,\n                isInteractive,\n                motionConfig,\n                renderWrapper,\n                theme,\n            }}\n        >\n            <InnerHeatMap<Datum, ExtraProps>\n                isInteractive={isInteractive}\n                {...otherProps}\n                forwardedRef={ref}\n            />\n        </Container>\n    )\n) as <\n    Datum extends HeatMapDatum = DefaultHeatMapDatum,\n    ExtraProps extends object = Record<string, never>,\n>(\n    props: WithChartRef<HeatMapSvgProps<Datum, ExtraProps>, SVGSVGElement>\n) => ReactElement\n", "import { forwardRef, Ref, ReactElement } from 'react'\nimport { ResponsiveWrapper, WithChartRef, ResponsiveProps } from '@nivo/core'\nimport { DefaultHeatMapDatum, HeatMapDatum, HeatMapSvgProps } from './types'\nimport { HeatMap } from './HeatMap'\n\nexport const ResponsiveHeatMap = forwardRef(\n    <\n        Datum extends HeatMapDatum = DefaultHeatMapDatum,\n        ExtraProps extends object = Record<string, never>,\n    >(\n        {\n            defaultWidth,\n            defaultHeight,\n            onResize,\n            debounceResize,\n            ...props\n        }: ResponsiveProps<HeatMapSvgProps<Datum, ExtraProps>>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <ResponsiveWrapper\n            defaultWidth={defaultWidth}\n            defaultHeight={defaultHeight}\n            onResize={onResize}\n            debounceResize={debounceResize}\n        >\n            {({ width, height }) => (\n                <HeatMap<Datum, ExtraProps> width={width} height={height} {...props} ref={ref} />\n            )}\n        </ResponsiveWrapper>\n    )\n) as <\n    Datum extends HeatMapDatum = DefaultHeatMapDatum,\n    ExtraProps extends object = Record<string, never>,\n>(\n    props: WithChartRef<ResponsiveProps<HeatMapSvgProps<Datum, ExtraProps>>, SVGSVGElement>\n) => ReactElement\n", "import { setCanvasFont, drawCanvasText } from '@nivo/text'\nimport { CellCanvasRendererProps, HeatMapDatum } from './types'\n\nexport const renderRect = <Datum extends HeatMapDatum>(\n    ctx: CanvasRenderingContext2D,\n    {\n        cell: { x, y, width, height, color, borderColor, opacity, labelTextColor, label },\n        borderWidth,\n        enableLabels,\n        theme,\n    }: CellCanvasRendererProps<Datum>\n) => {\n    ctx.save()\n    ctx.globalAlpha = opacity\n\n    ctx.fillStyle = color\n    if (borderWidth > 0) {\n        ctx.strokeStyle = borderColor\n        ctx.lineWidth = borderWidth\n    }\n\n    ctx.fillRect(x - width / 2, y - height / 2, width, height)\n    if (borderWidth > 0) {\n        ctx.strokeRect(x - width / 2, y - height / 2, width, height)\n    }\n\n    if (enableLabels) {\n        setCanvasFont(ctx, theme.labels.text)\n        ctx.textAlign = 'center'\n        ctx.textBaseline = 'middle'\n        drawCanvasText(\n            ctx,\n            {\n                ...theme.labels.text,\n                fill: labelTextColor,\n            },\n            label,\n            x,\n            y\n        )\n    }\n\n    ctx.restore()\n}\n\nexport const renderCircle = <Datum extends HeatMapDatum>(\n    ctx: CanvasRenderingContext2D,\n    {\n        cell: { x, y, width, height, color, borderColor, opacity, labelTextColor, label },\n        borderWidth,\n        enableLabels,\n        theme,\n    }: CellCanvasRendererProps<Datum>\n) => {\n    ctx.save()\n    ctx.globalAlpha = opacity\n\n    const radius = Math.min(width, height) / 2\n\n    ctx.fillStyle = color\n    if (borderWidth > 0) {\n        ctx.strokeStyle = borderColor\n        ctx.lineWidth = borderWidth\n    }\n\n    ctx.beginPath()\n    ctx.arc(x, y, radius, 0, 2 * Math.PI)\n\n    ctx.fill()\n    if (borderWidth > 0) {\n        ctx.stroke()\n    }\n\n    if (enableLabels) {\n        setCanvasFont(ctx, theme.labels.text)\n        ctx.textAlign = 'center'\n        ctx.textBaseline = 'middle'\n        drawCanvasText(\n            ctx,\n            {\n                ...theme.labels.text,\n                fill: labelTextColor,\n            },\n            label,\n            x,\n            y\n        )\n    }\n\n    ctx.restore()\n}\n", "import {\n    useEffect,\n    useRef,\n    useCallback,\n    createElement,\n    useMemo,\n    MouseEvent,\n    forwardRef,\n    Ref,\n    ReactElement,\n} from 'react'\nimport {\n    getRelativeCursor,\n    isCursorInRect,\n    useDimensions,\n    Container,\n    WithChartRef,\n    mergeRefs,\n} from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { renderAxesToCanvas, renderGridLinesToCanvas } from '@nivo/axes'\nimport { useTooltip } from '@nivo/tooltip'\nimport { renderContinuousColorLegendToCanvas } from '@nivo/legends'\nimport { renderAnnotationsToCanvas, useComputedAnnotations } from '@nivo/annotations'\nimport { useHeatMap, useCellAnnotations } from './hooks'\nimport { renderRect, renderCircle } from './canvas'\nimport { canvasDefaultProps } from './defaults'\nimport {\n    CellCanvasRenderer,\n    DefaultHeatMapDatum,\n    HeatMapCanvasProps,\n    HeatMapCommonProps,\n    HeatMapDatum,\n    CellShape,\n    CustomLayerProps,\n} from './types'\n\ntype InnerNetworkCanvasProps<Datum extends HeatMapDatum, ExtraProps extends object> = Omit<\n    HeatMapCanvasProps<Datum, ExtraProps>,\n    'renderWrapper' | 'theme'\n> & {\n    forwardedRef: Ref<HTMLCanvasElement>\n}\n\nconst InnerHeatMapCanvas = <Datum extends HeatMapDatum, ExtraProps extends object>({\n    data,\n    layers = canvasDefaultProps.layers,\n    valueFormat,\n    width,\n    height,\n    margin: partialMargin,\n    xInnerPadding = canvasDefaultProps.xInnerPadding,\n    xOuterPadding = canvasDefaultProps.xOuterPadding,\n    yInnerPadding = canvasDefaultProps.yInnerPadding,\n    yOuterPadding = canvasDefaultProps.yOuterPadding,\n    forceSquare = canvasDefaultProps.forceSquare,\n    sizeVariation = canvasDefaultProps.sizeVariation,\n    renderCell: _renderCell = canvasDefaultProps.renderCell as CellShape,\n    opacity = canvasDefaultProps.opacity,\n    activeOpacity = canvasDefaultProps.activeOpacity,\n    inactiveOpacity = canvasDefaultProps.inactiveOpacity,\n    borderWidth = canvasDefaultProps.borderWidth,\n    borderColor = canvasDefaultProps.borderColor as HeatMapCommonProps<Datum>['borderColor'],\n    enableGridX = canvasDefaultProps.enableGridX,\n    enableGridY = canvasDefaultProps.enableGridY,\n    axisTop = canvasDefaultProps.axisTop,\n    axisRight = canvasDefaultProps.axisRight,\n    axisBottom = canvasDefaultProps.axisBottom,\n    axisLeft = canvasDefaultProps.axisLeft,\n    enableLabels = canvasDefaultProps.enableLabels,\n    label = canvasDefaultProps.label as HeatMapCommonProps<Datum>['label'],\n    labelTextColor = canvasDefaultProps.labelTextColor as HeatMapCommonProps<Datum>['labelTextColor'],\n    colors = canvasDefaultProps.colors as HeatMapCommonProps<Datum>['colors'],\n    emptyColor = canvasDefaultProps.emptyColor,\n    legends = canvasDefaultProps.legends,\n    annotations = canvasDefaultProps.annotations as HeatMapCommonProps<Datum>['annotations'],\n    isInteractive = canvasDefaultProps.isInteractive,\n    onClick,\n    hoverTarget = canvasDefaultProps.hoverTarget,\n    tooltip = canvasDefaultProps.tooltip as HeatMapCommonProps<Datum>['tooltip'],\n    role,\n    ariaLabel,\n    ariaLabelledBy,\n    ariaDescribedBy,\n    pixelRatio = canvasDefaultProps.pixelRatio,\n    forwardedRef,\n}: InnerNetworkCanvasProps<Datum, ExtraProps>) => {\n    const canvasEl = useRef<HTMLCanvasElement | null>(null)\n\n    const {\n        margin: _margin,\n        innerWidth: _innerWidth,\n        innerHeight: _innerHeight,\n        outerWidth,\n        outerHeight,\n    } = useDimensions(width, height, partialMargin)\n\n    const {\n        width: innerWidth,\n        height: innerHeight,\n        offsetX,\n        offsetY,\n        xScale,\n        yScale,\n        cells,\n        colorScale,\n        activeCell,\n        setActiveCell,\n    } = useHeatMap<Datum, ExtraProps>({\n        data,\n        valueFormat,\n        width: _innerWidth,\n        height: _innerHeight,\n        xInnerPadding,\n        xOuterPadding,\n        yInnerPadding,\n        yOuterPadding,\n        forceSquare,\n        sizeVariation,\n        colors,\n        emptyColor,\n        opacity,\n        activeOpacity,\n        inactiveOpacity,\n        borderColor,\n        label,\n        labelTextColor,\n        hoverTarget,\n    })\n\n    const margin = useMemo(\n        () => ({\n            ..._margin,\n            top: _margin.top + offsetY,\n            left: _margin.left + offsetX,\n        }),\n        [_margin, offsetX, offsetY]\n    )\n\n    const boundAnnotations = useCellAnnotations(cells, annotations)\n    const computedAnnotations = useComputedAnnotations({\n        annotations: boundAnnotations,\n    })\n\n    let renderCell: CellCanvasRenderer<Datum>\n    if (typeof _renderCell === 'function') {\n        renderCell = _renderCell\n    } else if (_renderCell === 'circle') {\n        renderCell = renderCircle\n    } else {\n        renderCell = renderRect\n    }\n\n    const theme = useTheme()\n\n    const customLayerProps: CustomLayerProps<Datum> = useMemo(\n        () => ({\n            cells,\n            activeCell,\n            setActiveCell,\n        }),\n        [cells, activeCell, setActiveCell]\n    )\n\n    useEffect(() => {\n        if (canvasEl.current === null) return\n\n        const ctx = canvasEl.current.getContext('2d')\n        if (!ctx) return\n\n        canvasEl.current.width = outerWidth * pixelRatio\n        canvasEl.current.height = outerHeight * pixelRatio\n\n        ctx.scale(pixelRatio, pixelRatio)\n\n        ctx.fillStyle = theme.background\n        ctx.fillRect(0, 0, outerWidth, outerHeight)\n        ctx.translate(margin.left, margin.top) // + offsetX, margin.top + offsetY)\n\n        layers.forEach(layer => {\n            if (layer === 'grid') {\n                ctx.lineWidth = theme.grid.line.strokeWidth as number\n                ctx.strokeStyle = theme.grid.line.stroke as string\n\n                if (enableGridX) {\n                    renderGridLinesToCanvas(ctx, {\n                        width: innerWidth,\n                        height: innerHeight,\n                        scale: xScale,\n                        axis: 'x',\n                    })\n                }\n                if (enableGridY) {\n                    renderGridLinesToCanvas(ctx, {\n                        width: innerWidth,\n                        height: innerHeight,\n                        scale: yScale,\n                        axis: 'y',\n                    })\n                }\n            } else if (layer === 'axes') {\n                renderAxesToCanvas(ctx, {\n                    xScale,\n                    yScale,\n                    width: innerWidth, // - offsetX * 2,\n                    height: innerHeight, // - offsetY * 2,\n                    top: axisTop,\n                    right: axisRight,\n                    bottom: axisBottom,\n                    left: axisLeft,\n                    theme,\n                })\n            } else if (layer === 'cells') {\n                ctx.textAlign = 'center'\n                ctx.textBaseline = 'middle'\n\n                cells.forEach(cell => {\n                    renderCell(ctx, { cell, borderWidth, enableLabels, theme })\n                })\n            } else if (layer === 'legends' && colorScale !== null) {\n                legends.forEach(legend => {\n                    renderContinuousColorLegendToCanvas(ctx, {\n                        ...legend,\n                        containerWidth: innerWidth,\n                        containerHeight: innerHeight,\n                        scale: colorScale,\n                        theme,\n                    })\n                })\n            } else if (layer === 'annotations') {\n                renderAnnotationsToCanvas(ctx, {\n                    annotations: computedAnnotations,\n                    theme,\n                })\n            } else if (typeof layer === 'function') {\n                layer(ctx, customLayerProps)\n            }\n        })\n    }, [\n        canvasEl,\n        pixelRatio,\n        outerWidth,\n        outerHeight,\n        innerWidth,\n        innerHeight,\n        margin,\n        layers,\n        customLayerProps,\n        cells,\n        renderCell,\n        enableGridX,\n        enableGridY,\n        axisTop,\n        axisRight,\n        axisBottom,\n        axisLeft,\n        xScale,\n        yScale,\n        theme,\n        borderWidth,\n        enableLabels,\n        colorScale,\n        legends,\n        computedAnnotations,\n    ])\n\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleMouseHover = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (canvasEl.current === null) return\n\n            const [x, y] = getRelativeCursor(canvasEl.current, event)\n\n            const cell = cells.find(c =>\n                isCursorInRect(\n                    c.x + margin.left - c.width / 2, // + offsetX - c.width / 2,\n                    c.y + margin.top - c.height / 2, //+ offsetY - c.height / 2,\n                    c.width,\n                    c.height,\n                    x,\n                    y\n                )\n            )\n            if (cell !== undefined) {\n                setActiveCell(cell)\n                showTooltipFromEvent(createElement(tooltip, { cell }), event)\n            } else {\n                setActiveCell(null)\n                hideTooltip()\n            }\n        },\n        [\n            canvasEl,\n            cells,\n            margin,\n            // offsetX,\n            // offsetY,\n            setActiveCell,\n            showTooltipFromEvent,\n            hideTooltip,\n            tooltip,\n        ]\n    )\n\n    const handleMouseLeave = useCallback(() => {\n        setActiveCell(null)\n        hideTooltip()\n    }, [setActiveCell, hideTooltip])\n\n    const handleClick = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (activeCell === null) return\n\n            onClick?.(activeCell, event)\n        },\n        [activeCell, onClick]\n    )\n\n    return (\n        <canvas\n            ref={mergeRefs(canvasEl, forwardedRef)}\n            width={outerWidth * pixelRatio}\n            height={outerHeight * pixelRatio}\n            style={{\n                width: outerWidth,\n                height: outerHeight,\n            }}\n            onMouseEnter={isInteractive ? handleMouseHover : undefined}\n            onMouseMove={isInteractive ? handleMouseHover : undefined}\n            onMouseLeave={isInteractive ? handleMouseLeave : undefined}\n            onClick={isInteractive ? handleClick : undefined}\n            role={role}\n            aria-label={ariaLabel}\n            aria-labelledby={ariaLabelledBy}\n            aria-describedby={ariaDescribedBy}\n        />\n    )\n}\n\nexport const HeatMapCanvas = forwardRef(\n    <\n        Datum extends HeatMapDatum = DefaultHeatMapDatum,\n        ExtraProps extends object = Record<string, never>,\n    >(\n        {\n            theme,\n            isInteractive = canvasDefaultProps.isInteractive,\n            animate = canvasDefaultProps.animate,\n            motionConfig = canvasDefaultProps.motionConfig,\n            renderWrapper,\n            ...otherProps\n        }: HeatMapCanvasProps<Datum, ExtraProps>,\n        ref: Ref<HTMLCanvasElement>\n    ) => (\n        <Container {...{ isInteractive, animate, motionConfig, theme, renderWrapper }}>\n            <InnerHeatMapCanvas<Datum, ExtraProps>\n                isInteractive={isInteractive}\n                {...otherProps}\n                forwardedRef={ref}\n            />\n        </Container>\n    )\n) as <\n    Datum extends HeatMapDatum = DefaultHeatMapDatum,\n    ExtraProps extends object = Record<string, never>,\n>(\n    props: WithChartRef<HeatMapCanvasProps<Datum, ExtraProps>, HTMLCanvasElement>\n) => ReactElement\n", "import { forwardRef, ReactElement, Ref } from 'react'\nimport { ResponsiveWrapper, ResponsiveProps, WithChartRef } from '@nivo/core'\nimport { DefaultHeatMapDatum, HeatMapCanvasProps, HeatMapDatum } from './types'\nimport { HeatMapCanvas } from './HeatMapCanvas'\n\nexport const ResponsiveHeatMapCanvas = forwardRef(\n    <\n        Datum extends HeatMapDatum = DefaultHeatMapDatum,\n        ExtraProps extends object = Record<string, never>,\n    >(\n        {\n            defaultWidth,\n            defaultHeight,\n            onResize,\n            debounceResize,\n            ...props\n        }: ResponsiveProps<HeatMapCanvasProps<Datum, ExtraProps>>,\n        ref: Ref<HTMLCanvasElement>\n    ) => (\n        <ResponsiveWrapper\n            defaultWidth={defaultWidth}\n            defaultHeight={defaultHeight}\n            onResize={onResize}\n            debounceResize={debounceResize}\n        >\n            {({ width, height }) => (\n                <HeatMapCanvas<Datum, ExtraProps>\n                    width={width}\n                    height={height}\n                    {...props}\n                    ref={ref}\n                />\n            )}\n        </ResponsiveWrapper>\n    )\n) as <\n    Datum extends HeatMapDatum = DefaultHeatMapDatum,\n    ExtraProps extends object = Record<string, never>,\n>(\n    props: WithChartRef<ResponsiveProps<HeatMapCanvasProps<Datum, ExtraProps>>, HTMLCanvasElement>\n) => ReactElement\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAUf,aAAS,WAAW,YAAY,WAAW;AACzC,UAAI,SAAS,CAAC;AACd,eAAS,YAAY,SAAS,OAAO,OAAOA,aAAY;AACtD,YAAI,UAAU,OAAO,OAAOA,WAAU,GAAG;AACvC,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,aAAa;AADjB,QAEI,eAAe;AAFnB,QAGI,UAAU;AA2Cd,aAAS,OAAO,YAAY,WAAW;AACrC,UAAI,OAAO,QAAQ,UAAU,IAAI,cAAc;AAC/C,aAAO,KAAK,YAAY,aAAa,WAAW,CAAC,CAAC;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA;AAAA;;;;;;;;;;;;ACjDjB,uBAAwC;AAKxC,IAAI,iBAAiB;AACrB,SAAS,oBAAoB,MAAM,OAAO;AACxC,MAAI,SAAS,QAAQ,OAAO,UAAU,aAAa,UAAU,GAAI,QAAO;AACxE,MAAI,OAAO,UAAU,YAAY,UAAU,KAAK,CAAC,eAAe,KAAK,IAAI,KAAK,EAAE,iBAAiB,eAAe,IAAI,KAAK,iBAAiB,IAAI;AAC5I,WAAO,QAAQ;AACjB,UAAQ,KAAK,OAAO,KAAK;AAC3B;AACA,IAAI,iBAAiB,CAAC;AACtB,SAAS,oBAAoB,UAAU,OAAO;AAC5C,MAAI,CAAC,SAAS,YAAY,CAAC,SAAS,cAAc;AAChD,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,SAAS,aAAa,YAAY,SAAS,cAAc,SAAS,WAAW,aAAa;AAClH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,IACpC,CAAC,SAAS,mBAAmB,SAAS,aAAa,IAAI,IAAI,OAAO,eAAe,IAAI,MAAM,eAAe,IAAI,IAAI,KAAK;AAAA,MACrH;AAAA;AAAA,MAEA,CAACC,OAAM,MAAMA,GAAE,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,aAAS,cAAc;AAAA,EACzB;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,eAAe,IAAI,GAAG;AAC9B,YAAM,QAAQ,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,iBAAS,MAAM,YAAY,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,iBAAS,MAAM,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,MAAMC,OAAM;AACzB,aAAS,aAAa,MAAM,OAAOA,EAAC,CAAC;AAAA,EACvC,CAAC;AACD,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,eAAe,QAAQ;AACzB,aAAS,aAAa;AAAA,EACxB;AACA,MAAI,YAAY,QAAQ;AACtB,aAAS,aAAa,WAAW,OAAO;AAAA,EAC1C;AACF;AACA,IAAI,mBAAmB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AACA,IAAI,YAAY,CAAC,QAAQ,QAAQ,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AACvF,IAAI,WAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAC1C,mBAAmB,OAAO,KAAK,gBAAgB,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,WAAS,QAAQ,CAAC,WAAW,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AACrE,SAAO;AACT,GAAG,gBAAgB;AAgBnB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,UAAU,CAAC,OAAO,SAAS,GAAG,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,OAAO;AAC7E,IAAI,kBAAkB,CAAC,OAAO,OAAO,GAAG,IAAI,KAAK,IAAI,MAAM,MAAM,CAACC,OAAM,gBAAgBA,IAAG,EAAE,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,KAAK,WAAW,KAAK,MAAM;AACvJ,IAAI,gBAAgB,cAAc,eAAe;AAAA,EAC/C,YAAY,EAAE,GAAAC,IAAG,GAAG,GAAAC,IAAG,GAAG,MAAM,GAAG;AACjC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,CAAC;AACpB,QAAID,MAAK,KAAKC,IAAG;AACf,aAAO,KAAK,CAACD,MAAK,GAAG,KAAK,GAAGC,MAAK,CAAC,CAAC;AACpC,iBAAW,KAAK,CAAC,QAAQ;AAAA,QACvB,eAAe,IAAI,IAAI,CAACF,OAAM,QAAQA,IAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,QAEzD,gBAAgB,KAAK,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AACA,aAAS,OAAO,CAAC,OAAO,QAAQ;AAC9B,UAAI,QAAQ,aAAa;AACvB,eAAO,KAAK,CAAC,SAAS,EAAE,CAAC;AACzB,mBAAW,KAAK,CAAC,cAAc,CAAC,WAAW,cAAc,EAAE,CAAC;AAAA,MAC9D,WAAW,cAAc,KAAK,GAAG,GAAG;AAClC,eAAO,MAAM,GAAG;AAChB,YAAI,GAAG,IAAI,KAAK,EAAG;AACnB,cAAM,OAAO,aAAa,KAAK,GAAG,IAAI,OAAO,cAAc,KAAK,GAAG,IAAI,QAAQ;AAC/E,eAAO,KAAK,QAAQ,KAAK,CAAC;AAC1B,mBAAW;AAAA,UACT,QAAQ,aAAa,CAAC,CAACG,KAAI,IAAIC,KAAI,GAAG,MAAM;AAAA,YAC1C,YAAYD,GAAE,IAAI,EAAE,IAAIC,GAAE,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,YAChD,gBAAgB,KAAK,CAAC;AAAA,UACxB,IAAI,CAAC,UAAU;AAAA,YACb,GAAG,GAAG,IAAI,MAAM,IAAI,CAACJ,OAAM,QAAQA,IAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACtD,gBAAgB,OAAO,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY,IAAI,eAAe,QAAQ,UAAU;AAAA,IACzD;AACA,UAAM,KAAK;AAAA,EACb;AACF;AACA,IAAI,iBAAiB,cAAc,WAAW;AAAA,EAC5C,YAAY,QAAQ,YAAY;AAC9B,UAAM;AACN,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM;AACJ,WAAO,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA,EACA,OAAO;AACL,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,SAAK,KAAK,QAAQ,CAAC,OAAOD,OAAM;AAC9B,YAAM,OAAO,cAAc,MAAM,CAAC,CAAC;AACnC,YAAM,CAACM,IAAG,EAAE,IAAI,KAAK,WAAWN,EAAC;AAAA,QAC/B,GAAG,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,aAAa;AAAA,MAC/C;AACA,mBAAa,MAAMM;AACnB,iBAAW,YAAY;AAAA,IACzB,CAAC;AACD,WAAO,WAAW,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,iBAAiB,OAAO,IAAI;AAAA,QACjE;AAAA,MACF;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,oBAAoB,OAAO,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,uBAAmB,MAAM,KAAK;AAAA,EAChC;AACF;AAGA,IAAI,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,gBAAQ,OAAO;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA;AACF,CAAC;AACD,IAAI,OAAO,WAAW,YAAY;AAAA,EAChC;AAAA,EACA,qBAAqB,CAAC,UAAU,IAAI,cAAc,KAAK;AAAA;AAAA,EAEvD,mBAAmB,CAAC,EAAE,WAAW,YAAY,GAAG,MAAM,MAAM;AAC9D,CAAC;AACD,IAAI,WAAW,KAAK;;;;;;;;;;;;;AC1Xb,IAAMC,IAAe,EACxBC,SAAS,GACTC,WAAW,KACXC,gBAAgB,GAChBC,SAAAA,KAAS;AAJN,ICWMC,KAAY,SAAQC,IAAAA;AAC7B,MAAMC,KAAAA,OAAkBD;AAExB,aACIE,aAAAA,gBAAeF,EAAAA,KACF,aAAbC,MACa,eAAbA,MACa,aAAbA;AAER;ADpBO,ICsBME,IAAe,SAAQH,IAAAA;AAChC,MAAMC,KAAAA,OAAkBD;AAExB,SAAoB,aAAbC,MAAsC,eAAbA;AACpC;AD1BO,IC4BMG,KAAqB,SAC9BC,IAAAA;AAAqC,SACmC,aAAxBA,GAAeC;AAAiB;AD9B7E,ICgCMC,IAAkB,SAC3BF,IAAAA;AAAqC,SACgC,UAAxBA,GAAeC;AAAc;ADlCvE,ICoCME,KAAmB,SAC5BH,IAAAA;AAAqC,SACiC,WAAxBA,GAAeC;AAAe;ADtCzE,IEmBMG,IAAkB,SAAHC,IAAAA;AAAA,MAMxBC,KAAID,GAAJC,MACAC,KAAWF,GAAXE,aACAC,KAAWH,GAAXG,aACAC,KAAaJ,GAAbI;AAAa,SAObF,GAAYG,OAAO,SAACC,IAA+BC,IAAAA;AAC/C,QAAMC,IAASD,GAAWC,UAAU;AAEpC,WAAA,CAAA,EAAAC,OACOH,QACAI,cAAAA,SAAcT,IAAMM,GAAWI,KAAAA,EAAOC,IAAI,SAAAC,IAAAA;AACzC,UAAMC,KAAWX,GAAYU,EAAAA,GACvBE,KAAaX,GAAcS,EAAAA;AAyBjC,cAvBInB,GAAmBa,EAAAA,KAAeT,GAAiBS,EAAAA,OACnDQ,GAAWC,OAAOD,GAAWC,OAAgB,IAATR,GACpCO,GAAWE,QAAQF,GAAWE,QAAiB,IAATT,GACtCO,GAAWG,SAASH,GAAWG,SAAkB,IAATV,IAoB5CW,EAAA,CAAA,OACOC,YAAAA,SAAKb,IAAY,CAAC,SAAS,QAAA,CAAA,GAC3BO,IACAC,IAAU,EACbC,MAAMT,GAAWS,QAAQD,GAAWC,MACpCH,OAAAA,GAAAA,CAAAA;IAEP,CAAA,CAAA;EAIR,GAAE,CAAA,CAAA;AAAG;AF9EH,IEgFMQ,IAAe,SACxBC,IACAC,IACAC,IACAC,IAAAA;AAEA,MAAMC,KAAQC,KAAKC,MAAMH,KAAUF,IAASC,KAAUF,EAAAA;AAEtD,SAAOO,GAAsBC,GAAiBJ,EAAAA,CAAAA;AAClD;AFzFO,IE2FMK,IAAoB,SAC7BxB,IAAAA;AAEA,MASIyB,IACAC,IATAC,KAMA3B,GANA2B,GACAC,KAKA5B,GALA4B,GACAC,KAIA7B,GAJA6B,OACAC,IAGA9B,GAHA8B,OAAKC,IAGL/B,GAFArB,WAAAA,KAAAA,WAASoD,IAAGtD,EAAaE,YAASoD,GAAAC,IAElChC,GADApB,gBAAAA,IAAAA,WAAcoD,IAAGvD,EAAaG,iBAAcoD;AAMhD,UAAIC,gBAAAA,SAASJ,EAAAA,EACTJ,CAAAA,KAAgBE,KAAIE;OACjB;AAAA,QAAA,WAAIA,GAAMK,IAGb,OAAM,IAAIC,MAAK,2EAAA;AAFfV,IAAAA,KAAgBI,GAAMK;EAG1B;AAEA,UAAID,gBAAAA,SAASH,CAAAA,EACTJ,CAAAA,KAAgBE,KAAIE;OACjB;AAAA,QAAA,WAAIA,EAAMI,IAGb,OAAM,IAAIC,MAAK,2EAAA;AAFfT,IAAAA,KAAgBI,EAAMI;EAG1B;AAEA,MAAIE,IAAYT,IACZU,KAAYT,IAEVT,KAAQL,EAAaa,IAAGC,IAAGH,IAAeC,EAAAA;AAEhD,MAAIvC,GAA0Ba,EAAAA,GAAa;AACvC,QAAMO,KAAW+B,GAAkBC,GAAiBpB,EAAAA,GAAQnB,GAAWS,OAAO,CAAA;AAC9E2B,SAAa7B,GAASoB,GACtBU,MAAa9B,GAASqB;EAC1B;AAEA,MAAIrC,GAAwBS,EAAAA,GAAa;AACrC,QAAMwC,KAASpB,KAAKqB,OAAOtB,KAAQ,MAAM,EAAA,IAAM;AAChC,UAAXqB,OACAH,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,IAErB,MAAX8B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAH,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS,IAEtB,MAAX6B,OACAJ,KAAapC,GAAWU,QAAQ,IAErB,MAAX8B,OACAJ,KAAapC,GAAWU,QAAQ,GAChC2B,MAAarC,GAAWW,SAAS;EAEzC;AAEA,MAAI+B,KAAQjB,IAGRkB,KAAYlB;AAUhB,UAPKN,KAAQ,MAAM,MAAM,OACrBuB,MAAS/D,IACTgE,MAAahE,MAEbgE,MAAahE,IAGV,EACHiE,QAAQ,CACJ,CAACR,GAAWC,EAAAA,GACZ,CAACZ,IAAeC,EAAAA,GAChB,CAACiB,IAbSjB,EAAAA,CAAAA,GAedmB,MAAM,CAACH,IAlBGhB,KAAgB9C,CAAAA,GAmB1BuC,OAAOA,KAAQ,GAAA;AAEvB;AF3LO,IGYM2B,IAAiB,SAAHrD,IAAAA;AAAA,MACvBC,KAAID,GAAJC,MACAC,KAAWF,GAAXE,aACAC,KAAWH,GAAXG,aACAC,KAAaJ,GAAbI;AAAa,aAObkD,aAAAA,SACI,WAAA;AAAA,WACIvD,EAAuB,EACnBE,MAAAA,IACAC,aAAAA,IACAC,aAAAA,IACAC,eAAAA,GAAAA,CAAAA;EAER,GAAA,CAACH,IAAMC,IAAaC,IAAaC,EAAAA,CAAAA;AACpC;AHhCE,IGkCMmD,KAAyB,SAAHC,IAAAA;AAAA,MAC/BtD,KAAWsD,GAAXtD;AAAW,aAIXoD,aAAAA,SACI,WAAA;AAAA,WACIpD,GAAYU,IAAI,SAAAL,IAAAA;AAAU,aAAAY,EAAAA,CAAAA,GACnBZ,IAAU,EACbkD,UAAU1B,EAAiBZ,EAAA,CAAA,GACpBZ,EAAAA,CAAAA,EAAAA,CAAAA;IACL,CAAA;EAAA,GAEV,CAACL,EAAAA,CAAAA;AACJ;AHhDE,IGkDMwD,KAAwB,SAAQnD,IAAAA;AAAkC,aAC3E+C,aAAAA,SAAQ,WAAA;AAAA,WAAMvB,EAAyBxB,EAAAA;EAAAA,GAAa,CAACA,EAAAA,CAAAA;AAAY;AHnD9D,IIOMoD,KAAiB,SAAH3D,IAAAA;AAUrB,MATFa,KAAKb,GAALa,OACAqB,KAAClC,GAADkC,GACAC,KAACnC,GAADmC,GACA7C,KAAIU,GAAJV,MAOMsE,IAAQC,EAAAA,GACdC,KAA0CC,GAAAA,GAAlC3E,IAAO0E,GAAP1E,SAAiB4E,KAAYF,GAApBG,QAEXC,KAAgBC,UAAU,EAC5BjC,GAAAA,IACAC,GAAAA,IACA8B,QAAQD,IACRI,WAAAA,CAAYhF,EAAAA,CAAAA;AAGhB,SAAoB,cAAA,OAATE,SACA+E,aAAAA,eAAc/E,IAAM,EAAE4C,GAAAA,IAAGC,GAAAA,IAAGtB,OAAAA,GAAAA,CAAAA,QAInCyD,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,EAAM1D,YAAYkD,KAAKqB,eAAe,SACnCC,mBAAAA,KAACC,SAASvB,MAAI,EACVlB,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjByC,OAAKzD,EAAA,CAAA,GACEyC,EAAM1D,YAAYkD,MAAI,EACzByB,gBAAgB,SAChBC,aAAmD,IAAtClB,EAAM1D,YAAYkD,KAAKqB,cACpCM,QAAQnB,EAAM1D,YAAYkD,KAAK4B,aAAAA,CAAAA,GACjCR,UAEDlF,GAAAA,CAAAA,OAGToF,mBAAAA,KAACC,SAASvB,MAAI,EACVlB,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjByC,WAAOxD,YAAAA,SAAKwC,EAAM1D,YAAYkD,MAAM,CAAC,gBAAgB,cAAA,CAAA,GAAiBoB,UAErElF,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIjB;AJzDO,IKKM2F,IAAiB,SAAHjF,IAAAA;AAMrB,MALFmD,KAAMnD,GAANmD,QAAM+B,KAAAlF,GACNmF,WAAAA,KAAAA,WAASD,MAAQA,IAKXtB,KAAQC,EAAAA,GAERuB,SAAO9B,aAAAA,SAAQ,WAAA;AACjB,QAAO+B,KAA8BlC,GAAM,CAAA;AAE3C,WAFqCA,GAAMmC,MAAA,CAAA,EAExBjF,OACf,SAACC,IAAGkD,IAAAA;AAAO,aAASlD,KAAG,OAAfkD,GAAA,CAAA,IAAqB,MAAlBA,GAAA,CAAA;IAAA,GAAyB,MAChC6B,GAAW,CAAA,IAAA,MAAMA,GAAW,CAAA,CAAA;EAExC,GAAG,CAAClC,EAAAA,CAAAA,GAEEoC,IAAeC,GAAgBJ,EAAAA;AAErC,MAAID,MAAavB,GAAM1D,YAAYuF,KAAKhB,gBAAgB,EACpD,QAAO;AAGX,MAAMG,KAAKzD,EAAA,CAAA,GAAQyC,GAAM1D,YAAYuF,IAAAA;AASrC,SARIN,OACAP,GAAMc,gBAAgB,UACtBd,GAAME,cACFlB,GAAM1D,YAAYuF,KAAKX,cAAoD,IAAtClB,GAAM1D,YAAYuF,KAAKhB,cAChEG,GAAMG,SAASnB,GAAM1D,YAAYuF,KAAKT,cACtCJ,GAAMe,UAAU/B,GAAM1D,YAAYuF,KAAKG,qBAGpClB,mBAAAA,KAACC,SAASS,MAAI,EAACS,MAAK,QAAOC,GAAGP,GAAcX,OAAOA,GAAAA,CAAAA;AAC9D;ALvCO,IMIMmB,IAA0B,SAAH/F,IAAAA;AAA+D,MAAzDkC,KAAClC,GAADkC,GAAGC,KAACnC,GAADmC,GAAGnB,KAAIhB,GAAJgB,MACtC4C,KAAQC,EAAAA,GACdC,KAA0CC,GAAAA,GAAlC3E,KAAO0E,GAAP1E,SAAiB4E,IAAYF,GAApBG,QAEXC,KAAgBC,UAAU,EAC5BjC,GAAAA,IACAC,GAAAA,IACA6D,QAAQhF,KAAO,GACfiD,QAAQD,GACRI,WAAAA,CAAYhF,GAAAA,CAAAA;AAGhB,aACIkF,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,GAAM1D,YAAY+F,QAAQxB,eAAe,SACtCC,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,GAAchC,GAClBkE,IAAIlC,GAAc/B,GAClBkE,GAAGnC,GAAc8B,QACjBpB,OAAKzD,EAAA,CAAA,GACEyC,GAAM1D,YAAY+F,SAAO,EAC5BJ,MAAM,QACNf,aACIlB,GAAM1D,YAAY+F,QAAQnB,cACe,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cAC9BM,QAAQnB,GAAM1D,YAAY+F,QAAQjB,cAClCW,SAAS/B,GAAM1D,YAAY+F,QAAQL,eAAAA,CAAAA,EAAAA,CAAAA,OAI/ClB,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,GAAchC,GAClBkE,IAAIlC,GAAc/B,GAClBkE,GAAGnC,GAAc8B,QACjBpB,OAAOhB,GAAM1D,YAAY+F,QAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzC;AN1CO,IOKMK,IAAuB,SAAHtG,IAAAA;AAQ3B,MAPFkC,KAAClC,GAADkC,GACAC,KAACnC,GAADmC,GAACoE,KAAAvG,GACDgB,MAAAA,KAAAA,WAAIuF,KAAGvH,EAAaC,UAAOsH,IAMrB3C,KAAQC,EAAAA,GACdC,KAA0CC,GAAAA,GAAlC3E,IAAO0E,GAAP1E,SAAiB4E,KAAYF,GAApBG,QAEXC,IAAgBC,UAAU,EAC5BjC,GAAAA,IACAC,GAAAA,IACA6D,QAAQhF,KAAO,GACfiD,QAAQD,IACRI,WAAAA,CAAYhF,EAAAA,CAAAA;AAGhB,aACIkF,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,GAAM1D,YAAY+F,QAAQxB,eAAe,SACtCC,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,EAAchC,GAClBkE,IAAIlC,EAAc/B,GAClBkE,GAAGnC,EAAc8B,QACjBpB,OAAKzD,EAAA,CAAA,GACEyC,GAAM1D,YAAY+F,SAAO,EAC5BJ,MAAM,QACNf,aAAsD,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cACvCM,QAAQnB,GAAM1D,YAAY+F,QAAQjB,cAClCW,SAAS/B,GAAM1D,YAAY+F,QAAQL,eAAAA,CAAAA,EAAAA,CAAAA,OAI/ClB,mBAAAA,KAACC,SAASuB,QAAM,EACZC,IAAIjC,EAAchC,GAClBkE,IAAIlC,EAAc/B,GAClBkE,GAAGnC,EAAc8B,QACjBpB,OAAOhB,GAAM1D,YAAYsG,OAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzC;APjDO,IQIMC,IAAwB,SAAHzG,IAAAA;AAY5B,MAXFkC,KAAClC,GAADkC,GACAC,KAACnC,GAADmC,GACAlB,KAAKjB,GAALiB,OACAC,KAAMlB,GAANkB,QAAMwF,KAAA1G,GACN2G,cAAAA,KAAAA,WAAYD,KAAG,IAACA,IAQV9C,IAAQC,EAAAA,GACdC,KAA0CC,GAAAA,GAAlC3E,IAAO0E,GAAP1E,SAAiB4E,KAAYF,GAApBG,QAEXC,KAAgBC,UAAU,EAC5BjC,GAAGA,KAAIjB,KAAQ,GACfkB,GAAGA,KAAIjB,KAAS,GAChBD,OAAAA,IACAC,QAAAA,IACA+C,QAAQD,IACRI,WAAAA,CAAYhF,EAAAA,CAAAA;AAGhB,aACIkF,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UACKZ,CAAAA,EAAM1D,YAAY+F,QAAQxB,eAAe,SACtCC,mBAAAA,KAACC,SAASiC,MAAI,EACV1E,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjB0E,IAAIF,IACJG,IAAIH,IACJ1F,OAAOiD,GAAcjD,OACrBC,QAAQgD,GAAchD,QACtB0D,OAAKzD,EAAA,CAAA,GACEyC,EAAM1D,YAAY+F,SAAO,EAC5BJ,MAAM,QACNf,aACIlB,EAAM1D,YAAY+F,QAAQnB,cACe,IAAzClB,EAAM1D,YAAY+F,QAAQxB,cAC9BM,QAAQnB,EAAM1D,YAAY+F,QAAQjB,cAClCW,SAAS/B,EAAM1D,YAAY+F,QAAQL,eAAAA,CAAAA,EAAAA,CAAAA,OAI/ClB,mBAAAA,KAACC,SAASiC,MAAI,EACV1E,GAAGgC,GAAchC,GACjBC,GAAG+B,GAAc/B,GACjB0E,IAAIF,IACJG,IAAIH,IACJ1F,OAAOiD,GAAcjD,OACrBC,QAAQgD,GAAchD,QACtB0D,OAAOhB,EAAM1D,YAAY+F,QAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzC;AR7DO,ISSMc,IAAa,SAASxG,IAAAA;AAC/B,MAAQM,KAAsBN,GAAtBM,OAAOqB,KAAe3B,GAAf2B,GAAGC,KAAY5B,GAAZ4B,GAAG7C,KAASiB,GAATjB,MACfmE,KAAWC,GAAsBnD,EAAAA;AAEvC,MAAA,CAAKlB,GAAUC,EAAAA,EACX,OAAM,IAAIoD,MAAM,sCAAA;AAGpB,aACI4B,mBAAAA,MAAAC,mBAAAA,UAAA,EAAAC,UAAA,KACIE,mBAAAA,KAACO,GAAc,EAAC9B,QAAQM,GAASN,QAAQgC,WAAAA,KAAW,CAAA,GACnDzF,GAAmBa,EAAAA,SAChBmE,mBAAAA,KAACqB,GAAuB,EAAC7D,GAAGA,IAAGC,GAAGA,IAAGnB,MAAMT,GAAWS,KAAAA,CAAAA,GAEzDnB,EAAgBU,EAAAA,SACbmE,mBAAAA,KAAC4B,GAAoB,EAACpE,GAAGA,IAAGC,GAAGA,IAAGnB,MAAMT,GAAWS,KAAAA,CAAAA,GAEtDlB,GAAiBS,EAAAA,SACdmE,mBAAAA,KAAC+B,GAAqB,EAClBvE,GAAGA,IACHC,GAAGA,IACHlB,OAAOV,GAAWU,OAClBC,QAAQX,GAAWW,QACnByF,cAAcpG,GAAWoG,aAAAA,CAAAA,OAGjCjC,mBAAAA,KAACO,GAAc,EAAC9B,QAAQM,GAASN,OAAAA,CAAAA,OACjCuB,mBAAAA,KAACf,IAAc,EAAC9C,OAAOA,IAAOqB,GAAGuB,GAASL,KAAK,CAAA,GAAIjB,GAAGsB,GAASL,KAAK,CAAA,GAAI9D,MAAMA,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAG1F;ATvCO,IUID0H,KAAa,SAACC,IAA+B9D,IAAAA;AAC/CA,EAAAA,GAAO+D,QAAQ,SAAAlH,IAASmH,IAAAA;AAAU,QAAjBjF,KAAClC,GAAA,CAAA,GAAEmC,KAACnC,GAAA,CAAA;AACH,UAAVmH,KACAF,GAAIG,OAAOlF,IAAGC,EAAAA,IAEd8E,GAAII,OAAOnF,IAAGC,EAAAA;EAEtB,CAAA;AACJ;AVZO,IUcMmF,IAA4B,SACrCL,IAA6BzD,IAAAA;AAQ5B,MANGtD,KAAWsD,GAAXtD,aACA0D,KAAKJ,GAALI;AAMuB,QAAvB1D,GAAYqH,WAEhBN,GAAIO,KAAAA,GACJtH,GAAYgH,QAAQ,SAAA3G,IAAAA;AAChB,QAAA,CAAKd,EAAac,GAAWjB,IAAAA,EACzB,OAAM,IAAIoD,MAAM,2CAAA;AAGhBkB,IAAAA,GAAM1D,YAAYuF,KAAKhB,eAAe,MACtCwC,GAAIQ,UAAU,UACdR,GAAIS,cAAc9D,GAAM1D,YAAYuF,KAAKT,cACzCiC,GAAIU,YACA/D,GAAM1D,YAAYuF,KAAKX,cAAoD,IAAtClB,GAAM1D,YAAYuF,KAAKhB,cAChEwC,GAAIW,UAAAA,GACJZ,GAAWC,IAAK1G,GAAWkD,SAASN,MAAAA,GACpC8D,GAAIlC,OAAAA,GACJkC,GAAIQ,UAAU,SAGd/H,GAAmBa,EAAAA,KAAeqD,GAAM1D,YAAY+F,QAAQxB,eAAe,MAC3EwC,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQjB,cAC5CiC,GAAIU,YACA/D,GAAM1D,YAAY+F,QAAQnB,cAAuD,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cACtEwC,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIlC,OAAAA,IAGJlF,EAAgBU,EAAAA,KAAeqD,GAAM1D,YAAYsG,OAAO/B,eAAe,MACvEwC,GAAIS,cAAc9D,GAAM1D,YAAYsG,OAAOxB,cAC3CiC,GAAIU,YAAoD,IAAxC/D,GAAM1D,YAAYsG,OAAO/B,cACzCwC,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIlC,OAAAA,IAGJjF,GAAiBS,EAAAA,KAAeqD,GAAM1D,YAAY+F,QAAQxB,eAAe,MACzEwC,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQjB,cAC5CiC,GAAIU,YACA/D,GAAM1D,YAAY+F,QAAQnB,cAAuD,IAAzClB,GAAM1D,YAAY+F,QAAQxB,cACtEwC,GAAIW,UAAAA,GACJX,GAAIL,KACArG,GAAW2B,IAAI3B,GAAWU,QAAQ,GAClCV,GAAW4B,IAAI5B,GAAWW,SAAS,GACnCX,GAAWU,OACXV,GAAWW,MAAAA,GAEf+F,GAAIlC,OAAAA,IAGRkC,GAAIS,cAAc9D,GAAM1D,YAAYuF,KAAKV,QACzCkC,GAAIU,YAAY/D,GAAM1D,YAAYuF,KAAKX,aACvCmC,GAAIW,UAAAA,GACJZ,GAAWC,IAAK1G,GAAWkD,SAASN,MAAAA,GACpC8D,GAAIlC,OAAAA,GAEArF,GAAmBa,EAAAA,MACnB0G,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQlB,QAC5CkC,GAAIU,YAAY/D,GAAM1D,YAAY+F,QAAQnB,aAC1CmC,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIlC,OAAAA,IAGJlF,EAAgBU,EAAAA,MAChB0G,GAAIc,YAAYnE,GAAM1D,YAAYsG,OAAOX,MACzCoB,GAAIW,UAAAA,GACJX,GAAIY,IAAItH,GAAW2B,GAAG3B,GAAW4B,GAAG5B,GAAWS,OAAO,GAAG,GAAG,IAAIW,KAAKmG,EAAAA,GACrEb,GAAIpB,KAAAA,IAGJ/F,GAAiBS,EAAAA,MACjB0G,GAAIS,cAAc9D,GAAM1D,YAAY+F,QAAQlB,QAC5CkC,GAAIU,YAAY/D,GAAM1D,YAAY+F,QAAQnB,aAC1CmC,GAAIW,UAAAA,GACJX,GAAIL,KACArG,GAAW2B,IAAI3B,GAAWU,QAAQ,GAClCV,GAAW4B,IAAI5B,GAAWW,SAAS,GACnCX,GAAWU,OACXV,GAAWW,MAAAA,GAEf+F,GAAIlC,OAAAA,IAGuB,cAAA,OAApBxE,GAAWjB,OAClBiB,GAAWjB,KAAK2H,IAAK,EACjBpG,OAAON,GAAWM,OAClBqB,GAAG3B,GAAWkD,SAASL,KAAK,CAAA,GAC5BjB,GAAG5B,GAAWkD,SAASL,KAAK,CAAA,GAC5BQ,OAAAA,GAAAA,CAAAA,KAGJqD,GAAIe,OAAUpE,GAAM1D,YAAYkD,KAAK6E,WAAAA,QAAcrE,GAAM1D,YAAYkD,KAAK8E,YAC1EjB,GAAIkB,YAAY,QAChBlB,GAAImB,eAAe,cAEnBnB,GAAIc,YAAYnE,GAAM1D,YAAYkD,KAAKyC,MACvCoB,GAAIS,cAAc9D,GAAM1D,YAAYkD,KAAK4B,cACzCiC,GAAIU,YAAkD,IAAtC/D,GAAM1D,YAAYkD,KAAKqB,cAEnCb,GAAM1D,YAAYkD,KAAKqB,eAAe,MACtCwC,GAAIoB,WAAW,SACfpB,GAAIqB,WACA/H,GAAWjB,MACXiB,GAAWkD,SAASL,KAAK,CAAA,GACzB7C,GAAWkD,SAASL,KAAK,CAAA,CAAA,GAE7B6D,GAAIoB,WAAW,UAEnBpB,GAAIsB,SAAShI,GAAWjB,MAAMiB,GAAWkD,SAASL,KAAK,CAAA,GAAI7C,GAAWkD,SAASL,KAAK,CAAA,CAAA;EAE5F,CAAA,GACA6D,GAAIuB,QAAAA;AACR;;;;;;ACvIA,IAAAC,oBAAwC;AAKxC,IAAIC,kBAAiB;AACrB,SAASC,qBAAoB,MAAM,OAAO;AACxC,MAAI,SAAS,QAAQ,OAAO,UAAU,aAAa,UAAU,GAAI,QAAO;AACxE,MAAI,OAAO,UAAU,YAAY,UAAU,KAAK,CAACD,gBAAe,KAAK,IAAI,KAAK,EAAEE,kBAAiB,eAAe,IAAI,KAAKA,kBAAiB,IAAI;AAC5I,WAAO,QAAQ;AACjB,UAAQ,KAAK,OAAO,KAAK;AAC3B;AACA,IAAIC,kBAAiB,CAAC;AACtB,SAASC,qBAAoB,UAAU,OAAO;AAC5C,MAAI,CAAC,SAAS,YAAY,CAAC,SAAS,cAAc;AAChD,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,SAAS,aAAa,YAAY,SAAS,cAAc,SAAS,WAAW,aAAa;AAClH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,IACpC,CAAC,SAAS,mBAAmB,SAAS,aAAa,IAAI,IAAI,OAAOD,gBAAe,IAAI,MAAMA,gBAAe,IAAI,IAAI,KAAK;AAAA,MACrH;AAAA;AAAA,MAEA,CAACE,OAAM,MAAMA,GAAE,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,aAAS,cAAc;AAAA,EACzB;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,eAAe,IAAI,GAAG;AAC9B,YAAM,QAAQJ,qBAAoB,MAAM,MAAM,IAAI,CAAC;AACnD,UAAID,gBAAe,KAAK,IAAI,GAAG;AAC7B,iBAAS,MAAM,YAAY,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,iBAAS,MAAM,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,MAAMM,OAAM;AACzB,aAAS,aAAa,MAAM,OAAOA,EAAC,CAAC;AAAA,EACvC,CAAC;AACD,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,eAAe,QAAQ;AACzB,aAAS,aAAa;AAAA,EACxB;AACA,MAAI,YAAY,QAAQ;AACtB,aAAS,aAAa,WAAW,OAAO;AAAA,EAC1C;AACF;AACA,IAAIJ,oBAAmB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AACA,IAAIK,aAAY,CAAC,QAAQ,QAAQ,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AACvF,IAAIC,YAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAC1CN,oBAAmB,OAAO,KAAKA,iBAAgB,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,EAAAM,UAAS,QAAQ,CAAC,WAAW,IAAID,WAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AACrE,SAAO;AACT,GAAGL,iBAAgB;AAgBnB,IAAIO,iBAAgB;AACpB,IAAIC,gBAAe;AACnB,IAAIC,iBAAgB;AACpB,IAAIC,WAAU,CAAC,OAAO,SAAS,GAAG,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,OAAO;AAC7E,IAAIC,mBAAkB,CAAC,OAAO,OAAO,GAAG,IAAI,KAAK,IAAI,MAAM,MAAM,CAACC,OAAMD,iBAAgBC,IAAG,EAAE,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,KAAK,WAAW,KAAK,MAAM;AACvJ,IAAIC,iBAAgB,cAAc,eAAe;AAAA,EAC/C,YAAY,EAAE,GAAAC,IAAG,GAAG,GAAAC,IAAG,GAAG,MAAM,GAAG;AACjC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,CAAC;AACpB,QAAID,MAAK,KAAKC,IAAG;AACf,aAAO,KAAK,CAACD,MAAK,GAAG,KAAK,GAAGC,MAAK,CAAC,CAAC;AACpC,iBAAW,KAAK,CAAC,QAAQ;AAAA,QACvB,eAAe,IAAI,IAAI,CAACH,OAAMF,SAAQE,IAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,QAEzDD,iBAAgB,KAAK,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AACA,aAAS,OAAO,CAAC,OAAO,QAAQ;AAC9B,UAAI,QAAQ,aAAa;AACvB,eAAO,KAAK,CAAC,SAAS,EAAE,CAAC;AACzB,mBAAW,KAAK,CAAC,cAAc,CAAC,WAAW,cAAc,EAAE,CAAC;AAAA,MAC9D,WAAWJ,eAAc,KAAK,GAAG,GAAG;AAClC,eAAO,MAAM,GAAG;AAChB,YAAI,GAAG,IAAI,KAAK,EAAG;AACnB,cAAM,OAAOC,cAAa,KAAK,GAAG,IAAI,OAAOC,eAAc,KAAK,GAAG,IAAI,QAAQ;AAC/E,eAAO,KAAK,QAAQ,KAAK,CAAC;AAC1B,mBAAW;AAAA,UACT,QAAQ,aAAa,CAAC,CAACO,KAAI,IAAIC,KAAI,GAAG,MAAM;AAAA,YAC1C,YAAYD,GAAE,IAAI,EAAE,IAAIC,GAAE,IAAIP,SAAQ,KAAK,IAAI,CAAC;AAAA,YAChDC,iBAAgB,KAAK,CAAC;AAAA,UACxB,IAAI,CAAC,UAAU;AAAA,YACb,GAAG,GAAG,IAAI,MAAM,IAAI,CAACC,OAAMF,SAAQE,IAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACtDD,iBAAgB,OAAO,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY,IAAIO,gBAAe,QAAQ,UAAU;AAAA,IACzD;AACA,UAAM,KAAK;AAAA,EACb;AACF;AACA,IAAIA,kBAAiB,cAAc,WAAW;AAAA,EAC5C,YAAY,QAAQ,YAAY;AAC9B,UAAM;AACN,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM;AACJ,WAAO,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA,EACA,OAAO;AACL,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,SAAK,KAAK,QAAQ,CAAC,OAAOd,OAAM;AAC9B,YAAM,OAAO,cAAc,MAAM,CAAC,CAAC;AACnC,YAAM,CAACe,IAAG,EAAE,IAAI,KAAK,WAAWf,EAAC;AAAA,QAC/B,GAAG,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,aAAa;AAAA,MAC/C;AACA,mBAAa,MAAMe;AACnB,iBAAW,YAAY;AAAA,IACzB,CAAC;AACD,WAAO,WAAW,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,iBAAiB,OAAO,IAAI;AAAA,QACjE;AAAA,MACF;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,oBAAoB,OAAO,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,uBAAmB,MAAM,KAAK;AAAA,EAChC;AACF;AAGA,IAAIC,cAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,gBAAQ,OAAO;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA;AACF,CAAC;AACD,IAAIC,QAAO,WAAWD,aAAY;AAAA,EAChC,qBAAAlB;AAAA,EACA,qBAAqB,CAAC,UAAU,IAAIW,eAAc,KAAK;AAAA;AAAA,EAEvD,mBAAmB,CAAC,EAAE,WAAW,YAAY,GAAG,MAAM,MAAM;AAC9D,CAAC;AACD,IAAIS,YAAWD,MAAK;;;;;;;;;;;;;;;;;;;;;ACtXpB,ICDaE,IAaT,EACAC,QAAQ,CAAC,QAAQ,QAAQ,SAAS,WAAW,aAAA,GAE7CC,aAAAA,OACAC,eAAe,GACfC,eAAe,GACfC,eAAe,GACfC,eAAe,GACfC,eAAAA,OAEAC,SAAS,GACTC,eAAe,GACfC,iBAAiB,MACjBC,aAAa,GACbC,aAAa,EAAEC,MAAM,SAASC,WAAW,CAAC,CAAC,UAAU,GAAA,CAAA,EAAA,GAErDC,aAAAA,OACAC,aAAAA,OAEAC,cAAAA,MACAC,OAAO,kBACPC,gBAAgB,EAAEN,MAAM,SAASC,WAAW,CAAC,CAAC,UAAU,CAAA,CAAA,EAAA,GAExDM,QAAQ,EACJC,MAAM,cACNC,QAAQ,kBAAA,GAEZC,YAAY,WAEZC,SAAS,CAAA,GACTC,aAAa,CAAA,GAEbC,eAAAA,MACAC,aAAa,aACbC,aDjC0BC,cAAAA,MAbI,SAAHC,IAAAA;AAAkE,MAAhCC,KAAID,GAAJC;AAC7D,SAA4B,SAAxBA,GAAKC,iBAAgC,WAGrCC,oBAAAA,KAACC,GAAY,EACTC,IAAOJ,GAAKK,UAAAA,QAAaL,GAAKM,KAAKC,GACnCC,OAAOR,GAAKC,gBACZQ,YAAAA,MACAC,OAAOV,GAAKU,MAAAA,CAAAA;AAGxB,CAAA,GCqCIC,SAAAA,MACAC,cAAc,SAAA;ADjDlB,ICoDaC,IAAeC,EAAAA,CAAAA,GACrB7C,GAAkB,EACrB8C,SAAS,CAAE,GACXC,WAAW,MACXC,YAAY,MACZC,UAAU,CAAE,GACZC,cAAc,GACdC,eAAe,OAAA,CAAA;AD3DnB,IC8DaC,IAAkBP,EAAAA,CAAAA,GACxB7C,GAAkB,EACrB8C,SAAS,CAAE,GACXC,WAAW,MACXC,YAAY,MACZC,UAAU,CAAE,GACZI,YAAY,QACZC,YAA8B,eAAA,OAAXC,UAAyBA,OAAOC,oBAAwB,EAAA,CAAA;ADrE/E,IEMaC,IAAgB,SAAH3B,IAAAA;AAYpB,MAXK4B,KAAM5B,GAAb6B,OACQC,KAAO9B,GAAf+B,QACAC,KAAIhC,GAAJgC,MACAC,KAAOjC,GAAPiC,SASIJ,KAAQD,IACRG,KAASD,IAETI,KAAU,GACVC,KAAU;AAEd,MAdWnC,GAAX5B,aAciB;AACb,QAAMgE,IAAYC,KAAKC,IAAIV,KAASK,IAAS,CAAA,GACvCM,IAAaF,KAAKC,IAAIR,KAAUE,IAAM,CAAA,GACtCQ,IAAWH,KAAKI,IAAIL,GAAWG,CAAAA;AAKrCL,IAAAA,MAAWN,MAHXC,KAAQW,IAAWP,OAGU,GAC7BE,MAAWL,MAHXC,KAASS,IAAWR,OAGW;EACnC;AAEA,SAAO,EACHE,SAAAA,IACAC,SAAAA,IACAN,OAAAA,IACAE,QAAAA,GAAAA;AAER;AF3CA,IE6CaW,IAAe,SAAHC,IAAAA;AAgBnB,MAfFpC,KAAIoC,GAAJpC,MACOqB,KAAMe,GAAbd,OACQC,KAAOa,GAAfZ,QACA1D,KAAasE,GAAbtE,eACAC,KAAaqE,GAAbrE,eACAC,KAAaoE,GAAbpE,eACAC,KAAamE,GAAbnE,eACAJ,KAAWuE,GAAXvE,aASMwE,IAAa,oBAAIC,OACjBC,IAAqB,CAAA,GACrBC,IAAsB,CAAA,GAEtBC,IAA0E,CAAA;AAEhFzC,EAAAA,GAAK0C,QAAQ,SAAAC,IAAAA;AACTJ,MAASK,KAAKD,GAAM7C,EAAAA,GAEpB6C,GAAM3C,KAAK0C,QAAQ,SAAAG,IAAAA;AACfR,QAAWS,IAAID,GAAM5C,CAAAA;AAErB,UAAIC,KAAuB;AAAA,iBACvB2C,GAAME,KAA+B,SAAZF,GAAME,MAC/BP,EAAUI,KAAKC,GAAME,CAAAA,GACrB7C,KAAQ2C,GAAME,IAGlBN,EAAMG,KAAK,EACP9C,IAAO6C,GAAM7C,KAAM+C,MAAAA,GAAM5C,GACzBF,SAAS4C,GAAM7C,IACfI,OAAAA,IACAF,MAAM6C,GAAAA,CAAAA;IAEd,CAAA;EACJ,CAAA;AAEA,MAAMG,IAAUC,MAAMzE,KAAK6D,CAAAA,GAE3Ba,KAA4C9B,EAAc,EACtDE,OAAOD,IACPG,QAAQD,IACRG,SAASsB,EAAQG,QACjB1B,MAAMc,EAASY,QACftF,aAAAA,GAAAA,CAAAA,GALIyD,KAAK4B,GAAL5B,OAAOE,KAAM0B,GAAN1B,QAAQG,KAAOuB,GAAPvB,SAASC,KAAOsB,GAAPtB,SAQ1BwB,IAASC,GACXC,KAAAA,EACKC,OAAOP,CAAAA,EACPQ,MAAM,CAAC,GAAGlC,EAAAA,CAAAA,EACVmC,aAAa1F,EAAAA,EACb2F,aAAa5F,EAAAA,CAAAA,GAGhB6F,KAASN,GACXC,KAAAA,EACKC,OAAOhB,CAAAA,EACPiB,MAAM,CAAC,GAAGhC,EAAAA,CAAAA,EACViC,aAAaxF,EAAAA,EACbyF,aAAa1F,EAAAA,CAAAA,GAGhB6D,KAAYuB,EAAOQ,UAAAA,GACnB5B,KAAa2B,GAAOC,UAAAA,GAEpBC,KAGApB,EAAMqB,IAAI,SAAApE,IAAAA;AAAI,WAAAc,EAAAA,CAAAA,GACbd,IAAI,EACPO,GAAGmD,EAAO1D,GAAKM,KAAKC,CAAAA,IAAM4B,KAAY,GACtCkB,GAAGY,GAAOjE,GAAKK,OAAAA,IAAYiC,KAAa,GACxCV,OAAOO,IACPL,QAAQQ,GAAAA,CAAAA;EAAU,CAAA;AAGtB,SAAO,EACHV,OAAAA,IACAE,QAAAA,IACAG,SAAAA,IACAC,SAAAA,IACAwB,QAAAA,GACAO,QAAAA,IACAI,UAAUjC,KAAKI,IAAG8B,MAARlC,MAAYU,CAAAA,GACtByB,UAAUnC,KAAKC,IAAGiC,MAARlC,MAAYU,CAAAA,GACtBC,OAAOoB,GAAAA;AAEf;AF5IA,IE8IaK,IAAmB,SAC5BC,IACAjC,IACAH,IAAAA;AAEA,MAAA,CAAKoC,GAAM,QAAO,WAAA;AAAA,WAAM;EAAC;AAEzB,MAAMC,KAAQC,OAAAA,EACTd,OAAOY,GAAKG,SAASH,GAAKG,SAAS,CAACpC,IAAKH,EAAAA,CAAAA,EACzCyB,MAAMW,GAAKI,KAAAA;AAEhB,SAAO,SAACrE,IAAAA;AACJ,WAAc,SAAVA,KAAuB,IACpBkE,GAAMlE,EAAAA;EAAAA;AAErB;AF7JA,IE+JasE,KAA4B,SACrC9E,IAAAA;AAAyB,SACvB,EACFO,GAAGP,GAAKO,GACR8C,GAAGrD,GAAKqD,EAAAA;AACX;AFpKD,IEsKa0B,KAA8B,SACvC/E,IAAAA;AAAyB,SACvB,EACFyE,MAAMrC,KAAKC,IAAIrC,GAAK4B,OAAO5B,GAAK8B,MAAAA,GAChCF,OAAO5B,GAAK4B,OACZE,QAAQ9B,GAAK8B,OAAAA;AAChB;AF5KD,IGiBakD,KAAkB,SAAHjF,IAAAA;AAAA,MACxBO,KAAIP,GAAJO,MACAsB,KAAK7B,GAAL6B,OACAE,KAAM/B,GAAN+B,QACA1D,KAAa2B,GAAb3B,eACAC,KAAa0B,GAAb1B,eACAC,KAAayB,GAAbzB,eACAC,KAAawB,GAAbxB,eACAJ,IAAW4B,GAAX5B;AAAW,aASX8G,cAAAA,SACI,WAAA;AAAA,WACIxC,EAAgC,EAC5BnC,MAAAA,IACAsB,OAAAA,IACAE,QAAAA,IACA1D,eAAAA,IACAC,eAAAA,IACAC,eAAAA,IACAC,eAAAA,IACAJ,aAAAA,EAAAA,CAAAA;EACF,GACN,CACImC,IACAsB,IACAE,IACA1D,IACAC,IACAC,IACAC,IACAJ,CAAAA,CAAAA;AAEP;AHxDL,IG0DM+G,KAAsB,EACxBlF,MAAM,SACFA,IAIAmF,IAAAA;AAA4B,SAC3BnF,GAAKI,OAAO+E,GAAQ/E;AAAE,GAC3BgF,KAAK,SACDpF,IAIAmF,IAAAA;AAA4B,SAC3BnF,GAAKK,YAAY8E,GAAQ9E;AAAO,GACrCgF,QAAQ,SACJrF,IAIAmF,IAAAA;AAA4B,SAC3BnF,GAAKM,KAAKC,MAAM4E,GAAQ7E,KAAKC;AAAC,GACnC+E,WAAW,SACPtF,IAIAmF,IAAAA;AAA4B,SAC3BnF,GAAKK,YAAY8E,GAAQ9E,WAAWL,GAAKM,KAAKC,MAAM4E,GAAQ7E,KAAKC;AAAC,EAAA;AHtF3E,IGgGMgF,KAAgB,SAAH7C,IAAAA;AAmCb,MAzCF+B,IACAjC,IACAH,IAKAU,KAAKL,GAALK,OACAsB,KAAQ3B,GAAR2B,UACAE,KAAQ7B,GAAR6B,UACA/F,IAAakE,GAAblE,eACAa,IAAMqD,GAANrD,QACAG,IAAUkD,GAAVlD,YACAf,KAAOiE,GAAPjE,SACAC,KAAagE,GAAbhE,eACAC,KAAe+D,GAAf/D,iBACAE,KAAW6D,GAAX7D,aACAM,KAAKuD,GAALvD,OACAC,IAAcsD,GAAdtD,gBACAoG,KAAW9C,GAAX8C,aACAC,KAAS/C,GAAT+C,WAsBMC,SAtCNT,cAAAA,SAAQ,WAAA;AAAA,WAAMT,EAAiBC,IAAMjC,IAAKH,EAAAA;EAAI,GAAE,CAJhDoC,KA0C6BjG,GAzC7BgE,KAyC4C6B,IAxC5ChC,KAwCsDkC,EAAAA,CAAAA,GAEhDoB,SAAaV,cAAAA,SAAQ,WAAA;AACvB,WAAsB,cAAA,OAAX5F,IAA8B,OAElCuG,GAAwBvG,GAAQ,EACnCmD,KAAK6B,IACLhC,KAAKkC,GAAAA,CAAAA;EAEZ,GAAE,CAAClF,GAAQgF,IAAUE,EAAAA,CAAAA,GAEhBsB,SAAWC,cAAAA,aACb,SAAC9F,IAAAA;AACG,QAAmB,SAAfA,GAAKQ,OAAgB;AACrB,UAAsB,cAAA,OAAXnB,EAAuB,QAAOA,EAAOW,EAAAA;AAChD,UAAmB,SAAf2F,GAAqB,QAAOA,GAAW3F,GAAKQ,KAAAA;IACpD;AAEA,WAAOhB;EACV,GACD,CAACH,GAAQsG,IAAYnG,CAAAA,CAAAA,GAEnBuG,KAAQC,EAAAA,GACRC,KAAiBC,GAAkBrH,IAAakH,EAAAA,GAChDI,KAAoBD,GAAkB9G,GAAgB2G,EAAAA,GAEtDK,KAAcC,GAAkBb,EAAAA,GAChCc,KAAWC,GAAoBpH,EAAAA;AA0CrC,SAAO,EACH4D,WAzCgBkC,cAAAA,SAChB,WAAA;AAAA,WACIlC,GAAMqB,IAAI,SAAApE,IAAAA;AACN,UAAIwG,KAAkB/H;AAClBgH,MAAAA,GAAUhC,SAAS,MACnB+C,KAAkBf,GAAUgB,SAASzG,GAAKI,EAAAA,IAAM1B,KAAgBC;AAGpE,UAAM+H,KAAiBhB,GAAQ1F,GAAKQ,KAAAA,GAE9BmG,KAAY7F,EAAA,CAAA,GACXd,IAAI,EACP4B,OAAO5B,GAAK4B,QAAQ8E,IACpB5E,QAAQ9B,GAAK8B,SAAS4E,IACtBzG,gBAA+B,SAAfD,GAAKQ,QAAiB4F,GAAYpG,GAAKQ,KAAAA,IAAS,MAChE/B,SAAS+H,GAAAA,CAAAA;AAQb,aALAG,GAAaxH,QAAQmH,GAASK,EAAAA,GAC9BA,GAAajG,QAAQmF,GAASc,EAAAA,GAC9BA,GAAa9H,cAAcoH,GAAeU,EAAAA,GAC1CA,GAAavH,iBAAiB+G,GAAkBQ,EAAAA,GAEzCA;IACX,CAAA;EACJ,GAAA,CACI5D,IACA2C,IACAG,IACAI,IACAE,IACAC,IACAE,IACAb,IACAhH,IACAC,IACAC,EAAAA,CAAAA,GAMJgH,YAAAA,GAAAA;AAER;AH7MA,IG+MaiB,KAAa,SAAHC,IAAAA;AA+CjB,MA3CFvG,KAAIuG,GAAJvG,MACAkF,KAAWqB,GAAXrB,aACO7D,KAAMkF,GAAbjF,OACQC,KAAOgF,GAAf/E,QAAMgF,KAAAD,GACNxI,eAAAA,KAAAA,WAAayI,KAAG7I,EAAmBI,gBAAayI,IAAAC,IAAAF,GAChDzI,eAAAA,IAAAA,WAAa2I,IAAG9I,EAAmBG,gBAAa2I,GAAAC,IAAAH,GAChDtI,eAAAA,IAAAA,WAAayI,IAAG/I,EAAmBM,gBAAayI,GAAAC,IAAAJ,GAChDvI,eAAAA,KAAAA,WAAa2I,IAAGhJ,EAAmBK,gBAAa2I,GAAAC,KAAAL,GAChD1I,aAAAA,KAAAA,WAAW+I,KAAGjJ,EAAmBE,cAAW+I,IAAAC,KAAAN,GAC5CrI,eAAAA,KAAAA,WAAa2I,KAAGlJ,EAAmBO,gBAAa2I,IAAAC,IAAAP,GAChDxH,QAAAA,KAAAA,WAAM+H,IAAGnJ,EAAmBoB,SAAM+H,GAAAC,KAAAR,GAClCrH,YAAAA,KAAAA,WAAU6H,KAAGpJ,EAAmBuB,aAAU6H,IAAAC,KAAAT,GAC1CpI,SAAAA,KAAAA,WAAO6I,KAAGrJ,EAAmBQ,UAAO6I,IAAAC,KAAAV,GACpCnI,eAAAA,KAAAA,WAAa6I,KAAGtJ,EAAmBS,gBAAa6I,IAAAC,KAAAX,GAChDlI,iBAAAA,KAAAA,WAAe6I,KAAGvJ,EAAmBU,kBAAe6I,IAAAC,KAAAZ,GACpDhI,aAAAA,KAAAA,WAAW4I,KAAGxJ,EAAmBY,cAAW4I,IAAAC,KAAAb,GAC5C1H,OAAAA,KAAAA,WAAKuI,KAAGzJ,EAAmBkB,QAAKuI,IAAAC,KAAAd,GAChCzH,gBAAAA,KAAAA,WAAcuI,KAAG1J,EAAmBmB,iBAAcuI,IAAAC,KAAAf,GAClDjH,aAAAA,KAAAA,WAAWgI,KAAG3J,EAAmB2B,cAAWgI,IA0B5CC,SAAoCC,cAAAA,UAAqC,IAAA,GAAlEC,IAAUF,GAAA,CAAA,GAAEG,IAAaH,GAAA,CAAA,GAEhCI,IACIjD,GAAmC,EAC/B1E,MAAAA,IACAsB,OAAOD,IACPG,QAAQD,IACRxD,eAAAA,IACAD,eAAAA,GACAG,eAAAA,GACAD,eAAAA,IACAH,aAAAA,GAAAA,CAAAA,GATAyD,KAAKqG,EAALrG,OAAOE,IAAMmG,EAANnG,QAAQG,IAAOgG,EAAPhG,SAASC,KAAO+F,EAAP/F,SAASa,KAAKkF,EAALlF,OAAOW,KAAMuE,EAANvE,QAAQO,KAAMgE,EAANhE,QAAQI,KAAQ4D,EAAR5D,UAAUE,KAAQ0D,EAAR1D,UAYpEkB,SAAYR,cAAAA,SAAQ,WAAA;AACtB,QAAA,CAAK8C,EAAY,QAAO,CAAA;AAExB,QAAMG,KAAgBhD,GAAoBtF,EAAAA;AAE1C,WAAOmD,GAAMoF,OAAO,SAAAnI,IAAAA;AAAI,aAAIkI,GAAclI,IAAM+H,CAAAA;IAAW,CAAA,EAAE3D,IAAI,SAAApE,IAAAA;AAAI,aAAIA,GAAKI;IAAAA,CAAAA;EACjF,GAAE,CAAC2C,IAAOgF,GAAYnI,EAAAA,CAAAA,GAEvBwI,KAA6C7C,GAAqB,EAC9DxC,OAAAA,IACAsB,UAAAA,IACAE,UAAAA,IACA/F,eAAAA,IACAa,QAAAA,IACAG,YAAAA,IACAf,SAAAA,IACAC,eAAAA,IACAC,iBAAAA,IACAE,aAAAA,IACAM,OAAAA,IACAC,gBAAAA,IACAoG,aAAAA,IACAC,WAAAA,GAAAA,CAAAA;AAGJ,SAAO,EACH7D,OAAAA,IACAE,QAAAA,GACAG,SAAAA,GACAC,SAAAA,IACAa,OAtBwBqF,GAApBrF,OAuBJW,QAAAA,IACAO,QAAAA,IACA0B,YAzBoCyC,GAAVzC,YA0B1BoC,YAAAA,GACAC,eAAAA,EAAAA;AAER;AHlTA,IGoTaK,KAAqB,SAC9BtF,IACArD,IAAAA;AAAqD,SAErD4I,EAAoC,EAChChI,MAAMyC,IACNrD,aAAAA,IACA6I,aAAazD,IACb0D,eAAezD,GAAAA,CAAAA;AACjB;AH7TN,IIoEa0D,SAAkB3I,cAAAA,MAlEI,SAAHC,IAAAA;AAUC,MAT7BC,KAAID,GAAJC,MACApB,KAAWmB,GAAXnB,aACAuC,KAAYpB,GAAZoB,cACAuH,KAAa3I,GAAb2I,eACAC,KAAY5I,GAAZ4I,cACAC,KAAW7I,GAAX6I,aACAC,KAAY9I,GAAZ8I,cACAC,IAAO/I,GAAP+I,SACA5J,IAAYa,GAAZb,cAEM6G,IAAQC,EAAAA,GAER+C,QAAW9D,cAAAA,SACb,WAAA;AAAA,WAAO,EACH0D,cAAcA,KAAeA,GAAa3I,EAAAA,IAAAA,QAC1C4I,aAAaA,KAAcA,GAAY5I,EAAAA,IAAAA,QACvC6I,cAAcA,KAAeA,GAAa7I,EAAAA,IAAAA,QAC1C8I,SAASA,IAAUA,EAAQ9I,EAAAA,IAAAA,OAAQgJ;EACtC,GACD,CAAChJ,IAAM2I,IAAcC,IAAaC,IAAcC,CAAAA,CAAAA;AAGpD,aACIG,oBAAAA,MAACC,UAASC,GAACrI,EAAA,EACP,eAAqBd,UAAAA,GAAKI,IAC1BgJ,OAAO,EAAEC,QAAQ,UAAA,GACjB5K,SAASiK,GAAcjK,QAAAA,GACnBsK,GAAQ,EACZO,WAAWC,GACP,CAACb,GAAcnI,GAAGmI,GAAcrF,GAAGqF,GAAchE,KAAAA,GACjD,SAACnE,IAAG8C,IAAGqB,IAAAA;AAAK,WAAA,eAAkBnE,KAAC,OAAK8C,KAAC,aAAWqB,KAAK;EAAA,CAAA,GACvD8E,UAEFtJ,KAAAA,oBAAAA,KAACgJ,UAASO,MAAI,EACVH,WAAWC,GACP,CAACb,GAAc9G,OAAO8G,GAAc5G,MAAAA,GACpC,SAACF,IAAOE,IAAAA;AAAM,WAAkBF,eAAAA,OAAAA,KAAAA,OAAAA,OAAiBE,KAAa;EAAA,CAAA,GAGlE4H,MAAMhB,GAAchI,OACpBkB,OAAO8G,GAAc9G,OACrBE,QAAQ4G,GAAc5G,QACtB6H,QAAQjB,GAAc7J,aACtB+K,aAAahL,IACbiL,IAAI1I,IACJ2I,IAAI3I,GAAAA,GAPCnB,GAAKI,EAAAA,GASblB,SACGgB,oBAAAA,KAAC6J,GAAI,EACDC,YAAW,UACXC,kBAAiB,WACjBP,MAAMhB,GAActJ,gBACpBgK,OAAKtI,EAAA,CAAA,GACEiF,EAAMmE,OAAOC,MAAI,EACpBT,MAAAA,QACAU,YAAY,OAAA,CAAA,GACdZ,UAEDxJ,GAAKb,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,CAAAA;AAK1B,CAAA;AJlEA,IK2DakL,SAAoBvK,cAAAA,MAzDI,SAAHC,IAAAA;AASD,MAR7BC,KAAID,GAAJC,MACApB,KAAWmB,GAAXnB,aACA8J,KAAa3I,GAAb2I,eACAC,KAAY5I,GAAZ4I,cACAC,KAAW7I,GAAX6I,aACAC,KAAY9I,GAAZ8I,cACAC,KAAO/I,GAAP+I,SACA5J,IAAYa,GAAZb,cAEM6G,IAAQC,EAAAA,GAER+C,QAAW9D,cAAAA,SACb,WAAA;AAAA,WAAO,EACH0D,cAAcA,KAAeA,GAAa3I,EAAAA,IAAAA,QAC1C4I,aAAaA,KAAcA,GAAY5I,EAAAA,IAAAA,QACvC6I,cAAcA,KAAeA,GAAa7I,EAAAA,IAAAA,QAC1C8I,SAASA,KAAUA,GAAQ9I,EAAAA,IAAAA,OAAQgJ;EACtC,GACD,CAAChJ,IAAM2I,IAAcC,IAAaC,IAAcC,EAAAA,CAAAA;AAGpD,aACIG,oBAAAA,MAACC,UAASC,GAACrI,EAAA,EACP,eAAqBd,UAAAA,GAAKI,IAC1BgJ,OAAO,EAAEC,QAAQ,UAAA,GACjB5K,SAASiK,GAAcjK,QAAAA,GACnBsK,GAAQ,EACZO,WAAWC,GAAG,CAACb,GAAcnI,GAAGmI,GAAcrF,CAAAA,GAAI,SAAC9C,IAAG8C,IAAAA;AAAC,WAAkB9C,eAAAA,KAAAA,OAAM8C,KAAC;EAAA,CAAA,GAAKmG,UAErFtJ,KAAAA,oBAAAA,KAACgJ,UAASoB,QAAM,EACZC,GAAGhB,GACC,CAACb,GAAc9G,OAAO8G,GAAc5G,MAAAA,GACpC,SAACF,IAAOE,IAAAA;AAAM,WAAKM,KAAKI,IAAIZ,IAAOE,EAAAA,IAAU;EAAC,CAAA,GAElD4H,MAAMhB,GAAchI,OACpB8J,aAAa9B,GAAcjK,SAC3BmL,aAAahL,IACb+K,QAAQjB,GAAc7J,YAAAA,CAAAA,GAEzBK,SACGgB,oBAAAA,KAAC6J,GAAI,EACDE,kBAAiB,WACjBD,YAAW,UACXN,MAAMhB,GAActJ,gBACpBgK,OAAKtI,EAAA,CAAA,GACEiF,EAAMmE,OAAOC,MAAI,EACpBT,MAAAA,OAAMV,CAAAA,GACRQ,UAEDxJ,GAAKb,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,CAAAA;AAK1B,CAAA;ALzDA,IMyBMsL,KAAkB,SAA6BzK,IAAAA;AAAyB,SAAM,EAChFO,GAAGP,GAAKO,GACR8C,GAAGrD,GAAKqD,GACRzB,OAAO5B,GAAK4B,OACZE,QAAQ9B,GAAK8B,QACbpB,OAAOV,GAAKU,OACZjC,SAAS,GACTI,aAAamB,GAAKnB,aAClBO,gBAAgBY,GAAKZ,gBACrBsF,OAAO,EAAA;AACV;ANnCD,IMqCMgG,KAAoB,SAA6B1K,IAAAA;AAAyB,SAAM,EAClFO,GAAGP,GAAKO,GACR8C,GAAGrD,GAAKqD,GACRzB,OAAO5B,GAAK4B,OACZE,QAAQ9B,GAAK8B,QACbpB,OAAOV,GAAKU,OACZjC,SAASuB,GAAKvB,SACdI,aAAamB,GAAKnB,aAClBO,gBAAgBY,GAAKZ,gBACrBsF,OAAO,EAAA;AACV;AN/CD,IMiDMiG,KAAiB,SAA6B3K,IAAAA;AAAyB,SAAM,EAC/EO,GAAGP,GAAKO,GACR8C,GAAGrD,GAAKqD,GACRzB,OAAO5B,GAAK4B,OACZE,QAAQ9B,GAAK8B,QACbpB,OAAOV,GAAKU,OACZjC,SAAS,GACTI,aAAamB,GAAKnB,aAClBO,gBAAgBY,GAAKZ,gBACrBsF,OAAO,EAAA;AACV;AN3DD,IM6DakG,KAAe,SAAH7K,IAAAA;AAamB,MAqDpC8K,IAjEJ9H,KAAKhD,GAALgD,OACA3B,KAAarB,GAAbqB,eACAD,KAAYpB,GAAZoB,cACAvC,KAAWmB,GAAXnB,aACAe,KAAaI,GAAbJ,eACAqI,IAAajI,GAAbiI,eACAW,IAAY5I,GAAZ4I,cACAC,IAAW7I,GAAX6I,aACAC,IAAY9I,GAAZ8I,cACAC,KAAO/I,GAAP+I,SACAjJ,KAAOE,GAAPF,SACAX,KAAYa,GAAZb,cAEA4L,KAA0CC,GAAAA,GAAlCpK,KAAOmK,GAAPnK,SAAiBqK,IAAYF,GAApBG,QAEXC,KAAaC,cAAsDpI,IAAO,EAC5EqI,MAAM,SAACpL,IAAAA;AAAyB,WAAKA,GAAKI;EAAE,GAC5CiL,SAASX,IACT5L,MAAM2L,IACNa,OAAOZ,IACPa,QAAQb,IACRc,OAAOb,IACPM,QAAQD,GACRS,WAAAA,CAAY9K,GAAAA,CAAAA,GAGhB+K,KAA8CC,EAAAA,GAAtCC,KAAoBF,GAApBE,sBAAsBC,KAAWH,GAAXG,aAExBC,SAAmB7G,cAAAA,SAAQ,WAAA;AAC7B,QAAKtF,GAEL,QAAO,SAACK,IAAAA;AAAyB,aAAK,SAAC+L,IAAAA;AACnCH,QAAAA,OAAqBI,cAAAA,eAAcnM,IAAS,EAAEG,MAAAA,GAAAA,CAAAA,GAAS+L,EAAAA,GACvD/D,EAAchI,EAAAA,GAAAA,QACd2I,KAAAA,EAAe3I,IAAM+L,EAAAA;MAAAA;IACxB;EACL,GAAG,CAACpM,IAAeiM,IAAsB/L,IAASmI,GAAeW,CAAAA,CAAAA,GAE3DsD,SAAkBhH,cAAAA,SAAQ,WAAA;AAC5B,QAAKtF,GAEL,QAAO,SAACK,IAAAA;AAAyB,aAAK,SAAC+L,IAAAA;AACnCH,QAAAA,OAAqBI,cAAAA,eAAcnM,IAAS,EAAEG,MAAAA,GAAAA,CAAAA,GAAS+L,EAAAA,GAAAA,QACvDnD,KAAAA,EAAc5I,IAAM+L,EAAAA;MAAAA;IACvB;EACJ,GAAE,CAACpM,IAAeiM,IAAsB/L,IAAS+I,CAAAA,CAAAA,GAE5CsD,SAAmBjH,cAAAA,SAAQ,WAAA;AAC7B,QAAKtF,GAEL,QAAO,SAACK,IAAAA;AAAyB,aAAK,SAAC+L,IAAAA;AACnCF,QAAAA,GAAAA,GACA7D,EAAc,IAAA,GAAA,QACda,KAAAA,EAAe7I,IAAM+L,EAAAA;MAAAA;IACxB;EACJ,GAAE,CAACpM,IAAekM,IAAa7D,GAAea,CAAAA,CAAAA,GAEzCsD,SAAclH,cAAAA,SAAQ,WAAA;AACxB,QAAKtF,GAEL,QAAO,SAACK,IAAAA;AAAyB,aAAK,SAAC+L,IAAAA;AAAAA,gBACnCjD,MAAAA,GAAU9I,IAAM+L,EAAAA;MAAAA;IACnB;EACL,GAAG,CAACpM,IAAemJ,EAAAA,CAAAA;AAWnB,SAPI+B,KADkB,WAAlBzJ,KACOqH,KACkB,aAAlBrH,KACAiJ,KAEAjJ,QAIPlB,oBAAAA,KAAAkM,oBAAAA,UAAA,EAAA5C,UACK0B,GAAW,SAACxC,IAAe1I,IAAAA;AAAI,eAC5BgM,cAAAA,eAAcnB,IAAM,EAChB7K,MAAAA,IACAmB,cAAAA,IACAvC,aAAAA,IACA8J,eAAAA,IACAxJ,cAAAA,IACAyJ,cAAcmD,IACdlD,aAAaqD,IACbpD,cAAcqD,IACdpD,SAASqD,GAAAA,CAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAK7B;ANzJA,IOKaE,KAAyB,SAAHtM,IAAAA;AAGO,MAFtCgD,KAAKhD,GAALgD,OACArD,KAAWK,GAAXL,aAEM4M,KAAmBjE,GAA0BtF,IAAOrD,EAAAA;AAE1D,aACIQ,oBAAAA,KAAAkM,oBAAAA,UAAA,EAAA5C,UACK8C,GAAiBlI,IAAI,SAACmI,IAAYC,IAAAA;AAAC,eAChCtM,oBAAAA,KAACuM,GAAU3L,EAAayL,CAAAA,GAAAA,EAAAA,GAAPC,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAIjC;APlBA,IOkBA,KAAA,CAAA,iBAAA,WAAA,gBAAA,SAAA,eAAA;APlBA,IQoBME,KAAe,SAAH3M,IAAAA;AA+C0B,MA9CxCO,KAAIP,GAAJO,MAAIqM,KAAA5M,GACJ7B,QAAAA,KAAAA,WAAMyO,KAAG9L,EAAgB3C,SAAMyO,IAC/BnH,KAAWzF,GAAXyF,aACA5D,KAAK7B,GAAL6B,OACAE,IAAM/B,GAAN+B,QACQ8K,IAAa7M,GAArB8M,QAAMC,IAAA/M,GACN5B,aAAAA,IAAAA,WAAW2O,IAAGjM,EAAgB1C,cAAW2O,GAAAC,KAAAhN,GACzC3B,eAAAA,KAAAA,WAAa2O,KAAGlM,EAAgBzC,gBAAa2O,IAAAC,KAAAjN,GAC7C1B,eAAAA,KAAAA,WAAa2O,KAAGnM,EAAgBxC,gBAAa2O,IAAAC,KAAAlN,GAC7CzB,eAAAA,KAAAA,WAAa2O,KAAGpM,EAAgBvC,gBAAa2O,IAAAC,KAAAnN,GAC7CxB,eAAAA,KAAAA,WAAa2O,KAAGrM,EAAgBtC,gBAAa2O,IAAAC,KAAApN,GAC7CvB,eAAAA,KAAAA,WAAa2O,KAAGtM,EAAgBrC,gBAAa2O,IAAAC,KAAArN,GAC7CqB,eAAAA,KAAAA,WAAagM,KAAGvM,EAAgBO,gBAAagM,IAAAC,KAAAtN,GAG7CtB,SAAAA,KAAAA,WAAO4O,KAAGxM,EAAgBpC,UAAO4O,IAAAC,KAAAvN,GACjCrB,eAAAA,KAAAA,WAAa4O,KAAGzM,EAAgBnC,gBAAa4O,IAAAC,KAAAxN,GAC7CpB,iBAAAA,KAAAA,WAAe4O,KAAG1M,EAAgBlC,kBAAe4O,IAAAC,KAAAzN,GACjDoB,cAAAA,IAAAA,WAAYqM,KAAG3M,EAAgBM,eAAYqM,IAAAC,IAAA1N,GAC3CnB,aAAAA,IAAAA,WAAW6O,IAAG5M,EAAgBjC,cAAW6O,GAAAC,KAAA3N,GACzClB,aAAAA,IAAAA,WAAW6O,KAAG7M,EAAgBhC,cAAW6O,IAAAC,IAAA5N,GACzCf,aAAAA,KAAAA,WAAW2O,IAAG9M,EAAgB7B,cAAW2O,GAAAC,KAAA7N,GACzCd,aAAAA,KAAAA,WAAW2O,KAAG/M,EAAgB5B,cAAW2O,IAAAC,KAAA9N,GACzCgB,SAAAA,KAAAA,WAAO8M,KAAGhN,EAAgBE,UAAO8M,IAAAC,KAAA/N,GACjCiB,WAAAA,KAAAA,WAAS8M,KAAGjN,EAAgBG,YAAS8M,IAAAC,KAAAhO,GACrCkB,YAAAA,MAAAA,WAAU8M,KAAGlN,EAAgBI,aAAU8M,IAAAC,MAAAjO,GACvCmB,UAAAA,MAAAA,WAAQ8M,MAAGnN,EAAgBK,WAAQ8M,KAAAC,MAAAlO,GACnCb,cAAAA,MAAAA,WAAY+O,MAAGpN,EAAgB3B,eAAY+O,KAAAC,MAAAnO,GAC3CZ,OAAAA,MAAAA,WAAK+O,MAAGrN,EAAgB1B,QAAK+O,KAAAC,MAAApO,GAC7BX,gBAAAA,MAAAA,WAAc+O,MAAGtN,EAAgBzB,iBAAc+O,KAAAC,MAAArO,GAC/CV,QAAAA,MAAAA,WAAM+O,MAAGvN,EAAgBxB,SAAM+O,KAAAC,MAAAtO,GAC/BP,YAAAA,MAAAA,WAAU6O,MAAGxN,EAAgBrB,aAAU6O,KAAAC,MAAAvO,GACvCN,SAAAA,MAAAA,WAAO6O,MAAGzN,EAAgBpB,UAAO6O,KAAAC,MAAAxO,GACjCL,aAAAA,MAAAA,WAAW6O,MAAG1N,EAAgBnB,cAAW6O,KAAAC,MAAAzO,GACzCJ,eAAAA,MAAAA,WAAa6O,MAAG3N,EAAgBlB,gBAAa6O,KAC7C7F,MAAY5I,GAAZ4I,cACAC,MAAW7I,GAAX6I,aACAC,MAAY9I,GAAZ8I,cACAC,MAAO/I,GAAP+I,SAAO2F,KAAA1O,GACPH,aAAAA,KAAAA,WAAW6O,KAAG5N,EAAgBjB,cAAW6O,IAAAC,KAAA3O,GACzCF,SAAAA,KAAAA,WAAO6O,KAAG7N,EAAgBhB,UAAO6O,IACjCC,KAAI5O,GAAJ4O,MACAC,KAAS7O,GAAT6O,WACAC,KAAc9O,GAAd8O,gBACAC,KAAe/O,GAAf+O,iBACAC,KAAYhP,GAAZgP,cAEAC,KAMIC,GAAcrN,IAAOE,GAAQ8K,CAAAA,GALrBsC,KAAOF,GAAfnC,QACYsC,KAAWH,GAAvBI,YACaC,KAAYL,GAAzBM,aACAC,KAAUP,GAAVO,YACAC,KAAWR,GAAXQ,aAGJC,MAWI7I,GAA8B,EAC9BtG,MAAAA,IACAkF,aAAAA,IACA5D,OAAOuN,IACPrN,QAAQuN,IACRlR,aAAAA,GACAC,eAAAA,IACAC,eAAAA,IACAC,eAAAA,IACAC,eAAAA,IACAC,eAAAA,IACAa,QAAAA,KACAG,YAAAA,KACAf,SAAAA,IACAC,eAAAA,IACAC,iBAAAA,IACAE,aAAAA,GACAM,OAAAA,KACAC,gBAAAA,KACAQ,aAAAA,GAAAA,CAAAA,GA7BOwP,KAAUK,IAAjB7N,OACQ0N,KAAWG,IAAnB3N,QACAG,KAAOwN,IAAPxN,SACAC,KAAOuN,IAAPvN,SACAwB,KAAM+L,IAAN/L,QACAO,KAAMwL,IAANxL,QACAlB,KAAK0M,IAAL1M,OACA4C,KAAU8J,IAAV9J,YACAoC,KAAU0H,IAAV1H,YACAC,KAAayH,IAAbzH,eAuBE6E,SAAS5H,cAAAA,SACX,WAAA;AAAA,WAAAnE,EAAAA,CAAAA,GACOoO,IAAO,EACVQ,KAAKR,GAAQQ,MAAMxN,IACnByN,MAAMT,GAAQS,OAAO1N,GAAAA,CAAAA;EACvB,GACF,CAACiN,IAASjN,IAASC,EAAAA,CAAAA,GAGjB0N,KAAwC,EAC1CC,MAAM,MACNC,MAAM,MACN/M,OAAO,MACPtD,SAAS,MACTC,aAAa,KAAA;AAGbxB,EAAAA,GAAOuI,SAAS,MAAA,MAChBmJ,GAAUC,WACN3P,oBAAAA,KAAC6P,GAAI,EAEDnO,OAAOwN,IACPtN,QAAQwN,IACR5L,QAAQ1E,KAAc0E,KAAS,MAC/BO,QAAQhF,KAAcgF,KAAS,KAAA,GAJ3B,MAAA,IASZ/F,GAAOuI,SAAS,MAAA,MAChBmJ,GAAUE,WACN5P,oBAAAA,KAAC8P,GAAI,EAEDtM,QAAQA,IACRO,QAAQA,IACRrC,OAAOwN,IACPtN,QAAQwN,IACRI,KAAK3O,IACLkP,OAAOjP,IACPkP,QAAQjP,KACR0O,MAAMzO,IAAAA,GARF,MAAA,IAaZhD,GAAOuI,SAAS,OAAA,MAChBmJ,GAAU7M,YACN7C,oBAAAA,KAACiQ,cAAAA,UAAQ,EAAA3G,cACLtJ,oBAAAA,KAAC0K,IAAY,EACT7H,OAAOA,IACP3B,eAAeA,IACfD,cAAcA,GACdvC,aAAaA,GACbe,eAAeA,KACfqI,eAAeA,IACfW,cAAcA,KACdC,aAAaA,KACbC,cAAcA,KACdC,SAASA,KACTjJ,SAASA,IACTX,cAAcA,IAAAA,CAAAA,EAAAA,GAbR,OAAA,IAmBlBhB,GAAOuI,SAAS,SAAA,KAA6B,SAAfd,OAC9BiK,GAAUnQ,cACNS,oBAAAA,KAACiQ,cAAAA,UAAQ,EAAA3G,UACJ/J,IAAQ2E,IAAI,SAACgM,IAAQC,IAAAA;AAAK,eACvBC,cAAAA,eAACC,GAAiCzP,EAAAA,CAAAA,GAC1BsP,IAAM,EACVI,KAAKH,IACLI,gBAAgBrB,IAChBsB,iBAAiBpB,IACjB5K,OAAOiB,GAAAA,CAAAA,CAAAA;EAAAA,CAAAA,EAAAA,GAPL,SAAA,IAclBzH,GAAOuI,SAAS,aAAA,KAAkB/G,IAAY+D,SAAS,MACvDmM,GAAUlQ,kBACNQ,oBAAAA,KAACmM,IAAsB,EAEnBtJ,OAAOA,IACPrD,aAAaA,IAAAA,GAFT,aAAA;AAOhB,MAAMiR,KAA4C,EAC9C5N,OAAAA,IACAgF,YAAAA,IACAC,eAAAA,GAAAA;AAGJ,aACI9H,oBAAAA,KAAC0Q,IAAU,EACPhP,OAAO2N,IACPzN,QAAQ0N,IACR3C,QAAQgE,OAAOC,OAAO,CAAA,GAAIjE,IAAQ,EAC9B6C,KAAK7C,GAAO6C,KACZC,MAAM9C,GAAO8C,KAAAA,CAAAA,GAEjBhB,MAAMA,IACNC,WAAWA,IACXC,gBAAgBA,IAChBC,iBAAiBA,IACjBiC,KAAKhC,IAAavF,UAEjBtL,GAAOkG,IAAI,SAAC4M,IAAOxE,IAAAA;AAAM,QAAAyE;AACtB,WAAqB,cAAA,OAAVD,SACA9Q,oBAAAA,KAACiQ,cAAAA,UAAQ,EAAA3G,cAAUwC,cAAAA,eAAcgF,IAAOL,EAAAA,EAAAA,GAAzBnE,EAAAA,IAGD,SAAzByE,KAAOrB,QAAAA,KAAAA,SAAAA,GAAYoB,EAAAA,KAAMC,KAAI;EAAA,CAAA,EAAA,CAAA;AAI7C;ARtOA,IQwOaC,SAAUC,cAAAA,YACnB,SAAAzO,IAYIqO,IAAAA;AAAuB,MAAAK,KAAA1O,GAPnB/C,eAAAA,KAAAA,WAAayR,KAAGvQ,EAAgBlB,gBAAayR,IAAAC,KAAA3O,GAC7C/B,SAAAA,KAAAA,WAAO0Q,KAAGxQ,EAAgBF,UAAO0Q,IAAAC,KAAA5O,GACjC9B,cAAAA,KAAAA,WAAY0Q,KAAGzQ,EAAgBD,eAAY0Q,IAC3CvL,KAAKrD,GAALqD,OACAwL,IAAa7O,GAAb6O,eACGC,IAAUC,GAAA/O,IAAAgP,EAAAA;AAAA,aAIjBxR,oBAAAA,KAACyR,IAAS,EAEFhR,SAAAA,IACAhB,eAAAA,IACAiB,cAAAA,IACA2Q,eAAAA,GACAxL,OAAAA,IAAKyD,cAGTtJ,oBAAAA,KAACwM,IAAY5L,EAAA,EACTnB,eAAeA,GAAAA,GACX6R,GAAU,EACdzC,cAAcgC,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAEV,CAAA;ARrQpB,IQqQoB,KAAA,CAAA,gBAAA,iBAAA,YAAA,gBAAA;ARrQpB,ISCaa,SAAoBT,cAAAA,YAC7B,SAAApR,IAWIgR,IAAAA;AAAuB,MANnBc,KAAY9R,GAAZ8R,cACAC,KAAa/R,GAAb+R,eACAC,KAAQhS,GAARgS,UACAC,KAAcjS,GAAdiS,gBACGC,KAAKR,GAAA1R,IAAA2R,EAAAA;AAAA,aAIZxR,oBAAAA,KAACgS,IAAiB,EACdL,cAAcA,IACdC,eAAeA,IACfC,UAAUA,IACVC,gBAAgBA,IAAexI,UAE9B,SAAA9G,IAAAA;AAAA,QAAGd,KAAKc,GAALd,OAAOE,KAAMY,GAANZ;AAAM,eACb5B,oBAAAA,KAACgR,IAAOpQ,EAAA,EAAoBc,OAAOA,IAAOE,QAAQA,GAAAA,GAAYmQ,IAAK,EAAElB,KAAKA,GAAAA,CAAAA,CAAAA;EAAO,EAAA,CAAA;AAErE,CAAA;ATxB5B,IUDaoB,KAAa,SACtBC,IAA6BrS,IAAAA;AAO5B,MAAAsS,KAAAtS,GALGC,MAAQO,KAAC8R,GAAD9R,GAAG8C,KAACgP,GAADhP,GAAGzB,KAAKyQ,GAALzQ,OAAOE,KAAMuQ,GAANvQ,QAAQpB,KAAK2R,GAAL3R,OAAO7B,KAAWwT,GAAXxT,aAAaJ,IAAO4T,GAAP5T,SAASW,IAAciT,GAAdjT,gBAAgBD,IAAKkT,GAALlT,OAC1EP,IAAWmB,GAAXnB,aACAM,IAAYa,GAAZb,cACA6G,KAAKhG,GAALgG;AAGJqM,EAAAA,GAAIE,KAAAA,GACJF,GAAIG,cAAc9T,GAElB2T,GAAII,YAAY9R,IACZ9B,IAAc,MACdwT,GAAIK,cAAc5T,IAClBuT,GAAIM,YAAY9T,IAGpBwT,GAAIO,SAASpS,KAAIqB,KAAQ,GAAGyB,KAAIvB,KAAS,GAAGF,IAAOE,EAAAA,GAC/ClD,IAAc,KACdwT,GAAIQ,WAAWrS,KAAIqB,KAAQ,GAAGyB,KAAIvB,KAAS,GAAGF,IAAOE,EAAAA,GAGrD5C,MACA2T,EAAcT,IAAKrM,GAAMmE,OAAOC,IAAAA,GAChCiI,GAAIU,YAAY,UAChBV,GAAIW,eAAe,UACnBC,EACIZ,IAAGtR,EAAAA,CAAAA,GAEIiF,GAAMmE,OAAOC,MAAI,EACpBT,MAAMtK,EAAAA,CAAAA,GAEVD,GACAoB,IACA8C,EAAAA,IAIR+O,GAAIa,QAAAA;AACR;AVvCA,IUyCaC,KAAe,SACxBd,IAA6B1P,IAAAA;AAO5B,MAAAyQ,KAAAzQ,GALG1C,MAAQO,KAAC4S,GAAD5S,GAAG8C,KAAC8P,GAAD9P,GAAGzB,KAAKuR,GAALvR,OAAOE,KAAMqR,GAANrR,QAAQpB,KAAKyS,GAALzS,OAAO7B,KAAWsU,GAAXtU,aAAaJ,IAAO0U,GAAP1U,SAASW,IAAc+T,GAAd/T,gBAAgBD,IAAKgU,GAALhU,OAC1EP,IAAW8D,GAAX9D,aACAM,IAAYwD,GAAZxD,cACA6G,KAAKrD,GAALqD;AAGJqM,EAAAA,GAAIE,KAAAA,GACJF,GAAIG,cAAc9T;AAElB,MAAM2U,KAAShR,KAAKI,IAAIZ,IAAOE,EAAAA,IAAU;AAEzCsQ,EAAAA,GAAII,YAAY9R,IACZ9B,IAAc,MACdwT,GAAIK,cAAc5T,IAClBuT,GAAIM,YAAY9T,IAGpBwT,GAAIiB,UAAAA,GACJjB,GAAIkB,IAAI/S,IAAG8C,IAAG+P,IAAQ,GAAG,IAAIhR,KAAKmR,EAAAA,GAElCnB,GAAI1I,KAAAA,GACA9K,IAAc,KACdwT,GAAIzI,OAAAA,GAGJzK,MACA2T,EAAcT,IAAKrM,GAAMmE,OAAOC,IAAAA,GAChCiI,GAAIU,YAAY,UAChBV,GAAIW,eAAe,UACnBC,EACIZ,IAAGtR,EAAAA,CAAAA,GAEIiF,GAAMmE,OAAOC,MAAI,EACpBT,MAAMtK,EAAAA,CAAAA,GAEVD,GACAoB,IACA8C,EAAAA,IAIR+O,GAAIa,QAAAA;AACR;AVtFA,IUsFA,KAAA,CAAA,SAAA,iBAAA,WAAA,gBAAA,eAAA;AVtFA,IWwCMO,KAAqB,SAAHzT,IAAAA;AA0C0B,MA0D1CuB,IAnGJhB,KAAIP,GAAJO,MAAIqM,KAAA5M,GACJ7B,QAAAA,IAAAA,WAAMyO,KAAGtL,EAAmBnD,SAAMyO,IAClCnH,IAAWzF,GAAXyF,aACA5D,IAAK7B,GAAL6B,OACAE,IAAM/B,GAAN+B,QACQ8K,KAAa7M,GAArB8M,QAAME,KAAAhN,GACN3B,eAAAA,IAAAA,WAAa2O,KAAG1L,EAAmBjD,gBAAa2O,IAAAC,KAAAjN,GAChD1B,eAAAA,KAAAA,WAAa2O,KAAG3L,EAAmBhD,gBAAa2O,IAAAC,KAAAlN,GAChDzB,eAAAA,KAAAA,WAAa2O,KAAG5L,EAAmB/C,gBAAa2O,IAAAC,KAAAnN,GAChDxB,eAAAA,KAAAA,WAAa2O,KAAG7L,EAAmB9C,gBAAa2O,IAAAJ,KAAA/M,GAChD5B,aAAAA,KAAAA,WAAW2O,KAAGzL,EAAmBlD,cAAW2O,IAAAK,KAAApN,GAC5CvB,eAAAA,KAAAA,WAAa2O,KAAG9L,EAAmB7C,gBAAa2O,IAAAsG,IAAA1T,GAChDuB,YAAYoS,IAAAA,WAAWD,IAAGpS,EAAmBC,aAAUmS,GAAApG,IAAAtN,GACvDtB,SAAAA,KAAAA,WAAO4O,IAAGhM,EAAmB5C,UAAO4O,GAAAC,IAAAvN,GACpCrB,eAAAA,IAAAA,WAAa4O,IAAGjM,EAAmB3C,gBAAa4O,GAAAC,KAAAxN,GAChDpB,iBAAAA,KAAAA,WAAe4O,KAAGlM,EAAmB1C,kBAAe4O,IAAAE,KAAA1N,GACpDnB,aAAAA,KAAAA,WAAW6O,KAAGpM,EAAmBzC,cAAW6O,IAAAC,KAAA3N,GAC5ClB,aAAAA,KAAAA,WAAW6O,KAAGrM,EAAmBxC,cAAW6O,IAAAC,KAAA5N,GAC5Cf,aAAAA,KAAAA,WAAW2O,KAAGtM,EAAmBrC,cAAW2O,IAAAC,MAAA7N,GAC5Cd,aAAAA,MAAAA,WAAW2O,MAAGvM,EAAmBpC,cAAW2O,KAAAC,MAAA9N,GAC5CgB,SAAAA,MAAAA,WAAO8M,MAAGxM,EAAmBN,UAAO8M,KAAAC,MAAA/N,GACpCiB,WAAAA,MAAAA,WAAS8M,MAAGzM,EAAmBL,YAAS8M,KAAAC,MAAAhO,GACxCkB,YAAAA,MAAAA,WAAU8M,MAAG1M,EAAmBJ,aAAU8M,KAAAC,MAAAjO,GAC1CmB,UAAAA,MAAAA,WAAQ8M,MAAG3M,EAAmBH,WAAQ8M,KAAAC,MAAAlO,GACtCb,cAAAA,MAAAA,WAAY+O,MAAG5M,EAAmBnC,eAAY+O,KAAAC,MAAAnO,GAC9CZ,OAAAA,MAAAA,WAAK+O,MAAG7M,EAAmBlC,QAAK+O,KAAAC,MAAApO,GAChCX,gBAAAA,MAAAA,WAAc+O,MAAG9M,EAAmBjC,iBAAc+O,KAAAC,MAAArO,GAClDV,QAAAA,MAAAA,WAAM+O,MAAG/M,EAAmBhC,SAAM+O,KAAAC,MAAAtO,GAClCP,YAAAA,MAAAA,WAAU6O,MAAGhN,EAAmB7B,aAAU6O,KAAAC,MAAAvO,GAC1CN,SAAAA,MAAAA,WAAO6O,MAAGjN,EAAmB5B,UAAO6O,KAAAC,KAAAxO,GACpCL,aAAAA,KAAAA,WAAW6O,KAAGlN,EAAmB3B,cAAW6O,IAAAC,KAAAzO,GAC5CJ,eAAAA,KAAAA,WAAa6O,KAAGnN,EAAmB1B,gBAAa6O,IAChD1F,KAAO/I,GAAP+I,SAAO2F,KAAA1O,GACPH,aAAAA,KAAAA,WAAW6O,KAAGpN,EAAmBzB,cAAW6O,IAAAC,KAAA3O,GAC5CF,SAAAA,KAAAA,WAAO6O,KAAGrN,EAAmBxB,UAAO6O,IACpCC,KAAI5O,GAAJ4O,MACAC,KAAS7O,GAAT6O,WACAC,KAAc9O,GAAd8O,gBACAC,KAAe/O,GAAf+O,iBAAe6E,KAAA5T,GACfwB,YAAAA,KAAAA,WAAUoS,KAAGtS,EAAmBE,aAAUoS,IAC1C5E,MAAYhP,GAAZgP,cAEM6E,SAAWC,cAAAA,QAAiC,IAAA,GAElD7E,KAMIC,GAAcrN,GAAOE,GAAQ8K,EAAAA,GALrBsC,KAAOF,GAAfnC,QACYsC,KAAWH,GAAvBI,YACaC,KAAYL,GAAzBM,aACAC,KAAUP,GAAVO,YACAC,KAAWR,GAAXQ,aAGJC,KAWI7I,GAA8B,EAC9BtG,MAAAA,IACAkF,aAAAA,GACA5D,OAAOuN,IACPrN,QAAQuN,IACRjR,eAAAA,GACAC,eAAAA,IACAC,eAAAA,IACAC,eAAAA,IACAJ,aAAAA,IACAK,eAAAA,IACAa,QAAAA,KACAG,YAAAA,KACAf,SAAAA,IACAC,eAAAA,GACAC,iBAAAA,IACAE,aAAAA,IACAM,OAAAA,KACAC,gBAAAA,KACAQ,aAAAA,GAAAA,CAAAA,GA7BOwP,KAAUK,GAAjB7N,OACQ0N,KAAWG,GAAnB3N,QACAG,KAAOwN,GAAPxN,SACAC,KAAOuN,GAAPvN,SACAwB,KAAM+L,GAAN/L,QACAO,KAAMwL,GAANxL,QACAlB,KAAK0M,GAAL1M,OACA4C,KAAU8J,GAAV9J,YACAoC,KAAU0H,GAAV1H,YACAC,KAAayH,GAAbzH,eAuBE6E,SAAS5H,cAAAA,SACX,WAAA;AAAA,WAAAnE,EAAAA,CAAAA,GACOoO,IAAO,EACVQ,KAAKR,GAAQQ,MAAMxN,IACnByN,MAAMT,GAAQS,OAAO1N,GAAAA,CAAAA;EACvB,GACF,CAACiN,IAASjN,IAASC,EAAAA,CAAAA,GAGjBoK,KAAmBjE,GAAmBtF,IAAOrD,EAAAA,GAC7CoU,KAAsBC,GAAuB,EAC/CrU,aAAa4M,GAAAA,CAAAA;AAKbhL,EAAAA,KADuB,cAAA,OAAhBoS,IACMA,IACU,aAAhBA,IACMR,KAEAf;AAGjB,MAAMpM,KAAQC,EAAAA,GAER2K,SAA4C1L,cAAAA,SAC9C,WAAA;AAAA,WAAO,EACHlC,OAAAA,IACAgF,YAAAA,IACAC,eAAAA,GAAAA;EACF,GACF,CAACjF,IAAOgF,IAAYC,EAAAA,CAAAA;AAGxBgM,oBAAAA,WAAU,WAAA;AACN,QAAyB,SAArBJ,GAASzO,SAAb;AAEA,UAAMiN,KAAMwB,GAASzO,QAAQ8O,WAAW,IAAA;AACnC7B,MAAAA,OAELwB,GAASzO,QAAQvD,QAAQ2N,KAAahO,IACtCqS,GAASzO,QAAQrD,SAAS0N,KAAcjO,IAExC6Q,GAAI1N,MAAMnD,IAAYA,EAAAA,GAEtB6Q,GAAII,YAAYzM,GAAMmO,YACtB9B,GAAIO,SAAS,GAAG,GAAGpD,IAAYC,EAAAA,GAC/B4C,GAAI+B,UAAUtH,GAAO8C,MAAM9C,GAAO6C,GAAAA,GAElCxR,EAAO8E,QAAQ,SAAAgO,IAAAA;AACG,mBAAVA,MACAoB,GAAIM,YAAY3M,GAAM8J,KAAKuE,KAAKxK,aAChCwI,GAAIK,cAAc1M,GAAM8J,KAAKuE,KAAKzK,QAE9B3K,MACAqV,EAAwBjC,IAAK,EACzBxQ,OAAOwN,IACPtN,QAAQwN,IACR5K,OAAOhB,IACP4Q,MAAM,IAAA,CAAA,GAGVrV,OACAoV,EAAwBjC,IAAK,EACzBxQ,OAAOwN,IACPtN,QAAQwN,IACR5K,OAAOT,IACPqQ,MAAM,IAAA,CAAA,KAGG,WAAVtD,KACPuD,EAAmBnC,IAAK,EACpB1O,QAAAA,IACAO,QAAAA,IACArC,OAAOwN,IACPtN,QAAQwN,IACRI,KAAK3O,KACLkP,OAAOjP,KACPkP,QAAQjP,KACR0O,MAAMzO,KACN6E,OAAAA,GAAAA,CAAAA,IAEa,YAAViL,MACPoB,GAAIU,YAAY,UAChBV,GAAIW,eAAe,UAEnBhQ,GAAMC,QAAQ,SAAAhD,IAAAA;AACVsB,UAAAA,GAAW8Q,IAAK,EAAEpS,MAAAA,IAAMpB,aAAAA,IAAaM,cAAAA,KAAc6G,OAAAA,GAAAA,CAAAA;QACvD,CAAA,KACiB,cAAViL,MAAsC,SAAfrL,KAC9BlG,IAAQuD,QAAQ,SAAAoN,IAAAA;AACZoE,UAAAA,GAAoCpC,IAAGtR,EAAAA,CAAAA,GAChCsP,IAAM,EACTK,gBAAgBrB,IAChBsB,iBAAiBpB,IACjB5K,OAAOiB,IACPI,OAAAA,GAAAA,CAAAA,CAAAA;QAER,CAAA,IACiB,kBAAViL,KACPyD,EAA0BrC,IAAK,EAC3B1S,aAAaoU,IACb/N,OAAAA,GAAAA,CAAAA,IAEoB,cAAA,OAAViL,MACdA,GAAMoB,IAAKzB,EAAAA;MAEnB,CAAA;IAxE+B;EAyElC,GAAE,CACCiD,IACArS,IACAgO,IACAC,IACAJ,IACAE,IACAzC,IACA3O,GACAyS,IACA5N,IACAzB,IACAtC,IACAC,KACA8B,KACAC,KACAC,KACAC,KACAwC,IACAO,IACA8B,IACAnH,IACAM,KACAyG,IACAlG,KACAqU,EAAAA,CAAAA;AAGJ,MAAApI,KAA8CC,EAAAA,GAAtCC,KAAoBF,GAApBE,sBAAsBC,KAAWH,GAAXG,aAExB6I,SAAmB5O,cAAAA,aACrB,SAACiG,IAAAA;AACG,QAAyB,SAArB6H,GAASzO,SAAb;AAEA,UAAAwP,KAAeC,GAAkBhB,GAASzO,SAAS4G,EAAAA,GAA5CxL,KAACoU,GAAA,CAAA,GAAEtR,KAACsR,GAAA,CAAA,GAEL3U,KAAO+C,GAAM8R,KAAK,SAAAC,IAAAA;AAAC,eACrBC,GACID,GAAEvU,IAAIsM,GAAO8C,OAAOmF,GAAElT,QAAQ,GAC9BkT,GAAEzR,IAAIwJ,GAAO6C,MAAMoF,GAAEhT,SAAS,GAC9BgT,GAAElT,OACFkT,GAAEhT,QACFvB,IACA8C,EAAAA;MACH,CAAA;AAAA,iBAEDrD,MACAgI,GAAchI,EAAAA,GACd4L,OAAqBI,cAAAA,eAAcnM,IAAS,EAAEG,MAAAA,GAAAA,CAAAA,GAAS+L,EAAAA,MAEvD/D,GAAc,IAAA,GACd6D,GAAAA;IAnB2B;EAqBnC,GACA,CACI+H,IACA7Q,IACA8J,IAGA7E,IACA4D,IACAC,IACAhM,EAAAA,CAAAA,GAIFqM,SAAmBpG,cAAAA,aAAY,WAAA;AACjCkC,OAAc,IAAA,GACd6D,GAAAA;EACJ,GAAG,CAAC7D,IAAe6D,EAAAA,CAAAA,GAEbM,SAAcrG,cAAAA,aAChB,SAACiG,IAAAA;AACsB,aAAfhE,OAAAA,QAEJe,MAAAA,GAAUf,IAAYgE,EAAAA;EAC1B,GACA,CAAChE,IAAYe,EAAAA,CAAAA;AAGjB,aACI5I,oBAAAA,KAAA,UAAA,EACI6Q,KAAKiE,GAAUpB,IAAU7E,GAAAA,GACzBnN,OAAO2N,KAAahO,IACpBO,QAAQ0N,KAAcjO,IACtB6H,OAAO,EACHxH,OAAO2N,IACPzN,QAAQ0N,GAAAA,GAEZ7G,cAAchJ,KAAgB+U,KAAAA,QAC9B9L,aAAajJ,KAAgB+U,KAAAA,QAC7B7L,cAAclJ,KAAgBuM,KAAAA,QAC9BpD,SAASnJ,KAAgBwM,KAAAA,QACzBwC,MAAMA,IACN,cAAYC,IACZ,mBAAiBC,IACjB,oBAAkBC,GAAAA,CAAAA;AAG9B;AX9UA,IWgVamG,SAAgB9D,cAAAA,YACzB,SAAAzO,IAYIqO,IAAAA;AAA2B,MAPvBhL,KAAKrD,GAALqD,OAAKqL,KAAA1O,GACL/C,eAAAA,KAAAA,WAAayR,KAAG/P,EAAmB1B,gBAAayR,IAAAC,KAAA3O,GAChD/B,SAAAA,KAAAA,WAAO0Q,KAAGhQ,EAAmBV,UAAO0Q,IAAAC,KAAA5O,GACpC9B,cAAAA,KAAAA,WAAY0Q,KAAGjQ,EAAmBT,eAAY0Q,IAC9CC,IAAa7O,GAAb6O,eACGC,IAAUC,GAAA/O,IAAAgP,EAAAA;AAAA,aAIjBxR,oBAAAA,KAACyR,IAAS,EAAOhS,eAAAA,IAAegB,SAAAA,IAASC,cAAAA,IAAcmF,OAAAA,IAAOwL,eAAAA,GAAa/H,cACvEtJ,oBAAAA,KAACsT,IAAkB1S,EAAA,EACfnB,eAAeA,GAAAA,GACX6R,GAAU,EACdzC,cAAcgC,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAEV,CAAA;AXrWpB,IWqWoB,KAAA,CAAA,gBAAA,iBAAA,YAAA,gBAAA;AXrWpB,IYCamE,SAA0B/D,cAAAA,YACnC,SAAApR,IAWIgR,IAAAA;AAA2B,MANvBc,KAAY9R,GAAZ8R,cACAC,KAAa/R,GAAb+R,eACAC,KAAQhS,GAARgS,UACAC,KAAcjS,GAAdiS,gBACGC,KAAKR,GAAA1R,IAAA2R,EAAAA;AAAA,aAIZxR,oBAAAA,KAACgS,IAAiB,EACdL,cAAcA,IACdC,eAAeA,IACfC,UAAUA,IACVC,gBAAgBA,IAAexI,UAE9B,SAAA9G,IAAAA;AAAA,QAAGd,KAAKc,GAALd,OAAOE,KAAMY,GAANZ;AAAM,eACb5B,oBAAAA,KAAC+U,IAAanU,EAAA,EACVc,OAAOA,IACPE,QAAQA,GAAAA,GACJmQ,IAAK,EACTlB,KAAKA,GAAAA,CAAAA,CAAAA;EACP,EAAA,CAAA;AAEU,CAAA;", "names": ["collection", "n", "i", "v", "x", "z", "x2", "z2", "t", "defaultProps", "dotSize", "noteWidth", "noteTextOffset", "animate", "isSvgNote", "note", "noteType", "isValidElement", "isCanvasNote", "isCircleAnnotation", "annotationSpec", "type", "isDotAnnotation", "isRectAnnotation", "bindAnnotations", "_ref", "data", "annotations", "getPosition", "getDimensions", "reduce", "acc", "annotation", "offset", "concat", "filter", "match", "map", "datum", "position", "dimensions", "size", "width", "height", "_extends", "omit", "getLinkAngle", "sourceX", "sourceY", "targetX", "targetY", "angle", "Math", "atan2", "normalizeAngleDegrees", "radiansToDegrees", "computeAnnotation", "computedNoteX", "computedNoteY", "x", "y", "noteX", "noteY", "_annotation$noteWidth", "_annotation$noteTextO", "isNumber", "abs", "Error", "computedX", "computedY", "positionFromAngle", "degreesToRadians", "eighth", "round", "textX", "noteLineX", "points", "text", "useAnnotations", "useMemo", "useComputedAnnotations", "_ref2", "computed", "useComputedAnnotation", "AnnotationNote", "theme", "useTheme", "_useMotionConfig", "useMotionConfig", "springConfig", "config", "animatedProps", "useSpring", "immediate", "createElement", "_jsxs", "_Fragment", "children", "outlineWidth", "_jsx", "animated", "style", "strokeLinejoin", "strokeWidth", "stroke", "outlineColor", "AnnotationLink", "_ref$isOutline", "isOutline", "path", "firstPoint", "slice", "animatedPath", "useAnimatedPath", "link", "strokeLinecap", "opacity", "outlineOpacity", "fill", "d", "CircleAnnotationOutline", "radius", "outline", "circle", "cx", "cy", "r", "DotAnnotationOutline", "_ref$size", "symbol", "RectAnnotationOutline", "_ref$borderRadius", "borderRadius", "rect", "rx", "ry", "Annotation", "drawPoints", "ctx", "for<PERSON>ach", "index", "moveTo", "lineTo", "renderAnnotationsToCanvas", "length", "save", "lineCap", "strokeStyle", "lineWidth", "beginPath", "arc", "PI", "fillStyle", "font", "fontSize", "fontFamily", "textAlign", "textBaseline", "lineJoin", "strokeText", "fillText", "restore", "import_react_dom", "isCustomPropRE", "dangerousStyleValue", "isUnitlessNumber", "attributeCache", "applyAnimatedValues", "n", "i", "prefixKey", "prefixes", "domTransforms", "pxTransforms", "degTransforms", "addUnit", "isValueIdentity", "v", "AnimatedStyle", "x", "z", "x2", "z2", "FluidTransform", "t", "primitives", "host", "animated", "commonDefaultProps", "layers", "forceSquare", "xInnerPadding", "xOuterPadding", "yInnerPadding", "yOuterPadding", "sizeVariation", "opacity", "activeOpacity", "inactiveOpacity", "borderWidth", "borderColor", "from", "modifiers", "enableGridX", "enableGridY", "enableLabels", "label", "labelTextColor", "colors", "type", "scheme", "emptyColor", "legends", "annotations", "isInteractive", "hoverTarget", "tooltip", "memo", "_ref", "cell", "formattedValue", "_jsx", "BasicTooltip", "id", "serieId", "data", "x", "value", "enableChip", "color", "animate", "motionConfig", "svgDefaultProps", "_extends", "axisTop", "axisRight", "axisBottom", "axisLeft", "borderRadius", "cellComponent", "canvasDefaultProps", "renderCell", "pixelRatio", "window", "devicePixelRatio", "computeLayout", "_width", "width", "_height", "height", "rows", "columns", "offsetX", "offsetY", "cellWidth", "Math", "max", "cellHeight", "cellSize", "min", "computeCells", "_ref2", "xValuesSet", "Set", "serieIds", "allValues", "cells", "for<PERSON>ach", "serie", "push", "datum", "add", "y", "xValues", "Array", "_computeLayout", "length", "xScale", "castBandScale", "scaleBand", "domain", "range", "paddingOuter", "paddingInner", "yScale", "bandwidth", "cellsWithPosition", "map", "minValue", "apply", "maxValue", "computeSizeScale", "size", "scale", "scaleLinear", "values", "sizes", "getCellAnnotationPosition", "getCellAnnotationDimensions", "useComputeCells", "useMemo", "isHoverTargetByType", "current", "row", "column", "rowColumn", "useCellsStyle", "valueFormat", "activeIds", "getSize", "colorScale", "getContinuousColorScale", "getColor", "useCallback", "theme", "useTheme", "getBorderColor", "useInheritedColor", "getLabelTextColor", "formatValue", "useValueFormatter", "get<PERSON><PERSON><PERSON>", "usePropertyAccessor", "computedOpacity", "includes", "sizeMultiplier", "computedCell", "useHeatMap", "_ref3", "_ref3$xOuterPadding", "_ref3$xInnerPadding", "_ref3$yOuterPadding", "_ref3$yInnerPadding", "_ref3$forceSquare", "_ref3$sizeVariation", "_ref3$colors", "_ref3$emptyColor", "_ref3$opacity", "_ref3$activeOpacity", "_ref3$inactiveOpacity", "_ref3$borderColor", "_ref3$label", "_ref3$labelTextColor", "_ref3$hoverTarget", "_useState", "useState", "activeCell", "setActiveCell", "_useComputeCells", "isHoverTarget", "filter", "_useCellsStyle", "useCellAnnotations", "useAnnotations", "getPosition", "getDimensions", "HeatMapCellRect", "animatedProps", "onMouseEnter", "onMouseMove", "onMouseLeave", "onClick", "handlers", "undefined", "_jsxs", "animated", "g", "style", "cursor", "transform", "to", "children", "rect", "fill", "stroke", "strokeWidth", "rx", "ry", "Text", "textAnchor", "dominantBaseline", "labels", "text", "userSelect", "HeatMapCellCircle", "circle", "r", "fillOpacity", "enterTransition", "regularTransition", "exitTransition", "HeatMapCells", "Cell", "_useMotionConfig", "useMotionConfig", "springConfig", "config", "transition", "useTransition", "keys", "initial", "enter", "update", "leave", "immediate", "_useTooltip", "useTooltip", "showTooltipFromEvent", "hideTooltip", "handleMouseEnter", "event", "createElement", "handleMouseMove", "handleMouseLeave", "handleClick", "_Fragment", "HeatMapCellAnnotations", "boundAnnotations", "annotation", "i", "Annotation", "InnerHeatMap", "_ref$layers", "<PERSON><PERSON><PERSON><PERSON>", "margin", "_ref$forceSquare", "_ref$xInnerPadding", "_ref$xOuterPadding", "_ref$yInnerPadding", "_ref$yOuterPadding", "_ref$sizeVariation", "_ref$cellComponent", "_ref$opacity", "_ref$activeOpacity", "_ref$inactiveOpacity", "_ref$borderRadius", "_ref$borderWidth", "_ref$borderColor", "_ref$enableGridX", "_ref$enableGridY", "_ref$axisTop", "_ref$axisRight", "_ref$axisBottom", "_ref$axisLeft", "_ref$enableLabels", "_ref$label", "_ref$labelTextColor", "_ref$colors", "_ref$emptyColor", "_ref$legends", "_ref$annotations", "_ref$isInteractive", "_ref$hoverTarget", "_ref$tooltip", "role", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaDescribedBy", "forwardedRef", "_useDimensions", "useDimensions", "_margin", "_innerWidth", "innerWidth", "_innerHeight", "innerHeight", "outerWidth", "outerHeight", "_useHeatMap", "top", "left", "layerById", "grid", "axes", "Grid", "Axes", "right", "bottom", "Fragment", "legend", "index", "_createElement", "AnchoredContinuousColorsLegendSvg", "key", "containerWidth", "containerHeight", "customLayerProps", "SvgWrapper", "Object", "assign", "ref", "layer", "_layerById$layer", "HeatMap", "forwardRef", "_ref2$isInteractive", "_ref2$animate", "_ref2$motionConfig", "renderWrapper", "otherProps", "_objectWithoutPropertiesLoose", "_excluded", "Container", "ResponsiveHeatMap", "defaultWidth", "defaultHeight", "onResize", "debounceResize", "props", "ResponsiveWrapper", "renderRect", "ctx", "_ref$cell", "save", "globalAlpha", "fillStyle", "strokeStyle", "lineWidth", "fillRect", "strokeRect", "setCanvasFont", "textAlign", "textBaseline", "drawCanvasText", "restore", "renderCircle", "_ref2$cell", "radius", "beginPath", "arc", "PI", "InnerHeatMapCanvas", "_ref$renderCell", "_renderCell", "_ref$pixelRatio", "canvasEl", "useRef", "computedAnnotations", "useComputedAnnotations", "useEffect", "getContext", "background", "translate", "line", "renderGridLinesToCanvas", "axis", "renderAxesToCanvas", "renderContinuousColorLegendToCanvas", "renderAnnotationsToCanvas", "handleMouseHover", "_getRelativeCursor", "getRelativeCursor", "find", "c", "isCursorInRect", "mergeRefs", "HeatMapCanvas", "ResponsiveHeatMapCanvas"]}