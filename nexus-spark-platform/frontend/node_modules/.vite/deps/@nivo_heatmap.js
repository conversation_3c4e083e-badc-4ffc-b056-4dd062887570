import {
  require_isNumber,
  require_omit
} from "./chunk-FP623KLI.js";
import {
  V,
  W,
  an,
  j,
  q
} from "./chunk-XMDOMWJ3.js";
import {
  require_baseEach,
  require_baseIteratee
} from "./chunk-2BRWKHWI.js";
import {
  $r,
  AnimatedObject,
  Dr,
  FluidValue,
  Fr,
  It,
  M,
  M2,
  Rn,
  Rt,
  T,
  Y,
  Ye,
  a,
  addFluidObserver,
  b,
  bn,
  callFluidObservers,
  cn,
  colors2,
  createHost,
  createStringInterpolator2,
  ct,
  d,
  each,
  eachProp,
  ft,
  getFluidValue,
  globals_exports,
  hasFluidValue,
  hn,
  ht,
  is,
  kn,
  pr,
  removeFluidObserver,
  to,
  toArray,
  useSpring,
  useTransition,
  ut,
  wn,
  z
} from "./chunk-626YKPVW.js";
import {
  band,
  linear,
  require_arrayFilter,
  require_isArray
} from "./chunk-ZERN2KSE.js";
import {
  require_react_dom
} from "./chunk-T2SWDQEL.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __commonJS,
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/lodash/_baseFilter.js
var require_baseFilter = __commonJS({
  "node_modules/lodash/_baseFilter.js"(exports, module) {
    var baseEach = require_baseEach();
    function baseFilter(collection, predicate) {
      var result = [];
      baseEach(collection, function(value, index, collection2) {
        if (predicate(value, index, collection2)) {
          result.push(value);
        }
      });
      return result;
    }
    module.exports = baseFilter;
  }
});

// node_modules/lodash/filter.js
var require_filter = __commonJS({
  "node_modules/lodash/filter.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var baseFilter = require_baseFilter();
    var baseIteratee = require_baseIteratee();
    var isArray = require_isArray();
    function filter(collection, predicate) {
      var func = isArray(collection) ? arrayFilter : baseFilter;
      return func(collection, baseIteratee(predicate, 3));
    }
    module.exports = filter;
  }
});

// node_modules/@nivo/heatmap/dist/nivo-heatmap.mjs
var import_react2 = __toESM(require_react(), 1);

// node_modules/@nivo/annotations/dist/nivo-annotations.mjs
var import_react = __toESM(require_react(), 1);
var import_filter = __toESM(require_filter(), 1);
var import_isNumber = __toESM(require_isNumber(), 1);
var import_omit = __toESM(require_omit(), 1);

// node_modules/@nivo/annotations/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs
var import_react_dom = __toESM(require_react_dom(), 1);
var isCustomPropRE = /^--/;
function dangerousStyleValue(name, value) {
  if (value == null || typeof value === "boolean" || value === "") return "";
  if (typeof value === "number" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))
    return value + "px";
  return ("" + value).trim();
}
var attributeCache = {};
function applyAnimatedValues(instance, props) {
  if (!instance.nodeType || !instance.setAttribute) {
    return false;
  }
  const isFilterElement = instance.nodeName === "filter" || instance.parentNode && instance.parentNode.nodeName === "filter";
  const {
    className,
    style,
    children,
    scrollTop,
    scrollLeft,
    viewBox,
    ...attributes
  } = props;
  const values = Object.values(attributes);
  const names = Object.keys(attributes).map(
    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(
      /([A-Z])/g,
      // Attributes are written in dash case
      (n3) => "-" + n3.toLowerCase()
    ))
  );
  if (children !== void 0) {
    instance.textContent = children;
  }
  for (const name in style) {
    if (style.hasOwnProperty(name)) {
      const value = dangerousStyleValue(name, style[name]);
      if (isCustomPropRE.test(name)) {
        instance.style.setProperty(name, value);
      } else {
        instance.style[name] = value;
      }
    }
  }
  names.forEach((name, i3) => {
    instance.setAttribute(name, values[i3]);
  });
  if (className !== void 0) {
    instance.className = className;
  }
  if (scrollTop !== void 0) {
    instance.scrollTop = scrollTop;
  }
  if (scrollLeft !== void 0) {
    instance.scrollLeft = scrollLeft;
  }
  if (viewBox !== void 0) {
    instance.setAttribute("viewBox", viewBox);
  }
}
var isUnitlessNumber = {
  animationIterationCount: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);
var prefixes = ["Webkit", "Ms", "Moz", "O"];
isUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {
  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);
  return acc;
}, isUnitlessNumber);
var domTransforms = /^(matrix|translate|scale|rotate|skew)/;
var pxTransforms = /^(translate)/;
var degTransforms = /^(rotate|skew)/;
var addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;
var isValueIdentity = (value, id) => is.arr(value) ? value.every((v2) => isValueIdentity(v2, id)) : is.num(value) ? value === id : parseFloat(value) === id;
var AnimatedStyle = class extends AnimatedObject {
  constructor({ x: x2, y, z: z3, ...style }) {
    const inputs = [];
    const transforms = [];
    if (x2 || y || z3) {
      inputs.push([x2 || 0, y || 0, z3 || 0]);
      transforms.push((xyz) => [
        `translate3d(${xyz.map((v2) => addUnit(v2, "px")).join(",")})`,
        // prettier-ignore
        isValueIdentity(xyz, 0)
      ]);
    }
    eachProp(style, (value, key) => {
      if (key === "transform") {
        inputs.push([value || ""]);
        transforms.push((transform) => [transform, transform === ""]);
      } else if (domTransforms.test(key)) {
        delete style[key];
        if (is.und(value)) return;
        const unit = pxTransforms.test(key) ? "px" : degTransforms.test(key) ? "deg" : "";
        inputs.push(toArray(value));
        transforms.push(
          key === "rotate3d" ? ([x22, y2, z22, deg]) => [
            `rotate3d(${x22},${y2},${z22},${addUnit(deg, unit)})`,
            isValueIdentity(deg, 0)
          ] : (input) => [
            `${key}(${input.map((v2) => addUnit(v2, unit)).join(",")})`,
            isValueIdentity(input, key.startsWith("scale") ? 1 : 0)
          ]
        );
      }
    });
    if (inputs.length) {
      style.transform = new FluidTransform(inputs, transforms);
    }
    super(style);
  }
};
var FluidTransform = class extends FluidValue {
  constructor(inputs, transforms) {
    super();
    this.inputs = inputs;
    this.transforms = transforms;
    this._value = null;
  }
  get() {
    return this._value || (this._value = this._get());
  }
  _get() {
    let transform = "";
    let identity = true;
    each(this.inputs, (input, i3) => {
      const arg1 = getFluidValue(input[0]);
      const [t3, id] = this.transforms[i3](
        is.arr(arg1) ? arg1 : input.map(getFluidValue)
      );
      transform += " " + t3;
      identity = identity && id;
    });
    return identity ? "none" : transform;
  }
  // Start observing our inputs once we have an observer.
  observerAdded(count) {
    if (count == 1)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && addFluidObserver(value, this)
        )
      );
  }
  // Stop observing our inputs once we have no observers.
  observerRemoved(count) {
    if (count == 0)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && removeFluidObserver(value, this)
        )
      );
  }
  eventObserved(event) {
    if (event.type == "change") {
      this._value = null;
    }
    callFluidObservers(this, event);
  }
};
var primitives = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "big",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rp",
  "rt",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr",
  // SVG
  "circle",
  "clipPath",
  "defs",
  "ellipse",
  "foreignObject",
  "g",
  "image",
  "line",
  "linearGradient",
  "mask",
  "path",
  "pattern",
  "polygon",
  "polyline",
  "radialGradient",
  "rect",
  "stop",
  "svg",
  "text",
  "tspan"
];
globals_exports.assign({
  batchedUpdates: import_react_dom.unstable_batchedUpdates,
  createStringInterpolator: createStringInterpolator2,
  colors: colors2
});
var host = createHost(primitives, {
  applyAnimatedValues,
  createAnimatedStyle: (style) => new AnimatedStyle(style),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props
});
var animated = host.animated;

// node_modules/@nivo/annotations/dist/nivo-annotations.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function g() {
  return g = Object.assign ? Object.assign.bind() : function(t3) {
    for (var n3 = 1; n3 < arguments.length; n3++) {
      var i3 = arguments[n3];
      for (var o3 in i3) ({}).hasOwnProperty.call(i3, o3) && (t3[o3] = i3[o3]);
    }
    return t3;
  }, g.apply(null, arguments);
}
var k = { dotSize: 4, noteWidth: 120, noteTextOffset: 8, animate: true };
var W2 = function(n3) {
  var i3 = typeof n3;
  return (0, import_react.isValidElement)(n3) || "string" === i3 || "function" === i3 || "object" === i3;
};
var v = function(t3) {
  var n3 = typeof t3;
  return "string" === n3 || "function" === n3;
};
var b2 = function(t3) {
  return "circle" === t3.type;
};
var w = function(t3) {
  return "dot" === t3.type;
};
var z2 = function(t3) {
  return "rect" === t3.type;
};
var P = function(t3) {
  var n3 = t3.data, i3 = t3.annotations, e3 = t3.getPosition, r2 = t3.getDimensions;
  return i3.reduce(function(t4, i4) {
    var s = i4.offset || 0;
    return [].concat(t4, (0, import_filter.default)(n3, i4.match).map(function(t5) {
      var n4 = e3(t5), o3 = r2(t5);
      return (b2(i4) || z2(i4)) && (o3.size = o3.size + 2 * s, o3.width = o3.width + 2 * s, o3.height = o3.height + 2 * s), g({}, (0, import_omit.default)(i4, ["match", "offset"]), n4, o3, { size: i4.size || o3.size, datum: t5 });
    }));
  }, []);
};
var C = function(t3, n3, i3, o3) {
  var e3 = Math.atan2(o3 - n3, i3 - t3);
  return ht(ct(e3));
};
var S = function(t3) {
  var n3, i3, o3 = t3.x, a4 = t3.y, r2 = t3.noteX, s = t3.noteY, h = t3.noteWidth, d3 = void 0 === h ? k.noteWidth : h, c = t3.noteTextOffset, f = void 0 === c ? k.noteTextOffset : c;
  if ((0, import_isNumber.default)(r2)) n3 = o3 + r2;
  else {
    if (void 0 === r2.abs) throw new Error("noteX should be either a number or an object containing an 'abs' property");
    n3 = r2.abs;
  }
  if ((0, import_isNumber.default)(s)) i3 = a4 + s;
  else {
    if (void 0 === s.abs) throw new Error("noteY should be either a number or an object containing an 'abs' property");
    i3 = s.abs;
  }
  var y = o3, x2 = a4, m2 = C(o3, a4, n3, i3);
  if (b2(t3)) {
    var p2 = ft(ut(m2), t3.size / 2);
    y += p2.x, x2 += p2.y;
  }
  if (z2(t3)) {
    var g2 = Math.round((m2 + 90) / 45) % 8;
    0 === g2 && (x2 -= t3.height / 2), 1 === g2 && (y += t3.width / 2, x2 -= t3.height / 2), 2 === g2 && (y += t3.width / 2), 3 === g2 && (y += t3.width / 2, x2 += t3.height / 2), 4 === g2 && (x2 += t3.height / 2), 5 === g2 && (y -= t3.width / 2, x2 += t3.height / 2), 6 === g2 && (y -= t3.width / 2), 7 === g2 && (y -= t3.width / 2, x2 -= t3.height / 2);
  }
  var W3 = n3, v2 = n3;
  return (m2 + 90) % 360 > 180 ? (W3 -= d3, v2 -= d3) : v2 += d3, { points: [[y, x2], [n3, i3], [v2, i3]], text: [W3, i3 - f], angle: m2 + 90 };
};
var O = function(t3) {
  var i3 = t3.data, o3 = t3.annotations, e3 = t3.getPosition, a4 = t3.getDimensions;
  return (0, import_react.useMemo)(function() {
    return P({ data: i3, annotations: o3, getPosition: e3, getDimensions: a4 });
  }, [i3, o3, e3, a4]);
};
var j2 = function(t3) {
  var i3 = t3.annotations;
  return (0, import_react.useMemo)(function() {
    return i3.map(function(t4) {
      return g({}, t4, { computed: S(g({}, t4)) });
    });
  }, [i3]);
};
var M3 = function(t3) {
  return (0, import_react.useMemo)(function() {
    return S(t3);
  }, [t3]);
};
var T2 = function(t3) {
  var n3 = t3.datum, o3 = t3.x, e3 = t3.y, r2 = t3.note, s = M(), l2 = Dr(), u = l2.animate, d3 = l2.config, k2 = useSpring({ x: o3, y: e3, config: d3, immediate: !u });
  return "function" == typeof r2 ? (0, import_react.createElement)(r2, { x: o3, y: e3, datum: n3 }) : (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [s.annotations.text.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.text, { x: k2.x, y: k2.y, style: g({}, s.annotations.text, { strokeLinejoin: "round", strokeWidth: 2 * s.annotations.text.outlineWidth, stroke: s.annotations.text.outlineColor }), children: r2 }), (0, import_jsx_runtime.jsx)(animated.text, { x: k2.x, y: k2.y, style: (0, import_omit.default)(s.annotations.text, ["outlineWidth", "outlineColor"]), children: r2 })] });
};
var E = function(t3) {
  var i3 = t3.points, o3 = t3.isOutline, e3 = void 0 !== o3 && o3, a4 = M(), r2 = (0, import_react.useMemo)(function() {
    var t4 = i3[0];
    return i3.slice(1).reduce(function(t5, n3) {
      return t5 + " L" + n3[0] + "," + n3[1];
    }, "M" + t4[0] + "," + t4[1]);
  }, [i3]), s = It(r2);
  if (e3 && a4.annotations.link.outlineWidth <= 0) return null;
  var l2 = g({}, a4.annotations.link);
  return e3 && (l2.strokeLinecap = "square", l2.strokeWidth = a4.annotations.link.strokeWidth + 2 * a4.annotations.link.outlineWidth, l2.stroke = a4.annotations.link.outlineColor, l2.opacity = a4.annotations.link.outlineOpacity), (0, import_jsx_runtime.jsx)(animated.path, { fill: "none", d: s, style: l2 });
};
var I = function(t3) {
  var n3 = t3.x, i3 = t3.y, o3 = t3.size, e3 = M(), a4 = Dr(), r2 = a4.animate, s = a4.config, l2 = useSpring({ x: n3, y: i3, radius: o3 / 2, config: s, immediate: !r2 });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [e3.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.circle, { cx: l2.x, cy: l2.y, r: l2.radius, style: g({}, e3.annotations.outline, { fill: "none", strokeWidth: e3.annotations.outline.strokeWidth + 2 * e3.annotations.outline.outlineWidth, stroke: e3.annotations.outline.outlineColor, opacity: e3.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime.jsx)(animated.circle, { cx: l2.x, cy: l2.y, r: l2.radius, style: e3.annotations.outline })] });
};
var D = function(t3) {
  var n3 = t3.x, i3 = t3.y, o3 = t3.size, e3 = void 0 === o3 ? k.dotSize : o3, a4 = M(), r2 = Dr(), s = r2.animate, l2 = r2.config, u = useSpring({ x: n3, y: i3, radius: e3 / 2, config: l2, immediate: !s });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [a4.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.circle, { cx: u.x, cy: u.y, r: u.radius, style: g({}, a4.annotations.outline, { fill: "none", strokeWidth: 2 * a4.annotations.outline.outlineWidth, stroke: a4.annotations.outline.outlineColor, opacity: a4.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime.jsx)(animated.circle, { cx: u.x, cy: u.y, r: u.radius, style: a4.annotations.symbol })] });
};
var L = function(t3) {
  var n3 = t3.x, i3 = t3.y, o3 = t3.width, e3 = t3.height, a4 = t3.borderRadius, r2 = void 0 === a4 ? 6 : a4, s = M(), l2 = Dr(), u = l2.animate, d3 = l2.config, k2 = useSpring({ x: n3 - o3 / 2, y: i3 - e3 / 2, width: o3, height: e3, config: d3, immediate: !u });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [s.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime.jsx)(animated.rect, { x: k2.x, y: k2.y, rx: r2, ry: r2, width: k2.width, height: k2.height, style: g({}, s.annotations.outline, { fill: "none", strokeWidth: s.annotations.outline.strokeWidth + 2 * s.annotations.outline.outlineWidth, stroke: s.annotations.outline.outlineColor, opacity: s.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime.jsx)(animated.rect, { x: k2.x, y: k2.y, rx: r2, ry: r2, width: k2.width, height: k2.height, style: s.annotations.outline })] });
};
var R = function(t3) {
  var n3 = t3.datum, i3 = t3.x, o3 = t3.y, e3 = t3.note, a4 = M3(t3);
  if (!W2(e3)) throw new Error("note should be a valid react element");
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [(0, import_jsx_runtime.jsx)(E, { points: a4.points, isOutline: true }), b2(t3) && (0, import_jsx_runtime.jsx)(I, { x: i3, y: o3, size: t3.size }), w(t3) && (0, import_jsx_runtime.jsx)(D, { x: i3, y: o3, size: t3.size }), z2(t3) && (0, import_jsx_runtime.jsx)(L, { x: i3, y: o3, width: t3.width, height: t3.height, borderRadius: t3.borderRadius }), (0, import_jsx_runtime.jsx)(E, { points: a4.points }), (0, import_jsx_runtime.jsx)(T2, { datum: n3, x: a4.text[0], y: a4.text[1], note: e3 })] });
};
var q2 = function(t3, n3) {
  n3.forEach(function(n4, i3) {
    var o3 = n4[0], e3 = n4[1];
    0 === i3 ? t3.moveTo(o3, e3) : t3.lineTo(o3, e3);
  });
};
var J = function(t3, n3) {
  var i3 = n3.annotations, o3 = n3.theme;
  0 !== i3.length && (t3.save(), i3.forEach(function(n4) {
    if (!v(n4.note)) throw new Error("note is invalid for canvas implementation");
    o3.annotations.link.outlineWidth > 0 && (t3.lineCap = "square", t3.strokeStyle = o3.annotations.link.outlineColor, t3.lineWidth = o3.annotations.link.strokeWidth + 2 * o3.annotations.link.outlineWidth, t3.beginPath(), q2(t3, n4.computed.points), t3.stroke(), t3.lineCap = "butt"), b2(n4) && o3.annotations.outline.outlineWidth > 0 && (t3.strokeStyle = o3.annotations.outline.outlineColor, t3.lineWidth = o3.annotations.outline.strokeWidth + 2 * o3.annotations.outline.outlineWidth, t3.beginPath(), t3.arc(n4.x, n4.y, n4.size / 2, 0, 2 * Math.PI), t3.stroke()), w(n4) && o3.annotations.symbol.outlineWidth > 0 && (t3.strokeStyle = o3.annotations.symbol.outlineColor, t3.lineWidth = 2 * o3.annotations.symbol.outlineWidth, t3.beginPath(), t3.arc(n4.x, n4.y, n4.size / 2, 0, 2 * Math.PI), t3.stroke()), z2(n4) && o3.annotations.outline.outlineWidth > 0 && (t3.strokeStyle = o3.annotations.outline.outlineColor, t3.lineWidth = o3.annotations.outline.strokeWidth + 2 * o3.annotations.outline.outlineWidth, t3.beginPath(), t3.rect(n4.x - n4.width / 2, n4.y - n4.height / 2, n4.width, n4.height), t3.stroke()), t3.strokeStyle = o3.annotations.link.stroke, t3.lineWidth = o3.annotations.link.strokeWidth, t3.beginPath(), q2(t3, n4.computed.points), t3.stroke(), b2(n4) && (t3.strokeStyle = o3.annotations.outline.stroke, t3.lineWidth = o3.annotations.outline.strokeWidth, t3.beginPath(), t3.arc(n4.x, n4.y, n4.size / 2, 0, 2 * Math.PI), t3.stroke()), w(n4) && (t3.fillStyle = o3.annotations.symbol.fill, t3.beginPath(), t3.arc(n4.x, n4.y, n4.size / 2, 0, 2 * Math.PI), t3.fill()), z2(n4) && (t3.strokeStyle = o3.annotations.outline.stroke, t3.lineWidth = o3.annotations.outline.strokeWidth, t3.beginPath(), t3.rect(n4.x - n4.width / 2, n4.y - n4.height / 2, n4.width, n4.height), t3.stroke()), "function" == typeof n4.note ? n4.note(t3, { datum: n4.datum, x: n4.computed.text[0], y: n4.computed.text[1], theme: o3 }) : (t3.font = o3.annotations.text.fontSize + "px " + o3.annotations.text.fontFamily, t3.textAlign = "left", t3.textBaseline = "alphabetic", t3.fillStyle = o3.annotations.text.fill, t3.strokeStyle = o3.annotations.text.outlineColor, t3.lineWidth = 2 * o3.annotations.text.outlineWidth, o3.annotations.text.outlineWidth > 0 && (t3.lineJoin = "round", t3.strokeText(n4.note, n4.computed.text[0], n4.computed.text[1]), t3.lineJoin = "miter"), t3.fillText(n4.note, n4.computed.text[0], n4.computed.text[1]));
  }), t3.restore());
};

// node_modules/@nivo/heatmap/dist/nivo-heatmap.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);

// node_modules/@nivo/heatmap/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs
var import_react_dom2 = __toESM(require_react_dom(), 1);
var isCustomPropRE2 = /^--/;
function dangerousStyleValue2(name, value) {
  if (value == null || typeof value === "boolean" || value === "") return "";
  if (typeof value === "number" && value !== 0 && !isCustomPropRE2.test(name) && !(isUnitlessNumber2.hasOwnProperty(name) && isUnitlessNumber2[name]))
    return value + "px";
  return ("" + value).trim();
}
var attributeCache2 = {};
function applyAnimatedValues2(instance, props) {
  if (!instance.nodeType || !instance.setAttribute) {
    return false;
  }
  const isFilterElement = instance.nodeName === "filter" || instance.parentNode && instance.parentNode.nodeName === "filter";
  const {
    className,
    style,
    children,
    scrollTop,
    scrollLeft,
    viewBox,
    ...attributes
  } = props;
  const values = Object.values(attributes);
  const names = Object.keys(attributes).map(
    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache2[name] || (attributeCache2[name] = name.replace(
      /([A-Z])/g,
      // Attributes are written in dash case
      (n3) => "-" + n3.toLowerCase()
    ))
  );
  if (children !== void 0) {
    instance.textContent = children;
  }
  for (const name in style) {
    if (style.hasOwnProperty(name)) {
      const value = dangerousStyleValue2(name, style[name]);
      if (isCustomPropRE2.test(name)) {
        instance.style.setProperty(name, value);
      } else {
        instance.style[name] = value;
      }
    }
  }
  names.forEach((name, i3) => {
    instance.setAttribute(name, values[i3]);
  });
  if (className !== void 0) {
    instance.className = className;
  }
  if (scrollTop !== void 0) {
    instance.scrollTop = scrollTop;
  }
  if (scrollLeft !== void 0) {
    instance.scrollLeft = scrollLeft;
  }
  if (viewBox !== void 0) {
    instance.setAttribute("viewBox", viewBox);
  }
}
var isUnitlessNumber2 = {
  animationIterationCount: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var prefixKey2 = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);
var prefixes2 = ["Webkit", "Ms", "Moz", "O"];
isUnitlessNumber2 = Object.keys(isUnitlessNumber2).reduce((acc, prop) => {
  prefixes2.forEach((prefix) => acc[prefixKey2(prefix, prop)] = acc[prop]);
  return acc;
}, isUnitlessNumber2);
var domTransforms2 = /^(matrix|translate|scale|rotate|skew)/;
var pxTransforms2 = /^(translate)/;
var degTransforms2 = /^(rotate|skew)/;
var addUnit2 = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;
var isValueIdentity2 = (value, id) => is.arr(value) ? value.every((v2) => isValueIdentity2(v2, id)) : is.num(value) ? value === id : parseFloat(value) === id;
var AnimatedStyle2 = class extends AnimatedObject {
  constructor({ x: x2, y, z: z3, ...style }) {
    const inputs = [];
    const transforms = [];
    if (x2 || y || z3) {
      inputs.push([x2 || 0, y || 0, z3 || 0]);
      transforms.push((xyz) => [
        `translate3d(${xyz.map((v2) => addUnit2(v2, "px")).join(",")})`,
        // prettier-ignore
        isValueIdentity2(xyz, 0)
      ]);
    }
    eachProp(style, (value, key) => {
      if (key === "transform") {
        inputs.push([value || ""]);
        transforms.push((transform) => [transform, transform === ""]);
      } else if (domTransforms2.test(key)) {
        delete style[key];
        if (is.und(value)) return;
        const unit = pxTransforms2.test(key) ? "px" : degTransforms2.test(key) ? "deg" : "";
        inputs.push(toArray(value));
        transforms.push(
          key === "rotate3d" ? ([x22, y2, z22, deg]) => [
            `rotate3d(${x22},${y2},${z22},${addUnit2(deg, unit)})`,
            isValueIdentity2(deg, 0)
          ] : (input) => [
            `${key}(${input.map((v2) => addUnit2(v2, unit)).join(",")})`,
            isValueIdentity2(input, key.startsWith("scale") ? 1 : 0)
          ]
        );
      }
    });
    if (inputs.length) {
      style.transform = new FluidTransform2(inputs, transforms);
    }
    super(style);
  }
};
var FluidTransform2 = class extends FluidValue {
  constructor(inputs, transforms) {
    super();
    this.inputs = inputs;
    this.transforms = transforms;
    this._value = null;
  }
  get() {
    return this._value || (this._value = this._get());
  }
  _get() {
    let transform = "";
    let identity = true;
    each(this.inputs, (input, i3) => {
      const arg1 = getFluidValue(input[0]);
      const [t3, id] = this.transforms[i3](
        is.arr(arg1) ? arg1 : input.map(getFluidValue)
      );
      transform += " " + t3;
      identity = identity && id;
    });
    return identity ? "none" : transform;
  }
  // Start observing our inputs once we have an observer.
  observerAdded(count) {
    if (count == 1)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && addFluidObserver(value, this)
        )
      );
  }
  // Stop observing our inputs once we have no observers.
  observerRemoved(count) {
    if (count == 0)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && removeFluidObserver(value, this)
        )
      );
  }
  eventObserved(event) {
    if (event.type == "change") {
      this._value = null;
    }
    callFluidObservers(this, event);
  }
};
var primitives2 = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "big",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rp",
  "rt",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr",
  // SVG
  "circle",
  "clipPath",
  "defs",
  "ellipse",
  "foreignObject",
  "g",
  "image",
  "line",
  "linearGradient",
  "mask",
  "path",
  "pattern",
  "polygon",
  "polyline",
  "radialGradient",
  "rect",
  "stop",
  "svg",
  "text",
  "tspan"
];
globals_exports.assign({
  batchedUpdates: import_react_dom2.unstable_batchedUpdates,
  createStringInterpolator: createStringInterpolator2,
  colors: colors2
});
var host2 = createHost(primitives2, {
  applyAnimatedValues: applyAnimatedValues2,
  createAnimatedStyle: (style) => new AnimatedStyle2(style),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props
});
var animated2 = host2.animated;

// node_modules/@nivo/heatmap/dist/nivo-heatmap.mjs
function _() {
  return _ = Object.assign ? Object.assign.bind() : function(e3) {
    for (var i3 = 1; i3 < arguments.length; i3++) {
      var t3 = arguments[i3];
      for (var o3 in t3) ({}).hasOwnProperty.call(t3, o3) && (e3[o3] = t3[o3]);
    }
    return e3;
  }, _.apply(null, arguments);
}
function J2(e3, i3) {
  if (null == e3) return {};
  var t3 = {};
  for (var o3 in e3) if ({}.hasOwnProperty.call(e3, o3)) {
    if (-1 !== i3.indexOf(o3)) continue;
    t3[o3] = e3[o3];
  }
  return t3;
}
var K = { layers: ["grid", "axes", "cells", "legends", "annotations"], forceSquare: false, xInnerPadding: 0, xOuterPadding: 0, yInnerPadding: 0, yOuterPadding: 0, sizeVariation: false, opacity: 1, activeOpacity: 1, inactiveOpacity: 0.15, borderWidth: 0, borderColor: { from: "color", modifiers: [["darker", 0.8]] }, enableGridX: false, enableGridY: false, enableLabels: true, label: "formattedValue", labelTextColor: { from: "color", modifiers: [["darker", 2]] }, colors: { type: "sequential", scheme: "brown_blueGreen" }, emptyColor: "#000000", legends: [], annotations: [], isInteractive: true, hoverTarget: "rowColumn", tooltip: (0, import_react2.memo)(function(e3) {
  var i3 = e3.cell;
  return null === i3.formattedValue ? null : (0, import_jsx_runtime2.jsx)(T, { id: i3.serieId + " - " + i3.data.x, value: i3.formattedValue, enableChip: true, color: i3.color });
}), animate: true, motionConfig: "gentle" };
var N = _({}, K, { axisTop: {}, axisRight: null, axisBottom: null, axisLeft: {}, borderRadius: 0, cellComponent: "rect" });
var Q = _({}, K, { axisTop: {}, axisRight: null, axisBottom: null, axisLeft: {}, renderCell: "rect", pixelRatio: "undefined" != typeof window && window.devicePixelRatio || 1 });
var U = function(e3) {
  var i3 = e3.width, t3 = e3.height, o3 = e3.rows, n3 = e3.columns, r2 = i3, a4 = t3, l2 = 0, d3 = 0;
  if (e3.forceSquare) {
    var c = Math.max(i3 / n3, 0), u = Math.max(t3 / o3, 0), s = Math.min(c, u);
    l2 = (i3 - (r2 = s * n3)) / 2, d3 = (t3 - (a4 = s * o3)) / 2;
  }
  return { offsetX: l2, offsetY: d3, width: r2, height: a4 };
};
var Z = function(e3) {
  var i3 = e3.data, t3 = e3.width, o3 = e3.height, n3 = e3.xInnerPadding, r2 = e3.xOuterPadding, a4 = e3.yInnerPadding, l2 = e3.yOuterPadding, d3 = e3.forceSquare, c = /* @__PURE__ */ new Set(), u = [], s = [], h = [];
  i3.forEach(function(e4) {
    u.push(e4.id), e4.data.forEach(function(i4) {
      c.add(i4.x);
      var t4 = null;
      void 0 !== i4.y && null !== i4.y && (s.push(i4.y), t4 = i4.y), h.push({ id: e4.id + "." + i4.x, serieId: e4.id, value: t4, data: i4 });
    });
  });
  var f = Array.from(c), v2 = U({ width: t3, height: o3, columns: f.length, rows: u.length, forceSquare: d3 }), g2 = v2.width, b3 = v2.height, m2 = v2.offsetX, p2 = v2.offsetY, y = an(band().domain(f).range([0, g2]).paddingOuter(r2).paddingInner(n3)), x2 = an(band().domain(u).range([0, b3]).paddingOuter(l2).paddingInner(a4)), C2 = y.bandwidth(), w2 = x2.bandwidth(), P2 = h.map(function(e4) {
    return _({}, e4, { x: y(e4.data.x) + C2 / 2, y: x2(e4.serieId) + w2 / 2, width: C2, height: w2 });
  });
  return { width: g2, height: b3, offsetX: m2, offsetY: p2, xScale: y, yScale: x2, minValue: Math.min.apply(Math, s), maxValue: Math.max.apply(Math, s), cells: P2 };
};
var $ = function(e3, i3, t3) {
  if (!e3) return function() {
    return 1;
  };
  var o3 = linear().domain(e3.values ? e3.values : [i3, t3]).range(e3.sizes);
  return function(e4) {
    return null === e4 ? 1 : o3(e4);
  };
};
var ee = function(e3) {
  return { x: e3.x, y: e3.y };
};
var ie = function(e3) {
  return { size: Math.max(e3.width, e3.height), width: e3.width, height: e3.height };
};
var te = function(e3) {
  var t3 = e3.data, o3 = e3.width, n3 = e3.height, r2 = e3.xInnerPadding, a4 = e3.xOuterPadding, l2 = e3.yInnerPadding, d3 = e3.yOuterPadding, c = e3.forceSquare;
  return (0, import_react2.useMemo)(function() {
    return Z({ data: t3, width: o3, height: n3, xInnerPadding: r2, xOuterPadding: a4, yInnerPadding: l2, yOuterPadding: d3, forceSquare: c });
  }, [t3, o3, n3, r2, a4, l2, d3, c]);
};
var oe = { cell: function(e3, i3) {
  return e3.id === i3.id;
}, row: function(e3, i3) {
  return e3.serieId === i3.serieId;
}, column: function(e3, i3) {
  return e3.data.x === i3.data.x;
}, rowColumn: function(e3, i3) {
  return e3.serieId === i3.serieId || e3.data.x === i3.data.x;
} };
var ne = function(e3) {
  var t3, n3, r2, a4 = e3.cells, l2 = e3.minValue, d3 = e3.maxValue, s = e3.sizeVariation, h = e3.colors, f = e3.emptyColor, v2 = e3.opacity, g2 = e3.activeOpacity, b3 = e3.inactiveOpacity, m2 = e3.borderColor, p2 = e3.label, y = e3.labelTextColor, x2 = e3.valueFormat, C2 = e3.activeIds, w2 = (0, import_react2.useMemo)(function() {
    return $(t3, n3, r2);
  }, [t3 = s, n3 = l2, r2 = d3]), P2 = (0, import_react2.useMemo)(function() {
    return "function" == typeof h ? null : pr(h, { min: l2, max: d3 });
  }, [h, l2, d3]), O2 = (0, import_react2.useCallback)(function(e4) {
    if (null !== e4.value) {
      if ("function" == typeof h) return h(e4);
      if (null !== P2) return P2(e4.value);
    }
    return f;
  }, [h, P2, f]), T3 = M(), W3 = Ye(m2, T3), R2 = Ye(y, T3), L2 = hn(x2), k2 = bn(p2);
  return { cells: (0, import_react2.useMemo)(function() {
    return a4.map(function(e4) {
      var i3 = v2;
      C2.length > 0 && (i3 = C2.includes(e4.id) ? g2 : b3);
      var t4 = w2(e4.value), o3 = _({}, e4, { width: e4.width * t4, height: e4.height * t4, formattedValue: null !== e4.value ? L2(e4.value) : null, opacity: i3 });
      return o3.label = k2(o3), o3.color = O2(o3), o3.borderColor = W3(o3), o3.labelTextColor = R2(o3), o3;
    });
  }, [a4, w2, O2, W3, R2, L2, k2, C2, v2, g2, b3]), colorScale: P2 };
};
var re = function(e3) {
  var o3 = e3.data, n3 = e3.valueFormat, r2 = e3.width, a4 = e3.height, l2 = e3.xOuterPadding, d3 = void 0 === l2 ? K.xOuterPadding : l2, c = e3.xInnerPadding, u = void 0 === c ? K.xInnerPadding : c, s = e3.yOuterPadding, h = void 0 === s ? K.yOuterPadding : s, f = e3.yInnerPadding, v2 = void 0 === f ? K.yInnerPadding : f, g2 = e3.forceSquare, b3 = void 0 === g2 ? K.forceSquare : g2, m2 = e3.sizeVariation, p2 = void 0 === m2 ? K.sizeVariation : m2, y = e3.colors, x2 = void 0 === y ? K.colors : y, C2 = e3.emptyColor, w2 = void 0 === C2 ? K.emptyColor : C2, P2 = e3.opacity, O2 = void 0 === P2 ? K.opacity : P2, I2 = e3.activeOpacity, M4 = void 0 === I2 ? K.activeOpacity : I2, S2 = e3.inactiveOpacity, T3 = void 0 === S2 ? K.inactiveOpacity : S2, W3 = e3.borderColor, R2 = void 0 === W3 ? K.borderColor : W3, L2 = e3.label, k2 = void 0 === L2 ? K.label : L2, z3 = e3.labelTextColor, V3 = void 0 === z3 ? K.labelTextColor : z3, B2 = e3.hoverTarget, q4 = void 0 === B2 ? K.hoverTarget : B2, E2 = (0, import_react2.useState)(null), A = E2[0], H = E2[1], X = te({ data: o3, width: r2, height: a4, xOuterPadding: d3, xInnerPadding: u, yOuterPadding: h, yInnerPadding: v2, forceSquare: b3 }), Y2 = X.width, G = X.height, F = X.offsetX, j3 = X.offsetY, D2 = X.cells, _2 = X.xScale, J3 = X.yScale, N2 = X.minValue, Q2 = X.maxValue, U2 = (0, import_react2.useMemo)(function() {
    if (!A) return [];
    var e4 = oe[q4];
    return D2.filter(function(i3) {
      return e4(i3, A);
    }).map(function(e5) {
      return e5.id;
    });
  }, [D2, A, q4]), Z2 = ne({ cells: D2, minValue: N2, maxValue: Q2, sizeVariation: p2, colors: x2, emptyColor: w2, opacity: O2, activeOpacity: M4, inactiveOpacity: T3, borderColor: R2, label: k2, labelTextColor: V3, valueFormat: n3, activeIds: U2 });
  return { width: Y2, height: G, offsetX: F, offsetY: j3, cells: Z2.cells, xScale: _2, yScale: J3, colorScale: Z2.colorScale, activeCell: A, setActiveCell: H };
};
var ae = function(e3, i3) {
  return O({ data: e3, annotations: i3, getPosition: ee, getDimensions: ie });
};
var le = (0, import_react2.memo)(function(e3) {
  var t3 = e3.cell, o3 = e3.borderWidth, n3 = e3.borderRadius, r2 = e3.animatedProps, a4 = e3.onMouseEnter, l2 = e3.onMouseMove, d3 = e3.onMouseLeave, c = e3.onClick, u = e3.enableLabels, s = M(), h = (0, import_react2.useMemo)(function() {
    return { onMouseEnter: a4 ? a4(t3) : void 0, onMouseMove: l2 ? l2(t3) : void 0, onMouseLeave: d3 ? d3(t3) : void 0, onClick: c ? c(t3) : void 0 };
  }, [t3, a4, l2, d3, c]);
  return (0, import_jsx_runtime2.jsxs)(animated2.g, _({ "data-testid": "cell." + t3.id, style: { cursor: "pointer" }, opacity: r2.opacity }, h, { transform: to([r2.x, r2.y, r2.scale], function(e4, i3, t4) {
    return "translate(" + e4 + ", " + i3 + ") scale(" + t4 + ")";
  }), children: [(0, import_jsx_runtime2.jsx)(animated2.rect, { transform: to([r2.width, r2.height], function(e4, i3) {
    return "translate(" + -0.5 * e4 + ", " + -0.5 * i3 + ")";
  }), fill: r2.color, width: r2.width, height: r2.height, stroke: r2.borderColor, strokeWidth: o3, rx: n3, ry: n3 }, t3.id), u && (0, import_jsx_runtime2.jsx)(b, { textAnchor: "middle", dominantBaseline: "central", fill: r2.labelTextColor, style: _({}, s.labels.text, { fill: void 0, userSelect: "none" }), children: t3.label })] }));
});
var de = (0, import_react2.memo)(function(e3) {
  var t3 = e3.cell, o3 = e3.borderWidth, n3 = e3.animatedProps, r2 = e3.onMouseEnter, a4 = e3.onMouseMove, l2 = e3.onMouseLeave, d3 = e3.onClick, c = e3.enableLabels, u = M(), s = (0, import_react2.useMemo)(function() {
    return { onMouseEnter: r2 ? r2(t3) : void 0, onMouseMove: a4 ? a4(t3) : void 0, onMouseLeave: l2 ? l2(t3) : void 0, onClick: d3 ? d3(t3) : void 0 };
  }, [t3, r2, a4, l2, d3]);
  return (0, import_jsx_runtime2.jsxs)(animated2.g, _({ "data-testid": "cell." + t3.id, style: { cursor: "pointer" }, opacity: n3.opacity }, s, { transform: to([n3.x, n3.y], function(e4, i3) {
    return "translate(" + e4 + ", " + i3 + ")";
  }), children: [(0, import_jsx_runtime2.jsx)(animated2.circle, { r: to([n3.width, n3.height], function(e4, i3) {
    return Math.min(e4, i3) / 2;
  }), fill: n3.color, fillOpacity: n3.opacity, strokeWidth: o3, stroke: n3.borderColor }), c && (0, import_jsx_runtime2.jsx)(b, { dominantBaseline: "central", textAnchor: "middle", fill: n3.labelTextColor, style: _({}, u.labels.text, { fill: void 0 }), children: t3.label })] }));
});
var ce = function(e3) {
  return { x: e3.x, y: e3.y, width: e3.width, height: e3.height, color: e3.color, opacity: 0, borderColor: e3.borderColor, labelTextColor: e3.labelTextColor, scale: 0 };
};
var ue = function(e3) {
  return { x: e3.x, y: e3.y, width: e3.width, height: e3.height, color: e3.color, opacity: e3.opacity, borderColor: e3.borderColor, labelTextColor: e3.labelTextColor, scale: 1 };
};
var se = function(e3) {
  return { x: e3.x, y: e3.y, width: e3.width, height: e3.height, color: e3.color, opacity: 0, borderColor: e3.borderColor, labelTextColor: e3.labelTextColor, scale: 0 };
};
var he = function(e3) {
  var t3, o3 = e3.cells, r2 = e3.cellComponent, a4 = e3.borderRadius, l2 = e3.borderWidth, d3 = e3.isInteractive, c = e3.setActiveCell, u = e3.onMouseEnter, h = e3.onMouseMove, f = e3.onMouseLeave, v2 = e3.onClick, g2 = e3.tooltip, b3 = e3.enableLabels, m2 = Dr(), p2 = m2.animate, y = m2.config, x2 = useTransition(o3, { keys: function(e4) {
    return e4.id;
  }, initial: ue, from: ce, enter: ue, update: ue, leave: se, config: y, immediate: !p2 }), C2 = z(), w2 = C2.showTooltipFromEvent, P2 = C2.hideTooltip, O2 = (0, import_react2.useMemo)(function() {
    if (d3) return function(e4) {
      return function(i3) {
        w2((0, import_react2.createElement)(g2, { cell: e4 }), i3), c(e4), null == u || u(e4, i3);
      };
    };
  }, [d3, w2, g2, c, u]), I2 = (0, import_react2.useMemo)(function() {
    if (d3) return function(e4) {
      return function(i3) {
        w2((0, import_react2.createElement)(g2, { cell: e4 }), i3), null == h || h(e4, i3);
      };
    };
  }, [d3, w2, g2, h]), M4 = (0, import_react2.useMemo)(function() {
    if (d3) return function(e4) {
      return function(i3) {
        P2(), c(null), null == f || f(e4, i3);
      };
    };
  }, [d3, P2, c, f]), S2 = (0, import_react2.useMemo)(function() {
    if (d3) return function(e4) {
      return function(i3) {
        null == v2 || v2(e4, i3);
      };
    };
  }, [d3, v2]);
  return t3 = "rect" === r2 ? le : "circle" === r2 ? de : r2, (0, import_jsx_runtime2.jsx)(import_jsx_runtime2.Fragment, { children: x2(function(e4, i3) {
    return (0, import_react2.createElement)(t3, { cell: i3, borderRadius: a4, borderWidth: l2, animatedProps: e4, enableLabels: b3, onMouseEnter: O2, onMouseMove: I2, onMouseLeave: M4, onClick: S2 });
  }) });
};
var fe = function(e3) {
  var i3 = e3.cells, t3 = e3.annotations, o3 = ae(i3, t3);
  return (0, import_jsx_runtime2.jsx)(import_jsx_runtime2.Fragment, { children: o3.map(function(e4, i4) {
    return (0, import_jsx_runtime2.jsx)(R, _({}, e4), i4);
  }) });
};
var ve = ["isInteractive", "animate", "motionConfig", "theme", "renderWrapper"];
var ge = function(e3) {
  var t3 = e3.data, o3 = e3.layers, r2 = void 0 === o3 ? N.layers : o3, l2 = e3.valueFormat, d3 = e3.width, c = e3.height, u = e3.margin, s = e3.forceSquare, h = void 0 === s ? N.forceSquare : s, g2 = e3.xInnerPadding, b3 = void 0 === g2 ? N.xInnerPadding : g2, m2 = e3.xOuterPadding, p2 = void 0 === m2 ? N.xOuterPadding : m2, C2 = e3.yInnerPadding, w2 = void 0 === C2 ? N.yInnerPadding : C2, O2 = e3.yOuterPadding, I2 = void 0 === O2 ? N.yOuterPadding : O2, M4 = e3.sizeVariation, S2 = void 0 === M4 ? N.sizeVariation : M4, T3 = e3.cellComponent, W3 = void 0 === T3 ? N.cellComponent : T3, R2 = e3.opacity, L2 = void 0 === R2 ? N.opacity : R2, k2 = e3.activeOpacity, z3 = void 0 === k2 ? N.activeOpacity : k2, B2 = e3.inactiveOpacity, q4 = void 0 === B2 ? N.inactiveOpacity : B2, E2 = e3.borderRadius, A = void 0 === E2 ? N.borderRadius : E2, H = e3.borderWidth, X = void 0 === H ? N.borderWidth : H, Y2 = e3.borderColor, G = void 0 === Y2 ? N.borderColor : Y2, F = e3.enableGridX, j3 = void 0 === F ? N.enableGridX : F, D2 = e3.enableGridY, J3 = void 0 === D2 ? N.enableGridY : D2, K2 = e3.axisTop, Q2 = void 0 === K2 ? N.axisTop : K2, U2 = e3.axisRight, Z2 = void 0 === U2 ? N.axisRight : U2, $2 = e3.axisBottom, ee2 = void 0 === $2 ? N.axisBottom : $2, ie2 = e3.axisLeft, te2 = void 0 === ie2 ? N.axisLeft : ie2, oe2 = e3.enableLabels, ne2 = void 0 === oe2 ? N.enableLabels : oe2, ae2 = e3.label, le2 = void 0 === ae2 ? N.label : ae2, de2 = e3.labelTextColor, ce2 = void 0 === de2 ? N.labelTextColor : de2, ue2 = e3.colors, se2 = void 0 === ue2 ? N.colors : ue2, ve2 = e3.emptyColor, ge2 = void 0 === ve2 ? N.emptyColor : ve2, be2 = e3.legends, me2 = void 0 === be2 ? N.legends : be2, pe2 = e3.annotations, ye2 = void 0 === pe2 ? N.annotations : pe2, xe2 = e3.isInteractive, Ce2 = void 0 === xe2 ? N.isInteractive : xe2, we2 = e3.onMouseEnter, Pe2 = e3.onMouseMove, Oe2 = e3.onMouseLeave, Ie2 = e3.onClick, Me = e3.hoverTarget, Se = void 0 === Me ? N.hoverTarget : Me, Te = e3.tooltip, We = void 0 === Te ? N.tooltip : Te, Re = e3.role, Le = e3.ariaLabel, ke = e3.ariaLabelledBy, ze = e3.ariaDescribedBy, Ve = e3.forwardedRef, Be = cn(d3, c, u), qe = Be.margin, Ee = Be.innerWidth, Ae = Be.innerHeight, He = Be.outerWidth, Xe = Be.outerHeight, Ye2 = re({ data: t3, valueFormat: l2, width: Ee, height: Ae, forceSquare: h, xInnerPadding: b3, xOuterPadding: p2, yInnerPadding: w2, yOuterPadding: I2, sizeVariation: S2, colors: se2, emptyColor: ge2, opacity: L2, activeOpacity: z3, inactiveOpacity: q4, borderColor: G, label: le2, labelTextColor: ce2, hoverTarget: Se }), Ge = Ye2.width, Fe = Ye2.height, je = Ye2.offsetX, De = Ye2.offsetY, _e = Ye2.xScale, Je = Ye2.yScale, Ke = Ye2.cells, Ne = Ye2.colorScale, Qe = Ye2.activeCell, Ue = Ye2.setActiveCell, Ze = (0, import_react2.useMemo)(function() {
    return _({}, qe, { top: qe.top + De, left: qe.left + je });
  }, [qe, je, De]), $e = { grid: null, axes: null, cells: null, legends: null, annotations: null };
  r2.includes("grid") && ($e.grid = (0, import_jsx_runtime2.jsx)(j, { width: Ge, height: Fe, xScale: j3 ? _e : null, yScale: J3 ? Je : null }, "grid")), r2.includes("axes") && ($e.axes = (0, import_jsx_runtime2.jsx)(V, { xScale: _e, yScale: Je, width: Ge, height: Fe, top: Q2, right: Z2, bottom: ee2, left: te2 }, "axes")), r2.includes("cells") && ($e.cells = (0, import_jsx_runtime2.jsx)(import_react2.Fragment, { children: (0, import_jsx_runtime2.jsx)(he, { cells: Ke, cellComponent: W3, borderRadius: A, borderWidth: X, isInteractive: Ce2, setActiveCell: Ue, onMouseEnter: we2, onMouseMove: Pe2, onMouseLeave: Oe2, onClick: Ie2, tooltip: We, enableLabels: ne2 }) }, "cells")), r2.includes("legends") && null !== Ne && ($e.legends = (0, import_jsx_runtime2.jsx)(import_react2.Fragment, { children: me2.map(function(e4, i3) {
    return (0, import_react2.createElement)(Y, _({}, e4, { key: i3, containerWidth: Ge, containerHeight: Fe, scale: Ne }));
  }) }, "legends")), r2.includes("annotations") && ye2.length > 0 && ($e.annotations = (0, import_jsx_runtime2.jsx)(fe, { cells: Ke, annotations: ye2 }, "annotations"));
  var ei = { cells: Ke, activeCell: Qe, setActiveCell: Ue };
  return (0, import_jsx_runtime2.jsx)(Rt, { width: He, height: Xe, margin: Object.assign({}, Ze, { top: Ze.top, left: Ze.left }), role: Re, ariaLabel: Le, ariaLabelledBy: ke, ariaDescribedBy: ze, ref: Ve, children: r2.map(function(e4, i3) {
    var t4;
    return "function" == typeof e4 ? (0, import_jsx_runtime2.jsx)(import_react2.Fragment, { children: (0, import_react2.createElement)(e4, ei) }, i3) : null != (t4 = null == $e ? void 0 : $e[e4]) ? t4 : null;
  }) });
};
var be = (0, import_react2.forwardRef)(function(e3, i3) {
  var t3 = e3.isInteractive, o3 = void 0 === t3 ? N.isInteractive : t3, n3 = e3.animate, r2 = void 0 === n3 ? N.animate : n3, a4 = e3.motionConfig, l2 = void 0 === a4 ? N.motionConfig : a4, d3 = e3.theme, c = e3.renderWrapper, u = J2(e3, ve);
  return (0, import_jsx_runtime2.jsx)(Fr, { animate: r2, isInteractive: o3, motionConfig: l2, renderWrapper: c, theme: d3, children: (0, import_jsx_runtime2.jsx)(ge, _({ isInteractive: o3 }, u, { forwardedRef: i3 })) });
});
var me = ["defaultWidth", "defaultHeight", "onResize", "debounceResize"];
var pe = (0, import_react2.forwardRef)(function(e3, i3) {
  var t3 = e3.defaultWidth, o3 = e3.defaultHeight, n3 = e3.onResize, r2 = e3.debounceResize, a4 = J2(e3, me);
  return (0, import_jsx_runtime2.jsx)($r, { defaultWidth: t3, defaultHeight: o3, onResize: n3, debounceResize: r2, children: function(e4) {
    var t4 = e4.width, o4 = e4.height;
    return (0, import_jsx_runtime2.jsx)(be, _({ width: t4, height: o4 }, a4, { ref: i3 }));
  } });
});
var ye = function(e3, i3) {
  var t3 = i3.cell, o3 = t3.x, n3 = t3.y, r2 = t3.width, a4 = t3.height, l2 = t3.color, d3 = t3.borderColor, c = t3.opacity, u = t3.labelTextColor, s = t3.label, h = i3.borderWidth, f = i3.enableLabels, v2 = i3.theme;
  e3.save(), e3.globalAlpha = c, e3.fillStyle = l2, h > 0 && (e3.strokeStyle = d3, e3.lineWidth = h), e3.fillRect(o3 - r2 / 2, n3 - a4 / 2, r2, a4), h > 0 && e3.strokeRect(o3 - r2 / 2, n3 - a4 / 2, r2, a4), f && (a(e3, v2.labels.text), e3.textAlign = "center", e3.textBaseline = "middle", d(e3, _({}, v2.labels.text, { fill: u }), s, o3, n3)), e3.restore();
};
var xe = function(e3, i3) {
  var t3 = i3.cell, o3 = t3.x, n3 = t3.y, r2 = t3.width, a4 = t3.height, l2 = t3.color, d3 = t3.borderColor, c = t3.opacity, u = t3.labelTextColor, s = t3.label, h = i3.borderWidth, f = i3.enableLabels, v2 = i3.theme;
  e3.save(), e3.globalAlpha = c;
  var g2 = Math.min(r2, a4) / 2;
  e3.fillStyle = l2, h > 0 && (e3.strokeStyle = d3, e3.lineWidth = h), e3.beginPath(), e3.arc(o3, n3, g2, 0, 2 * Math.PI), e3.fill(), h > 0 && e3.stroke(), f && (a(e3, v2.labels.text), e3.textAlign = "center", e3.textBaseline = "middle", d(e3, _({}, v2.labels.text, { fill: u }), s, o3, n3)), e3.restore();
};
var Ce = ["theme", "isInteractive", "animate", "motionConfig", "renderWrapper"];
var we = function(e3) {
  var t3, r2 = e3.data, a4 = e3.layers, c = void 0 === a4 ? Q.layers : a4, u = e3.valueFormat, s = e3.width, h = e3.height, v2 = e3.margin, g2 = e3.xInnerPadding, y = void 0 === g2 ? Q.xInnerPadding : g2, x2 = e3.xOuterPadding, P2 = void 0 === x2 ? Q.xOuterPadding : x2, M4 = e3.yInnerPadding, S2 = void 0 === M4 ? Q.yInnerPadding : M4, T3 = e3.yOuterPadding, W3 = void 0 === T3 ? Q.yOuterPadding : T3, k2 = e3.forceSquare, B2 = void 0 === k2 ? Q.forceSquare : k2, q4 = e3.sizeVariation, E2 = void 0 === q4 ? Q.sizeVariation : q4, A = e3.renderCell, H = void 0 === A ? Q.renderCell : A, X = e3.opacity, Y2 = void 0 === X ? Q.opacity : X, G = e3.activeOpacity, F = void 0 === G ? Q.activeOpacity : G, j3 = e3.inactiveOpacity, D2 = void 0 === j3 ? Q.inactiveOpacity : j3, J3 = e3.borderWidth, K2 = void 0 === J3 ? Q.borderWidth : J3, N2 = e3.borderColor, U2 = void 0 === N2 ? Q.borderColor : N2, Z2 = e3.enableGridX, $2 = void 0 === Z2 ? Q.enableGridX : Z2, ee2 = e3.enableGridY, ie2 = void 0 === ee2 ? Q.enableGridY : ee2, te2 = e3.axisTop, oe2 = void 0 === te2 ? Q.axisTop : te2, ne2 = e3.axisRight, le2 = void 0 === ne2 ? Q.axisRight : ne2, de2 = e3.axisBottom, ce2 = void 0 === de2 ? Q.axisBottom : de2, ue2 = e3.axisLeft, se2 = void 0 === ue2 ? Q.axisLeft : ue2, he2 = e3.enableLabels, fe2 = void 0 === he2 ? Q.enableLabels : he2, ve2 = e3.label, ge2 = void 0 === ve2 ? Q.label : ve2, be2 = e3.labelTextColor, me2 = void 0 === be2 ? Q.labelTextColor : be2, pe2 = e3.colors, Ce2 = void 0 === pe2 ? Q.colors : pe2, we2 = e3.emptyColor, Pe2 = void 0 === we2 ? Q.emptyColor : we2, Oe2 = e3.legends, Ie2 = void 0 === Oe2 ? Q.legends : Oe2, Me = e3.annotations, Se = void 0 === Me ? Q.annotations : Me, Te = e3.isInteractive, We = void 0 === Te ? Q.isInteractive : Te, Re = e3.onClick, Le = e3.hoverTarget, ke = void 0 === Le ? Q.hoverTarget : Le, ze = e3.tooltip, Ve = void 0 === ze ? Q.tooltip : ze, Be = e3.role, qe = e3.ariaLabel, Ee = e3.ariaLabelledBy, Ae = e3.ariaDescribedBy, He = e3.pixelRatio, Xe = void 0 === He ? Q.pixelRatio : He, Ye2 = e3.forwardedRef, Ge = (0, import_react2.useRef)(null), Fe = cn(s, h, v2), je = Fe.margin, De = Fe.innerWidth, _e = Fe.innerHeight, Je = Fe.outerWidth, Ke = Fe.outerHeight, Ne = re({ data: r2, valueFormat: u, width: De, height: _e, xInnerPadding: y, xOuterPadding: P2, yInnerPadding: S2, yOuterPadding: W3, forceSquare: B2, sizeVariation: E2, colors: Ce2, emptyColor: Pe2, opacity: Y2, activeOpacity: F, inactiveOpacity: D2, borderColor: U2, label: ge2, labelTextColor: me2, hoverTarget: ke }), Qe = Ne.width, Ue = Ne.height, Ze = Ne.offsetX, $e = Ne.offsetY, ei = Ne.xScale, ii = Ne.yScale, ti = Ne.cells, oi = Ne.colorScale, ni = Ne.activeCell, ri = Ne.setActiveCell, ai = (0, import_react2.useMemo)(function() {
    return _({}, je, { top: je.top + $e, left: je.left + Ze });
  }, [je, Ze, $e]), li = ae(ti, Se), di = j2({ annotations: li });
  t3 = "function" == typeof H ? H : "circle" === H ? xe : ye;
  var ci = M(), ui = (0, import_react2.useMemo)(function() {
    return { cells: ti, activeCell: ni, setActiveCell: ri };
  }, [ti, ni, ri]);
  (0, import_react2.useEffect)(function() {
    if (null !== Ge.current) {
      var e4 = Ge.current.getContext("2d");
      e4 && (Ge.current.width = Je * Xe, Ge.current.height = Ke * Xe, e4.scale(Xe, Xe), e4.fillStyle = ci.background, e4.fillRect(0, 0, Je, Ke), e4.translate(ai.left, ai.top), c.forEach(function(i3) {
        "grid" === i3 ? (e4.lineWidth = ci.grid.line.strokeWidth, e4.strokeStyle = ci.grid.line.stroke, $2 && q(e4, { width: Qe, height: Ue, scale: ei, axis: "x" }), ie2 && q(e4, { width: Qe, height: Ue, scale: ii, axis: "y" })) : "axes" === i3 ? W(e4, { xScale: ei, yScale: ii, width: Qe, height: Ue, top: oe2, right: le2, bottom: ce2, left: se2, theme: ci }) : "cells" === i3 ? (e4.textAlign = "center", e4.textBaseline = "middle", ti.forEach(function(i4) {
          t3(e4, { cell: i4, borderWidth: K2, enableLabels: fe2, theme: ci });
        })) : "legends" === i3 && null !== oi ? Ie2.forEach(function(i4) {
          M2(e4, _({}, i4, { containerWidth: Qe, containerHeight: Ue, scale: oi, theme: ci }));
        }) : "annotations" === i3 ? J(e4, { annotations: di, theme: ci }) : "function" == typeof i3 && i3(e4, ui);
      }));
    }
  }, [Ge, Xe, Je, Ke, Qe, Ue, ai, c, ui, ti, t3, $2, ie2, oe2, le2, ce2, se2, ei, ii, ci, K2, fe2, oi, Ie2, di]);
  var si = z(), hi = si.showTooltipFromEvent, fi = si.hideTooltip, vi = (0, import_react2.useCallback)(function(e4) {
    if (null !== Ge.current) {
      var i3 = kn(Ge.current, e4), t4 = i3[0], o3 = i3[1], r3 = ti.find(function(e5) {
        return wn(e5.x + ai.left - e5.width / 2, e5.y + ai.top - e5.height / 2, e5.width, e5.height, t4, o3);
      });
      void 0 !== r3 ? (ri(r3), hi((0, import_react2.createElement)(Ve, { cell: r3 }), e4)) : (ri(null), fi());
    }
  }, [Ge, ti, ai, ri, hi, fi, Ve]), gi = (0, import_react2.useCallback)(function() {
    ri(null), fi();
  }, [ri, fi]), bi = (0, import_react2.useCallback)(function(e4) {
    null !== ni && (null == Re || Re(ni, e4));
  }, [ni, Re]);
  return (0, import_jsx_runtime2.jsx)("canvas", { ref: Rn(Ge, Ye2), width: Je * Xe, height: Ke * Xe, style: { width: Je, height: Ke }, onMouseEnter: We ? vi : void 0, onMouseMove: We ? vi : void 0, onMouseLeave: We ? gi : void 0, onClick: We ? bi : void 0, role: Be, "aria-label": qe, "aria-labelledby": Ee, "aria-describedby": Ae });
};
var Pe = (0, import_react2.forwardRef)(function(e3, i3) {
  var t3 = e3.theme, o3 = e3.isInteractive, n3 = void 0 === o3 ? Q.isInteractive : o3, r2 = e3.animate, a4 = void 0 === r2 ? Q.animate : r2, l2 = e3.motionConfig, d3 = void 0 === l2 ? Q.motionConfig : l2, c = e3.renderWrapper, u = J2(e3, Ce);
  return (0, import_jsx_runtime2.jsx)(Fr, { isInteractive: n3, animate: a4, motionConfig: d3, theme: t3, renderWrapper: c, children: (0, import_jsx_runtime2.jsx)(we, _({ isInteractive: n3 }, u, { forwardedRef: i3 })) });
});
var Oe = ["defaultWidth", "defaultHeight", "onResize", "debounceResize"];
var Ie = (0, import_react2.forwardRef)(function(e3, i3) {
  var t3 = e3.defaultWidth, o3 = e3.defaultHeight, n3 = e3.onResize, r2 = e3.debounceResize, a4 = J2(e3, Oe);
  return (0, import_jsx_runtime2.jsx)($r, { defaultWidth: t3, defaultHeight: o3, onResize: n3, debounceResize: r2, children: function(e4) {
    var t4 = e4.width, o4 = e4.height;
    return (0, import_jsx_runtime2.jsx)(Pe, _({ width: t4, height: o4 }, a4, { ref: i3 }));
  } });
});
export {
  be as HeatMap,
  Pe as HeatMapCanvas,
  pe as ResponsiveHeatMap,
  Ie as ResponsiveHeatMapCanvas,
  Q as canvasDefaultProps,
  K as commonDefaultProps,
  Z as computeCells,
  U as computeLayout,
  $ as computeSizeScale,
  ie as getCellAnnotationDimensions,
  ee as getCellAnnotationPosition,
  N as svgDefaultProps,
  ae as useCellAnnotations,
  te as useComputeCells,
  re as useHeatMap
};
//# sourceMappingURL=@nivo_heatmap.js.map
