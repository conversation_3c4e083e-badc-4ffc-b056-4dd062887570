import {
  require_baseUniq,
  require_sortBy,
  require_uniqBy
} from "./chunk-2BRWKHWI.js";
import {
  AnimatedObject,
  Dr,
  FluidValue,
  M,
  R,
  a,
  addFluidObserver,
  b,
  callFluidObservers,
  colors2,
  createHost,
  createStringInterpolator2,
  d,
  each,
  eachProp,
  getFluidValue,
  globals_exports,
  gt,
  hasFluidValue,
  is,
  removeFluidObserver,
  toArray,
  useSpring,
  useTransition,
  ut,
  w
} from "./chunk-626YKPVW.js";
import {
  band,
  format,
  friday,
  hour_default,
  linear,
  log,
  millisecond_default,
  minute_default,
  monday,
  month_default,
  newInterval,
  number_default,
  point,
  require_baseGetTag,
  require_baseUnary,
  require_isObjectLike,
  require_last,
  require_nodeUtil,
  round_default,
  saturday,
  second_default,
  sunday,
  symlog,
  thursday,
  time,
  timeFormat,
  timeParse,
  tuesday,
  utcFriday,
  utcHour_default,
  utcMinute_default,
  utcMonday,
  utcMonth_default,
  utcParse,
  utcSaturday,
  utcSunday,
  utcThursday,
  utcTime,
  utcTuesday,
  utcWednesday,
  utcYear_default,
  wednesday,
  year_default
} from "./chunk-ZERN2KSE.js";
import {
  require_react_dom
} from "./chunk-T2SWDQEL.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __commonJS,
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/lodash/uniq.js
var require_uniq = __commonJS({
  "node_modules/lodash/uniq.js"(exports, module) {
    var baseUniq = require_baseUniq();
    function uniq(array) {
      return array && array.length ? baseUniq(array) : [];
    }
    module.exports = uniq;
  }
});

// node_modules/lodash/_baseIsDate.js
var require_baseIsDate = __commonJS({
  "node_modules/lodash/_baseIsDate.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var dateTag = "[object Date]";
    function baseIsDate(value) {
      return isObjectLike(value) && baseGetTag(value) == dateTag;
    }
    module.exports = baseIsDate;
  }
});

// node_modules/lodash/isDate.js
var require_isDate = __commonJS({
  "node_modules/lodash/isDate.js"(exports, module) {
    var baseIsDate = require_baseIsDate();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsDate = nodeUtil && nodeUtil.isDate;
    var isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;
    module.exports = isDate;
  }
});

// node_modules/@nivo/scales/dist/nivo-scales.mjs
var import_uniq = __toESM(require_uniq(), 1);
var import_uniqBy = __toESM(require_uniqBy(), 1);
var import_sortBy = __toESM(require_sortBy(), 1);
var import_last = __toESM(require_last(), 1);
var import_isDate = __toESM(require_isDate(), 1);
function J() {
  return J = Object.assign ? Object.assign.bind() : function(n3) {
    for (var t3 = 1; t3 < arguments.length; t3++) {
      var e3 = arguments[t3];
      for (var r3 in e3) ({}).hasOwnProperty.call(e3, r3) && (n3[r3] = e3[r3]);
    }
    return n3;
  }, J.apply(null, arguments);
}
var L = [function(n3) {
  return n3.setMilliseconds(0);
}, function(n3) {
  return n3.setSeconds(0);
}, function(n3) {
  return n3.setMinutes(0);
}, function(n3) {
  return n3.setHours(0);
}, function(n3) {
  return n3.setDate(1);
}, function(n3) {
  return n3.setMonth(0);
}];
var Q = { millisecond: [], second: L.slice(0, 1), minute: L.slice(0, 2), hour: L.slice(0, 3), day: L.slice(0, 4), month: L.slice(0, 5), year: L.slice(0, 6) };
var W = function(n3) {
  return function(t3) {
    return Q[n3].forEach(function(n4) {
      n4(t3);
    }), t3;
  };
};
var X = function(n3) {
  var t3 = n3.format, e3 = void 0 === t3 ? "native" : t3, r3 = n3.precision, i3 = void 0 === r3 ? "millisecond" : r3, u = n3.useUTC, c = void 0 === u || u, s = W(i3);
  return function(n4) {
    if (void 0 === n4) return n4;
    if ("native" === e3 || n4 instanceof Date) return s(n4);
    var t4 = c ? utcParse(e3) : timeParse(e3);
    return s(t4(n4));
  };
};
var Y = { type: "linear", min: 0, max: "auto", stacked: false, reverse: false, clamp: false, nice: true, round: false };
var Z = function(n3, t3, e3, r3) {
  var i3, a2, o, c, s = n3.min, d2 = void 0 === s ? Y.min : s, m = n3.max, f = void 0 === m ? Y.max : m, l = n3.stacked, y = void 0 === l ? Y.stacked : l, h = n3.reverse, g = void 0 === h ? Y.reverse : h, x = n3.clamp, k = void 0 === x ? Y.clamp : x, T2 = n3.nice, b3 = void 0 === T2 ? Y.nice : T2, M2 = n3.round, w3 = void 0 === M2 ? Y.round : M2;
  "auto" === d2 ? i3 = true === y ? null != (a2 = t3.minStacked) ? a2 : 0 : t3.min : i3 = d2;
  "auto" === f ? o = true === y ? null != (c = t3.maxStacked) ? c : 0 : t3.max : o = f;
  var E2 = linear().range("x" === r3 ? [0, e3] : [e3, 0]).interpolate(w3 ? round_default : number_default).domain(g ? [o, i3] : [i3, o]).clamp(k);
  return true === b3 ? E2.nice() : "number" == typeof b3 && E2.nice(b3), _(E2, y);
};
var _ = function(n3, t3) {
  void 0 === t3 && (t3 = false);
  var e3 = n3;
  return e3.type = "linear", e3.stacked = t3, e3;
};
var nn = function(n3, t3, e3) {
  var r3 = point().range([0, e3]).domain(t3.all);
  return r3.type = "point", r3;
};
var en = { type: "band", round: false };
var rn = function(n3, t3, e3, r3) {
  var i3 = n3.round, a2 = void 0 === i3 ? en.round : i3, o = band().range("x" === r3 ? [0, e3] : [e3, 0]).domain(t3.all).round(a2);
  return an(o);
};
var an = function(n3) {
  var t3 = n3;
  return t3.type = "band", t3;
};
var on = { type: "time", format: "native", precision: "millisecond", min: "auto", max: "auto", useUTC: true, nice: false };
var un = function(n3, t3, e3) {
  var r3, i3, a2 = n3.format, o = void 0 === a2 ? on.format : a2, u = n3.precision, c = void 0 === u ? on.precision : u, s = n3.min, f = void 0 === s ? on.min : s, l = n3.max, v = void 0 === l ? on.max : l, p = n3.useUTC, y = void 0 === p ? on.useUTC : p, h = n3.nice, g = void 0 === h ? on.nice : h, x = X({ format: o, precision: c, useUTC: y });
  r3 = "auto" === f ? x(t3.min) : "native" !== o ? x(f) : f, i3 = "auto" === v ? x(t3.max) : "native" !== o ? x(v) : v;
  var k = y ? utcTime() : time();
  k.range([0, e3]), r3 && i3 && k.domain([r3, i3]), true === g ? k.nice() : "object" != typeof g && "number" != typeof g || k.nice(g);
  var T2 = k;
  return T2.type = "time", T2.useUTC = y, T2;
};
var cn = { type: "log", base: 10, min: "auto", max: "auto", round: false, reverse: false, nice: true };
var sn = function(n3, t3, e3, r3) {
  var i3, a2 = n3.base, o = void 0 === a2 ? cn.base : a2, u = n3.min, c = void 0 === u ? cn.min : u, s = n3.max, d2 = void 0 === s ? cn.max : s, m = n3.round, l = void 0 === m ? cn.round : m, v = n3.reverse, p = void 0 === v ? cn.reverse : v, y = n3.nice, h = void 0 === y ? cn.nice : y;
  if (t3.all.some(function(n4) {
    return 0 === n4;
  })) throw new Error("a log scale domain must not include or cross zero");
  var g, x, k = false;
  if (t3.all.filter(function(n4) {
    return null != n4;
  }).forEach(function(n4) {
    k || (void 0 === i3 ? i3 = Math.sign(n4) : Math.sign(n4) !== i3 && (k = true));
  }), k) throw new Error("a log scale domain must be strictly-positive or strictly-negative");
  g = "auto" === c ? t3.min : c, x = "auto" === d2 ? t3.max : d2;
  var T2 = log().base(o), b3 = "x" === r3 ? [0, e3] : [e3, 0];
  true === l ? T2.rangeRound(b3) : T2.range(b3), true === p ? T2.domain([x, g]) : T2.domain([g, x]), true === h ? T2.nice() : "number" == typeof h && T2.nice(h);
  var M2 = T2;
  return M2.type = "log", M2;
};
var dn = { type: "symlog", constant: 1, min: "auto", max: "auto", round: false, reverse: false, nice: true };
var mn = function(n3, t3, e3, r3) {
  var i3, a2, o = n3.constant, u = void 0 === o ? dn.constant : o, c = n3.min, s = void 0 === c ? dn.min : c, d2 = n3.max, m = void 0 === d2 ? dn.max : d2, f = n3.round, v = void 0 === f ? dn.round : f, p = n3.reverse, y = void 0 === p ? dn.reverse : p, h = n3.nice, g = void 0 === h ? dn.nice : h;
  i3 = "auto" === s ? t3.min : s, a2 = "auto" === m ? t3.max : m;
  var x = symlog().constant(u), k = "x" === r3 ? [0, e3] : [e3, 0];
  true === v ? x.rangeRound(k) : x.range(k), true === y ? x.domain([a2, i3]) : x.domain([i3, a2]), true === g ? x.nice() : "number" == typeof g && x.nice(g);
  var T2 = x;
  return T2.type = "symlog", T2;
};
var fn = function(n3) {
  return "x" === n3 ? "y" : "x";
};
var ln = function(n3, t3) {
  return n3 === t3;
};
var vn = function(n3, t3) {
  return n3.getTime() === t3.getTime();
};
function pn(n3, t3, e3, r3) {
  switch (n3.type) {
    case "linear":
      return Z(n3, t3, e3, r3);
    case "point":
      return nn(0, t3, e3);
    case "band":
      return rn(n3, t3, e3, r3);
    case "time":
      return un(n3, t3, e3);
    case "log":
      return sn(n3, t3, e3, r3);
    case "symlog":
      return mn(n3, t3, e3, r3);
    default:
      throw new Error("invalid scale spec");
  }
}
var yn = function(n3, t3, e3) {
  var r3;
  if ("stacked" in e3 && e3.stacked) {
    var i3 = n3.data["x" === t3 ? "xStacked" : "yStacked"];
    return null == i3 ? null : e3(i3);
  }
  return null != (r3 = e3(n3.data[t3])) ? r3 : null;
};
var hn = function(n3, t3, e3, r3, i3) {
  var a2 = n3.map(function(n4) {
    return function(n5) {
      return J({}, n5, { data: n5.data.map(function(n6) {
        return { data: J({}, n6) };
      }) });
    }(n4);
  }), o = gn(a2, t3, e3);
  "stacked" in t3 && true === t3.stacked && Tn(o, a2), "stacked" in e3 && true === e3.stacked && bn(o, a2);
  var u = pn(t3, o.x, r3, "x"), c = pn(e3, o.y, i3, "y"), s = a2.map(function(n4) {
    return J({}, n4, { data: n4.data.map(function(n5) {
      return J({}, n5, { position: { x: yn(n5, "x", u), y: yn(n5, "y", c) } });
    }) });
  });
  return J({}, o, { series: s, xScale: u, yScale: c });
};
var gn = function(n3, t3, e3) {
  return { x: xn(n3, "x", t3), y: xn(n3, "y", e3) };
};
var xn = function(i3, a2, o, u) {
  var c = void 0 === u ? {} : u, s = c.getValue, d2 = void 0 === s ? function(n3) {
    return n3.data[a2];
  } : s, m = c.setValue, f = void 0 === m ? function(n3, t3) {
    n3.data[a2] = t3;
  } : m;
  if ("linear" === o.type) i3.forEach(function(n3) {
    n3.data.forEach(function(n4) {
      var t3 = d2(n4);
      t3 && f(n4, parseFloat(String(t3)));
    });
  });
  else if ("time" === o.type && "native" !== o.format) {
    var l = X(o);
    i3.forEach(function(n3) {
      n3.data.forEach(function(n4) {
        var t3 = d2(n4);
        t3 && f(n4, l(t3));
      });
    });
  }
  var v = [];
  switch (i3.forEach(function(n3) {
    n3.data.forEach(function(n4) {
      v.push(d2(n4));
    });
  }), o.type) {
    case "linear":
      var p = (0, import_sortBy.default)((0, import_uniq.default)(v).filter(function(n3) {
        return null !== n3;
      }), function(n3) {
        return n3;
      });
      return { all: p, min: Math.min.apply(Math, p), max: Math.max.apply(Math, p) };
    case "time":
      var y = (0, import_uniqBy.default)(v, function(n3) {
        return n3.getTime();
      }).slice(0).sort(function(n3, t3) {
        return t3.getTime() - n3.getTime();
      }).reverse();
      return { all: y, min: y[0], max: (0, import_last.default)(y) };
    default:
      var h = (0, import_uniq.default)(v);
      return { all: h, min: h[0], max: (0, import_last.default)(h) };
  }
};
var kn = function(n3, t3, e3) {
  var a2 = fn(n3), o = [];
  t3[a2].all.forEach(function(t4) {
    var u = (0, import_isDate.default)(t4) ? vn : ln, c = [];
    e3.forEach(function(e4) {
      var i3 = e4.data.find(function(n4) {
        return u(n4.data[a2], t4);
      }), s = null, d2 = null;
      if (void 0 !== i3) {
        if (null !== (s = i3.data[n3])) {
          var m = (0, import_last.default)(c);
          void 0 === m ? d2 = s : null !== m && (d2 = m + s);
        }
        i3.data["x" === n3 ? "xStacked" : "yStacked"] = d2;
      }
      c.push(d2), null !== d2 && o.push(d2);
    });
  }), t3[n3].minStacked = Math.min.apply(Math, o), t3[n3].maxStacked = Math.max.apply(Math, o);
};
var Tn = function(n3, t3) {
  return kn("x", n3, t3);
};
var bn = function(n3, t3) {
  return kn("y", n3, t3);
};
var Mn = function(n3) {
  var t3 = n3.bandwidth();
  if (0 === t3) return n3;
  var e3 = t3 / 2;
  return n3.round() && (e3 = Math.round(e3)), function(t4) {
    var r3;
    return (null != (r3 = n3(t4)) ? r3 : 0) + e3;
  };
};
var wn = { millisecond: [millisecond_default, millisecond_default], second: [second_default, second_default], minute: [minute_default, utcMinute_default], hour: [hour_default, utcHour_default], day: [newInterval(function(n3) {
  return n3.setHours(0, 0, 0, 0);
}, function(n3, t3) {
  return n3.setDate(n3.getDate() + t3);
}, function(n3, t3) {
  return (t3.getTime() - n3.getTime()) / 864e5;
}, function(n3) {
  return Math.floor(n3.getTime() / 864e5);
}), newInterval(function(n3) {
  return n3.setUTCHours(0, 0, 0, 0);
}, function(n3, t3) {
  return n3.setUTCDate(n3.getUTCDate() + t3);
}, function(n3, t3) {
  return (t3.getTime() - n3.getTime()) / 864e5;
}, function(n3) {
  return Math.floor(n3.getTime() / 864e5);
})], week: [sunday, utcSunday], sunday: [sunday, utcSunday], monday: [monday, utcMonday], tuesday: [tuesday, utcTuesday], wednesday: [wednesday, utcWednesday], thursday: [thursday, utcThursday], friday: [friday, utcFriday], saturday: [saturday, utcSaturday], month: [month_default, utcMonth_default], year: [year_default, utcYear_default] };
var En = Object.keys(wn);
var Sn = new RegExp("^every\\s*(\\d+)?\\s*(" + En.join("|") + ")s?$", "i");
var Cn = function(n3, t3) {
  if (Array.isArray(t3)) return t3;
  if ("string" == typeof t3 && "useUTC" in n3) {
    var e3 = t3.match(Sn);
    if (e3) {
      var r3 = e3[1], i3 = e3[2], a2 = wn[i3][n3.useUTC ? 1 : 0];
      if ("day" === i3) {
        var o, u, c = n3.domain(), s = c[0], d2 = c[1], m = new Date(d2);
        return m.setDate(m.getDate() + 1), null != (o = null == (u = a2.every(Number(null != r3 ? r3 : 1))) ? void 0 : u.range(s, m)) ? o : [];
      }
      if (void 0 === r3) return n3.ticks(a2);
      var f = a2.every(Number(r3));
      if (f) return n3.ticks(f);
    }
    throw new Error("Invalid tickValues: " + t3);
  }
  if ("ticks" in n3) {
    if (void 0 === t3) return n3.ticks();
    if ("number" == typeof (l = t3) && isFinite(l) && Math.floor(l) === l) return n3.ticks(t3);
  }
  var l;
  return n3.domain();
};

// node_modules/@nivo/axes/dist/nivo-axes.mjs
var t2 = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);

// node_modules/@nivo/axes/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs
var import_react_dom = __toESM(require_react_dom(), 1);
var isCustomPropRE = /^--/;
function dangerousStyleValue(name, value) {
  if (value == null || typeof value === "boolean" || value === "") return "";
  if (typeof value === "number" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))
    return value + "px";
  return ("" + value).trim();
}
var attributeCache = {};
function applyAnimatedValues(instance, props) {
  if (!instance.nodeType || !instance.setAttribute) {
    return false;
  }
  const isFilterElement = instance.nodeName === "filter" || instance.parentNode && instance.parentNode.nodeName === "filter";
  const {
    className,
    style,
    children,
    scrollTop,
    scrollLeft,
    viewBox,
    ...attributes
  } = props;
  const values = Object.values(attributes);
  const names = Object.keys(attributes).map(
    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(
      /([A-Z])/g,
      // Attributes are written in dash case
      (n3) => "-" + n3.toLowerCase()
    ))
  );
  if (children !== void 0) {
    instance.textContent = children;
  }
  for (const name in style) {
    if (style.hasOwnProperty(name)) {
      const value = dangerousStyleValue(name, style[name]);
      if (isCustomPropRE.test(name)) {
        instance.style.setProperty(name, value);
      } else {
        instance.style[name] = value;
      }
    }
  }
  names.forEach((name, i3) => {
    instance.setAttribute(name, values[i3]);
  });
  if (className !== void 0) {
    instance.className = className;
  }
  if (scrollTop !== void 0) {
    instance.scrollTop = scrollTop;
  }
  if (scrollLeft !== void 0) {
    instance.scrollLeft = scrollLeft;
  }
  if (viewBox !== void 0) {
    instance.setAttribute("viewBox", viewBox);
  }
}
var isUnitlessNumber = {
  animationIterationCount: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);
var prefixes = ["Webkit", "Ms", "Moz", "O"];
isUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {
  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);
  return acc;
}, isUnitlessNumber);
var domTransforms = /^(matrix|translate|scale|rotate|skew)/;
var pxTransforms = /^(translate)/;
var degTransforms = /^(rotate|skew)/;
var addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;
var isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;
var AnimatedStyle = class extends AnimatedObject {
  constructor({ x, y, z: z2, ...style }) {
    const inputs = [];
    const transforms = [];
    if (x || y || z2) {
      inputs.push([x || 0, y || 0, z2 || 0]);
      transforms.push((xyz) => [
        `translate3d(${xyz.map((v) => addUnit(v, "px")).join(",")})`,
        // prettier-ignore
        isValueIdentity(xyz, 0)
      ]);
    }
    eachProp(style, (value, key) => {
      if (key === "transform") {
        inputs.push([value || ""]);
        transforms.push((transform) => [transform, transform === ""]);
      } else if (domTransforms.test(key)) {
        delete style[key];
        if (is.und(value)) return;
        const unit = pxTransforms.test(key) ? "px" : degTransforms.test(key) ? "deg" : "";
        inputs.push(toArray(value));
        transforms.push(
          key === "rotate3d" ? ([x2, y2, z22, deg]) => [
            `rotate3d(${x2},${y2},${z22},${addUnit(deg, unit)})`,
            isValueIdentity(deg, 0)
          ] : (input) => [
            `${key}(${input.map((v) => addUnit(v, unit)).join(",")})`,
            isValueIdentity(input, key.startsWith("scale") ? 1 : 0)
          ]
        );
      }
    });
    if (inputs.length) {
      style.transform = new FluidTransform(inputs, transforms);
    }
    super(style);
  }
};
var FluidTransform = class extends FluidValue {
  constructor(inputs, transforms) {
    super();
    this.inputs = inputs;
    this.transforms = transforms;
    this._value = null;
  }
  get() {
    return this._value || (this._value = this._get());
  }
  _get() {
    let transform = "";
    let identity = true;
    each(this.inputs, (input, i3) => {
      const arg1 = getFluidValue(input[0]);
      const [t3, id] = this.transforms[i3](
        is.arr(arg1) ? arg1 : input.map(getFluidValue)
      );
      transform += " " + t3;
      identity = identity && id;
    });
    return identity ? "none" : transform;
  }
  // Start observing our inputs once we have an observer.
  observerAdded(count) {
    if (count == 1)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && addFluidObserver(value, this)
        )
      );
  }
  // Stop observing our inputs once we have no observers.
  observerRemoved(count) {
    if (count == 0)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && removeFluidObserver(value, this)
        )
      );
  }
  eventObserved(event) {
    if (event.type == "change") {
      this._value = null;
    }
    callFluidObservers(this, event);
  }
};
var primitives = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "big",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rp",
  "rt",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr",
  // SVG
  "circle",
  "clipPath",
  "defs",
  "ellipse",
  "foreignObject",
  "g",
  "image",
  "line",
  "linearGradient",
  "mask",
  "path",
  "pattern",
  "polygon",
  "polyline",
  "radialGradient",
  "rect",
  "stop",
  "svg",
  "text",
  "tspan"
];
globals_exports.assign({
  batchedUpdates: import_react_dom.unstable_batchedUpdates,
  createStringInterpolator: createStringInterpolator2,
  colors: colors2
});
var host = createHost(primitives, {
  applyAnimatedValues,
  createAnimatedStyle: (style) => new AnimatedStyle(style),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props
});
var animated = host.animated;

// node_modules/@nivo/axes/dist/nivo-axes.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function A() {
  return A = Object.assign ? Object.assign.bind() : function(t3) {
    for (var e3 = 1; e3 < arguments.length; e3++) {
      var i3 = arguments[e3];
      for (var n3 in i3) ({}).hasOwnProperty.call(i3, n3) && (t3[n3] = i3[n3]);
    }
    return t3;
  }, A.apply(null, arguments);
}
var T = function(t3) {
  var e3, i3 = t3.axis, n3 = t3.scale, r3 = t3.ticksPosition, o = t3.tickValues, l = t3.tickSize, c = t3.tickPadding, s = t3.tickRotation, f = t3.truncateTickAt, d2 = t3.engine, u = void 0 === d2 ? "svg" : d2, x = Cn(n3, o), m = gt[u], y = "bandwidth" in n3 ? Mn(n3) : n3, g = { lineX: 0, lineY: 0 }, v = { textX: 0, textY: 0 }, k = "object" == typeof document && "rtl" === document.dir, b3 = m.align.center, P2 = m.baseline.center;
  "x" === i3 ? (e3 = function(t4) {
    var e4;
    return { x: null != (e4 = y(t4)) ? e4 : 0, y: 0 };
  }, g.lineY = l * ("after" === r3 ? 1 : -1), v.textY = (l + c) * ("after" === r3 ? 1 : -1), P2 = "after" === r3 ? m.baseline.top : m.baseline.bottom, 0 === s ? b3 = m.align.center : "after" === r3 && s < 0 || "before" === r3 && s > 0 ? (b3 = m.align[k ? "left" : "right"], P2 = m.baseline.center) : ("after" === r3 && s > 0 || "before" === r3 && s < 0) && (b3 = m.align[k ? "right" : "left"], P2 = m.baseline.center)) : (e3 = function(t4) {
    var e4;
    return { x: 0, y: null != (e4 = y(t4)) ? e4 : 0 };
  }, g.lineX = l * ("after" === r3 ? 1 : -1), v.textX = (l + c) * ("after" === r3 ? 1 : -1), b3 = "after" === r3 ? m.align.left : m.align.right);
  return { ticks: x.map(function(t4) {
    var i4 = "string" == typeof t4 ? function(t5) {
      var e4 = String(t5).length;
      return f && f > 0 && e4 > f ? "" + String(t5).slice(0, f).concat("...") : "" + t5;
    }(t4) : t4;
    return A({ key: t4 instanceof Date ? "" + t4.valueOf() : "" + t4, value: i4 }, e3(t4), g, v);
  }), textAlign: b3, textBaseline: P2 };
};
var w2 = function(t3, e3) {
  if (void 0 === t3 || "function" == typeof t3) return t3;
  if ("time" === e3.type) {
    var i3 = timeFormat(t3);
    return function(t4) {
      return i3(t4 instanceof Date ? t4 : new Date(t4));
    };
  }
  return format(t3);
};
var O = function(t3) {
  var e3, i3 = t3.width, n3 = t3.height, r3 = t3.scale, a2 = t3.axis, o = t3.values, l = (e3 = o, Array.isArray(e3) ? o : void 0) || Cn(r3, o), c = "bandwidth" in r3 ? Mn(r3) : r3, s = "x" === a2 ? l.map(function(t4) {
    var e4, i4;
    return { key: t4 instanceof Date ? "" + t4.valueOf() : "" + t4, x1: null != (e4 = c(t4)) ? e4 : 0, x2: null != (i4 = c(t4)) ? i4 : 0, y1: 0, y2: n3 };
  }) : l.map(function(t4) {
    var e4, n4;
    return { key: t4 instanceof Date ? "" + t4.valueOf() : "" + t4, x1: 0, x2: i3, y1: null != (e4 = c(t4)) ? e4 : 0, y2: null != (n4 = c(t4)) ? n4 : 0 };
  });
  return s;
};
var X2 = (0, import_react.memo)(function(t3) {
  var e3, n3 = t3.value, r3 = t3.format, a2 = t3.lineX, o = t3.lineY, l = t3.onClick, c = t3.textBaseline, s = t3.textAnchor, f = t3.theme, u = t3.animatedProps, x = null != (e3 = null == r3 ? void 0 : r3(n3)) ? e3 : n3, y = (0, import_react.useMemo)(function() {
    var t4 = { opacity: u.opacity };
    return l ? { style: A({}, t4, { cursor: "pointer" }), onClick: function(t5) {
      return l(t5, x);
    } } : { style: t4 };
  }, [u.opacity, l, x]);
  return (0, import_jsx_runtime.jsxs)(animated.g, A({ transform: u.transform }, y, { children: [(0, import_jsx_runtime.jsx)("line", { x1: 0, x2: a2, y1: 0, y2: o, style: f.line }), (0, import_jsx_runtime.jsx)(b, { dominantBaseline: c, textAnchor: s, transform: u.textTransform, style: f.text, children: "" + x })] }));
});
var Y2 = { tickSize: 5, tickPadding: 5, tickRotation: 0, legendPosition: "middle", legendOffset: 0 };
var B = function(e3) {
  var r3 = e3.axis, a2 = e3.scale, l = e3.x, f = void 0 === l ? 0 : l, u = e3.y, x = void 0 === u ? 0 : u, v = e3.length, k = e3.ticksPosition, h = e3.tickValues, p = e3.tickSize, O2 = void 0 === p ? Y2.tickSize : p, B2 = e3.tickPadding, z2 = void 0 === B2 ? Y2.tickPadding : B2, R3 = e3.tickRotation, V2 = void 0 === R3 ? Y2.tickRotation : R3, C2 = e3.format, D2 = e3.renderTick, j2 = void 0 === D2 ? X2 : D2, E2 = e3.truncateTickAt, W3 = e3.legend, q2 = e3.legendPosition, H = void 0 === q2 ? Y2.legendPosition : q2, I = e3.legendOffset, F = void 0 === I ? Y2.legendOffset : I, G = e3.style, J2 = e3.onClick, K = e3.ariaHidden, L2 = M(), M2 = w(L2.axis, G), N = (0, import_react.useMemo)(function() {
    return w2(C2, a2);
  }, [C2, a2]), Q2 = T({ axis: r3, scale: a2, ticksPosition: k, tickValues: h, tickSize: O2, tickPadding: z2, tickRotation: V2, truncateTickAt: E2 }), U = Q2.ticks, Z2 = Q2.textAlign, $ = Q2.textBaseline, _2 = null;
  if (void 0 !== W3) {
    var tt, et = 0, it = 0, nt = 0;
    "y" === r3 ? (nt = -90, et = F, "start" === H ? (tt = "start", it = v) : "middle" === H ? (tt = "middle", it = v / 2) : "end" === H && (tt = "end")) : (it = F, "start" === H ? tt = "start" : "middle" === H ? (tt = "middle", et = v / 2) : "end" === H && (tt = "end", et = v)), _2 = (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: (0, import_jsx_runtime.jsx)(b, { transform: "translate(" + et + ", " + it + ") rotate(" + nt + ")", textAnchor: tt, style: A({}, M2.legend.text, { dominantBaseline: "central" }), children: W3 }) });
  }
  var rt = Dr(), at = rt.animate, ot = rt.config, lt = useSpring({ transform: "translate(" + f + "," + x + ")", lineX2: "x" === r3 ? v : 0, lineY2: "x" === r3 ? 0 : v, config: ot, immediate: !at }), ct = (0, import_react.useCallback)(function(t3) {
    return { opacity: 1, transform: "translate(" + t3.x + "," + t3.y + ")", textTransform: "translate(" + t3.textX + "," + t3.textY + ") rotate(" + V2 + ")" };
  }, [V2]), st = (0, import_react.useCallback)(function(t3) {
    return { opacity: 0, transform: "translate(" + t3.x + "," + t3.y + ")", textTransform: "translate(" + t3.textX + "," + t3.textY + ") rotate(" + V2 + ")" };
  }, [V2]), ft = useTransition(U, { keys: function(t3) {
    return t3.key;
  }, initial: ct, from: st, enter: ct, update: ct, leave: { opacity: 0 }, config: ot, immediate: !at });
  return (0, import_jsx_runtime.jsxs)(animated.g, { transform: lt.transform, "aria-hidden": K, children: [ft(function(e4, i3, n3, r4) {
    return t2.createElement(j2, A({ tickIndex: r4, format: N, rotate: V2, textBaseline: $, textAnchor: Z2, truncateTickAt: E2, animatedProps: e4, theme: M2.ticks }, i3, J2 ? { onClick: J2 } : {}));
  }), (0, import_jsx_runtime.jsx)(animated.line, { style: M2.domain.line, x1: 0, x2: lt.lineX2, y1: 0, y2: lt.lineY2 }), _2] });
};
var z = (0, import_react.memo)(B);
var R2 = ["top", "right", "bottom", "left"];
var V = (0, import_react.memo)(function(t3) {
  var e3 = t3.xScale, i3 = t3.yScale, n3 = t3.width, r3 = t3.height, a2 = { top: t3.top, right: t3.right, bottom: t3.bottom, left: t3.left };
  return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: R2.map(function(t4) {
    var o = a2[t4];
    if (!o) return null;
    var l = "top" === t4 || "bottom" === t4;
    return (0, import_jsx_runtime.jsx)(z, A({}, o, { axis: l ? "x" : "y", x: "right" === t4 ? n3 : 0, y: "bottom" === t4 ? r3 : 0, scale: l ? e3 : i3, length: l ? n3 : r3, ticksPosition: "top" === t4 || "left" === t4 ? "before" : "after", truncateTickAt: o.truncateTickAt }), t4);
  }) });
});
var C = (0, import_react.memo)(function(t3) {
  var e3 = t3.animatedProps, i3 = M();
  return (0, import_jsx_runtime.jsx)(animated.line, A({}, e3, i3.grid.line));
});
var D = (0, import_react.memo)(function(t3) {
  var e3 = t3.lines, i3 = Dr(), n3 = i3.animate, a2 = i3.config, l = useTransition(e3, { keys: function(t4) {
    return t4.key;
  }, initial: function(t4) {
    return { opacity: 1, x1: t4.x1, x2: t4.x2, y1: t4.y1, y2: t4.y2 };
  }, from: function(t4) {
    return { opacity: 0, x1: t4.x1, x2: t4.x2, y1: t4.y1, y2: t4.y2 };
  }, enter: function(t4) {
    return { opacity: 1, x1: t4.x1, x2: t4.x2, y1: t4.y1, y2: t4.y2 };
  }, update: function(t4) {
    return { opacity: 1, x1: t4.x1, x2: t4.x2, y1: t4.y1, y2: t4.y2 };
  }, leave: { opacity: 0 }, config: a2, immediate: !n3 });
  return (0, import_jsx_runtime.jsx)("g", { children: l(function(t4, e4) {
    return (0, import_react.createElement)(C, A({}, e4, { key: e4.key, animatedProps: t4 }));
  }) });
});
var j = (0, import_react.memo)(function(t3) {
  var e3 = t3.width, n3 = t3.height, r3 = t3.xScale, a2 = t3.yScale, o = t3.xValues, l = t3.yValues, c = (0, import_react.useMemo)(function() {
    return !!r3 && O({ width: e3, height: n3, scale: r3, axis: "x", values: o });
  }, [r3, o, e3, n3]), s = (0, import_react.useMemo)(function() {
    return !!a2 && O({ width: e3, height: n3, scale: a2, axis: "y", values: l });
  }, [n3, e3, a2, l]);
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [c && (0, import_jsx_runtime.jsx)(D, { lines: c }), s && (0, import_jsx_runtime.jsx)(D, { lines: s })] });
});
var E = function(t3, e3) {
  var i3, n3, r3 = e3.axis, a2 = e3.scale, o = e3.x, c = void 0 === o ? 0 : o, s = e3.y, d2 = void 0 === s ? 0 : s, m = e3.length, y = e3.ticksPosition, g = e3.tickValues, v = e3.tickSize, k = void 0 === v ? Y2.tickSize : v, h = e3.tickPadding, p = void 0 === h ? Y2.tickPadding : h, b3 = e3.tickRotation, P2 = void 0 === b3 ? Y2.tickRotation : b3, S2 = e3.format, A2 = e3.legend, w3 = e3.legendPosition, O2 = void 0 === w3 ? Y2.legendPosition : w3, X3 = e3.legendOffset, B2 = void 0 === X3 ? Y2.legendOffset : X3, z2 = e3.theme, R3 = e3.style, V2 = T({ axis: r3, scale: a2, ticksPosition: y, tickValues: g, tickSize: k, tickPadding: p, tickRotation: P2, engine: "canvas" }), C2 = V2.ticks, D2 = V2.textAlign, j2 = V2.textBaseline;
  t3.save(), t3.translate(c, d2);
  var E2 = R(z2.axis, R3);
  t3.textAlign = D2, t3.textBaseline = j2, a(t3, E2.ticks.text);
  var W3 = null != (i3 = E2.domain.line.strokeWidth) ? i3 : 0;
  "string" != typeof W3 && W3 > 0 && (t3.lineWidth = W3, t3.lineCap = "square", E2.domain.line.stroke && (t3.strokeStyle = E2.domain.line.stroke), t3.beginPath(), t3.moveTo(0, 0), t3.lineTo("x" === r3 ? m : 0, "x" === r3 ? 0 : m), t3.stroke());
  var q2 = "function" == typeof S2 ? S2 : function(t4) {
    return "" + t4;
  }, H = null != (n3 = E2.ticks.line.strokeWidth) ? n3 : 0, I = "string" != typeof H && H > 0;
  if (C2.forEach(function(e4) {
    I && (t3.lineWidth = H, t3.lineCap = "square", E2.ticks.line.stroke && (t3.strokeStyle = E2.ticks.line.stroke), t3.beginPath(), t3.moveTo(e4.x, e4.y), t3.lineTo(e4.x + e4.lineX, e4.y + e4.lineY), t3.stroke());
    var i4 = q2(e4.value);
    t3.save(), t3.translate(e4.x + e4.textX, e4.y + e4.textY), t3.rotate(ut(P2)), d(t3, E2.ticks.text, "" + i4), t3.fillText("" + i4, 0, 0), t3.restore();
  }), void 0 !== A2) {
    var F = 0, G = 0, J2 = 0, K = "center";
    "y" === r3 ? (J2 = -90, F = B2, "start" === O2 ? (K = "start", G = m) : "middle" === O2 ? (K = "center", G = m / 2) : "end" === O2 && (K = "end")) : (G = B2, "start" === O2 ? K = "start" : "middle" === O2 ? (K = "center", F = m / 2) : "end" === O2 && (K = "end", F = m)), t3.translate(F, G), t3.rotate(ut(J2)), a(t3, E2.legend.text), E2.legend.text.fill && (t3.fillStyle = E2.legend.text.fill), t3.textAlign = K, t3.textBaseline = "middle", d(t3, E2.legend.text, A2);
  }
  t3.restore();
};
var W2 = function(t3, e3) {
  var i3 = e3.xScale, n3 = e3.yScale, r3 = e3.width, a2 = e3.height, o = e3.top, l = e3.right, c = e3.bottom, s = e3.left, f = e3.theme, d2 = { top: o, right: l, bottom: c, left: s };
  R2.forEach(function(e4) {
    var o2 = d2[e4];
    if (!o2) return null;
    var l2 = "top" === e4 || "bottom" === e4, c2 = "top" === e4 || "left" === e4 ? "before" : "after", s2 = l2 ? i3 : n3, u = w2(o2.format, s2);
    E(t3, A({}, o2, { axis: l2 ? "x" : "y", x: "right" === e4 ? r3 : 0, y: "bottom" === e4 ? a2 : 0, scale: s2, format: u, length: l2 ? r3 : a2, ticksPosition: c2, theme: f }));
  });
};
var q = function(t3, e3) {
  var i3 = e3.width, n3 = e3.height, r3 = e3.scale, a2 = e3.axis, o = e3.values;
  O({ width: i3, height: n3, scale: r3, axis: a2, values: o }).forEach(function(e4) {
    t3.beginPath(), t3.moveTo(e4.x1, e4.y1), t3.lineTo(e4.x2, e4.y2), t3.stroke();
  });
};

export {
  an,
  hn,
  Y2 as Y,
  V,
  j,
  W2 as W,
  q
};
//# sourceMappingURL=chunk-XMDOMWJ3.js.map
