import {
  $r,
  $t,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Dr,
  <PERSON>,
  E2,
  <PERSON>luidVal<PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Mn,
  <PERSON>t,
  <PERSON><PERSON>,
  <PERSON>,
  addFluidObserver,
  b,
  bn,
  callFluidObservers,
  cn,
  colors2,
  createHost,
  createStringInterpolator2,
  ct,
  each,
  eachProp,
  ft,
  getFluidValue,
  globals_exports,
  hasFluidValue,
  hn,
  hr,
  is,
  removeFluidObserver,
  to,
  toArray,
  useSpring,
  useSprings,
  ut,
  w2 as w,
  z
} from "./chunk-626YKPVW.js";
import {
  arc_default,
  lineRadial_default,
  linear,
  linearClosed_default
} from "./chunk-ZERN2KSE.js";
import {
  require_react_dom
} from "./chunk-T2SWDQEL.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@nivo/radar/dist/nivo-radar.mjs
var import_react = __toESM(require_react(), 1);

// node_modules/@nivo/radar/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs
var import_react_dom = __toESM(require_react_dom(), 1);
var isCustomPropRE = /^--/;
function dangerousStyleValue(name, value) {
  if (value == null || typeof value === "boolean" || value === "") return "";
  if (typeof value === "number" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))
    return value + "px";
  return ("" + value).trim();
}
var attributeCache = {};
function applyAnimatedValues(instance, props) {
  if (!instance.nodeType || !instance.setAttribute) {
    return false;
  }
  const isFilterElement = instance.nodeName === "filter" || instance.parentNode && instance.parentNode.nodeName === "filter";
  const {
    className,
    style,
    children,
    scrollTop,
    scrollLeft,
    viewBox,
    ...attributes
  } = props;
  const values = Object.values(attributes);
  const names = Object.keys(attributes).map(
    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(
      /([A-Z])/g,
      // Attributes are written in dash case
      (n2) => "-" + n2.toLowerCase()
    ))
  );
  if (children !== void 0) {
    instance.textContent = children;
  }
  for (const name in style) {
    if (style.hasOwnProperty(name)) {
      const value = dangerousStyleValue(name, style[name]);
      if (isCustomPropRE.test(name)) {
        instance.style.setProperty(name, value);
      } else {
        instance.style[name] = value;
      }
    }
  }
  names.forEach((name, i2) => {
    instance.setAttribute(name, values[i2]);
  });
  if (className !== void 0) {
    instance.className = className;
  }
  if (scrollTop !== void 0) {
    instance.scrollTop = scrollTop;
  }
  if (scrollLeft !== void 0) {
    instance.scrollLeft = scrollLeft;
  }
  if (viewBox !== void 0) {
    instance.setAttribute("viewBox", viewBox);
  }
}
var isUnitlessNumber = {
  animationIterationCount: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);
var prefixes = ["Webkit", "Ms", "Moz", "O"];
isUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {
  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);
  return acc;
}, isUnitlessNumber);
var domTransforms = /^(matrix|translate|scale|rotate|skew)/;
var pxTransforms = /^(translate)/;
var degTransforms = /^(rotate|skew)/;
var addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;
var isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;
var AnimatedStyle = class extends AnimatedObject {
  constructor({ x, y, z: z3, ...style }) {
    const inputs = [];
    const transforms = [];
    if (x || y || z3) {
      inputs.push([x || 0, y || 0, z3 || 0]);
      transforms.push((xyz) => [
        `translate3d(${xyz.map((v) => addUnit(v, "px")).join(",")})`,
        // prettier-ignore
        isValueIdentity(xyz, 0)
      ]);
    }
    eachProp(style, (value, key) => {
      if (key === "transform") {
        inputs.push([value || ""]);
        transforms.push((transform) => [transform, transform === ""]);
      } else if (domTransforms.test(key)) {
        delete style[key];
        if (is.und(value)) return;
        const unit = pxTransforms.test(key) ? "px" : degTransforms.test(key) ? "deg" : "";
        inputs.push(toArray(value));
        transforms.push(
          key === "rotate3d" ? ([x2, y2, z22, deg]) => [
            `rotate3d(${x2},${y2},${z22},${addUnit(deg, unit)})`,
            isValueIdentity(deg, 0)
          ] : (input) => [
            `${key}(${input.map((v) => addUnit(v, unit)).join(",")})`,
            isValueIdentity(input, key.startsWith("scale") ? 1 : 0)
          ]
        );
      }
    });
    if (inputs.length) {
      style.transform = new FluidTransform(inputs, transforms);
    }
    super(style);
  }
};
var FluidTransform = class extends FluidValue {
  constructor(inputs, transforms) {
    super();
    this.inputs = inputs;
    this.transforms = transforms;
    this._value = null;
  }
  get() {
    return this._value || (this._value = this._get());
  }
  _get() {
    let transform = "";
    let identity = true;
    each(this.inputs, (input, i2) => {
      const arg1 = getFluidValue(input[0]);
      const [t2, id] = this.transforms[i2](
        is.arr(arg1) ? arg1 : input.map(getFluidValue)
      );
      transform += " " + t2;
      identity = identity && id;
    });
    return identity ? "none" : transform;
  }
  // Start observing our inputs once we have an observer.
  observerAdded(count) {
    if (count == 1)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && addFluidObserver(value, this)
        )
      );
  }
  // Stop observing our inputs once we have no observers.
  observerRemoved(count) {
    if (count == 0)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && removeFluidObserver(value, this)
        )
      );
  }
  eventObserved(event) {
    if (event.type == "change") {
      this._value = null;
    }
    callFluidObservers(this, event);
  }
};
var primitives = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "big",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rp",
  "rt",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr",
  // SVG
  "circle",
  "clipPath",
  "defs",
  "ellipse",
  "foreignObject",
  "g",
  "image",
  "line",
  "linearGradient",
  "mask",
  "path",
  "pattern",
  "polygon",
  "polyline",
  "radialGradient",
  "rect",
  "stop",
  "svg",
  "text",
  "tspan"
];
globals_exports.assign({
  batchedUpdates: import_react_dom.unstable_batchedUpdates,
  createStringInterpolator: createStringInterpolator2,
  colors: colors2
});
var host = createHost(primitives, {
  applyAnimatedValues,
  createAnimatedStyle: (style) => new AnimatedStyle(style),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props
});
var animated = host.animated;

// node_modules/@nivo/radar/dist/nivo-radar.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function H() {
  return H = Object.assign ? Object.assign.bind() : function(e2) {
    for (var r2 = 1; r2 < arguments.length; r2++) {
      var t2 = arguments[r2];
      for (var n2 in t2) ({}).hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
    }
    return e2;
  }, H.apply(null, arguments);
}
function T(e2, r2) {
  if (null == e2) return {};
  var t2 = {};
  for (var n2 in e2) if ({}.hasOwnProperty.call(e2, n2)) {
    if (-1 !== r2.indexOf(n2)) continue;
    t2[n2] = e2[n2];
  }
  return t2;
}
var j = function(r2) {
  var t2, n2 = r2.data, o2 = r2.item, i2 = r2.colorByKey, a2 = r2.fillByKey, u = r2.radiusScale, c = r2.rotation, s = r2.angleStep, f = r2.curveFactory, m = r2.borderWidth, g = r2.borderColor, v = r2.fillOpacity, y = r2.blendMode, h = M(), p = Ye(g, h), b2 = (0, import_react.useMemo)(function() {
    return lineRadial_default().radius(function(e2) {
      return u(e2);
    }).angle(function(e2, r3) {
      return c + r3 * s;
    }).curve(f);
  }, [u, c, s, f]), x = Dr(), k = x.animate, S = x.config, C = It(b2(n2.map(function(e2) {
    return e2[o2];
  }))), W = useSpring({ fill: i2[o2], stroke: p({ key: o2, color: i2[o2] }), config: S, immediate: !k }), I = null != (t2 = a2[o2]) ? t2 : W.fill;
  return (0, import_jsx_runtime.jsx)(animated.path, { d: C, fill: I, fillOpacity: v, stroke: W.stroke, strokeWidth: m, style: { mixBlendMode: y } }, o2);
};
var E3 = function(e2) {
  var t2 = e2.radius, n2 = e2.angles, o2 = e2.indices, i2 = e2.label, a2 = e2.labelOffset, d = Dr(), s = d.animate, f = d.config, m = o2.map(function(e3, r2) {
    var o3, i3, l = ft(n2[r2], t2 + a2), d2 = (o3 = n2[r2], (i3 = ct(o3) + 90) <= 10 || i3 >= 350 || i3 >= 170 && i3 <= 190 ? "middle" : i3 > 180 ? "end" : "start");
    return H({ id: e3, angle: ct(n2[r2]), anchor: d2 }, l);
  }), g = useSprings(m.length, m.map(function(e3) {
    return { transform: "translate(" + e3.x + ", " + e3.y + ")", config: f, immediate: !s };
  }));
  return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: g.map(function(e3, t3) {
    var n3 = m[t3];
    return (0, import_react.createElement)(i2, { key: n3.id, id: n3.id, anchor: n3.anchor, angle: n3.angle, x: n3.x, y: n3.y, animated: e3 });
  }) });
};
var G = ["shape"];
var q = (0, import_react.memo)(function(e2) {
  var r2 = e2.radius, t2 = M(), n2 = Dr(), o2 = n2.animate, i2 = n2.config, a2 = useSpring({ radius: r2, config: i2, immediate: !o2 });
  return (0, import_jsx_runtime.jsx)(animated.circle, H({ fill: "none", r: to(a2.radius, function(e3) {
    return Math.max(e3, 0);
  }) }, t2.grid.line));
});
var J = function(r2) {
  var t2 = r2.radius, n2 = r2.rotation, o2 = r2.angleStep, i2 = r2.dataLength, a2 = M(), l = (0, import_react.useMemo)(function() {
    return lineRadial_default().angle(function(e2) {
      return n2 + e2 * o2;
    }).radius(t2).curve(linearClosed_default);
  }, [n2, o2, t2]), u = Array.from({ length: i2 }, function(e2, r3) {
    return r3;
  }), c = It(l(u));
  return (0, import_jsx_runtime.jsx)(animated.path, H({ fill: "none", d: c }, a2.grid.line));
};
var N = function(e2) {
  var r2 = e2.shape, t2 = T(e2, G);
  return "circular" === r2 ? (0, import_jsx_runtime.jsx)(q, { radius: t2.radius }) : (0, import_jsx_runtime.jsx)(J, H({}, t2));
};
var Q = function(r2) {
  var t2 = r2.indices, n2 = r2.levels, o2 = r2.shape, i2 = r2.radius, a2 = r2.rotation, l = r2.angleStep, d = r2.label, c = r2.labelOffset, s = M(), f = (0, import_react.useMemo)(function() {
    return { radii: Array.from({ length: n2 }).map(function(e2, r3) {
      return i2 / n2 * (r3 + 1);
    }).reverse(), angles: Array.from({ length: t2.length }).map(function(e2, r3) {
      return a2 + r3 * l - Math.PI / 2;
    }) };
  }, [t2, n2, i2, a2, l]), m = f.radii, g = f.angles;
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [g.map(function(e2, r3) {
    var t3 = ft(e2, i2);
    return (0, import_jsx_runtime.jsx)("line", H({ x1: 0, y1: 0, x2: t3.x, y2: t3.y }, s.grid.line), "axis." + r3);
  }), m.map(function(e2, r3) {
    return (0, import_jsx_runtime.jsx)(N, { shape: o2, radius: e2, rotation: a2, angleStep: l, dataLength: t2.length }, "level." + r3);
  }), (0, import_jsx_runtime.jsx)(E3, { radius: i2, angles: g, indices: t2, labelOffset: c, label: d })] });
};
var U = function(t2) {
  var i2 = t2.datum, a2 = t2.keys, l = t2.index, d = t2.formatValue, c = t2.colorByKey, s = t2.radius, f = t2.startAngle, m = t2.endAngle, g = t2.arcGenerator, v = t2.tooltip, y = t2.onClick, h = (0, import_react.useState)(false), p = h[0], b2 = h[1], x = M(), k = z(), B = k.showTooltipFromEvent, L = k.hideTooltip, S = (0, import_react.useCallback)(function(e2) {
    return null == y ? void 0 : y(i2, e2);
  }, [y, i2]), C = (0, import_react.useMemo)(function() {
    var e2 = a2.map(function(e3) {
      return { color: c[e3], id: e3, value: i2[e3], formattedValue: d(i2[e3], e3) };
    });
    return e2.sort(function(e3, r2) {
      return e3.value - r2.value;
    }), e2.reverse(), e2;
  }, [i2, a2, d, c]), O = (0, import_react.useCallback)(function(e2) {
    b2(true), B((0, import_react.createElement)(v, { index: l, data: C }), e2);
  }, [B, v, l, C]), W = (0, import_react.useCallback)(function() {
    b2(false), L();
  }, [L, b2]), I = (0, import_react.useMemo)(function() {
    var e2 = ft(f + 0.5 * (m - f) - Math.PI / 2, s);
    return { path: g({ startAngle: f, endAngle: m }), tipX: e2.x, tipY: e2.y };
  }, [f, m, s, g]), K = I.path, V = I.tipX, D = I.tipY;
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [p && (0, import_jsx_runtime.jsx)("line", { x1: 0, y1: 0, x2: V, y2: D, style: x.crosshair.line }), (0, import_jsx_runtime.jsx)("path", { d: K, fill: "#F00", fillOpacity: 0, onMouseEnter: O, onMouseMove: O, onMouseLeave: W, onClick: S })] });
};
var Z = function(e2) {
  var r2 = e2.data, t2 = e2.keys, n2 = e2.getIndex, o2 = e2.formatValue, i2 = e2.colorByKey, a2 = e2.radius, l = e2.rotation, d = e2.angleStep, u = e2.tooltip, c = e2.onClick, s = arc_default().outerRadius(a2).innerRadius(0), f = l - 0.5 * d;
  return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: r2.map(function(e3) {
    var r3 = n2(e3), l2 = f;
    return f += d, (0, import_jsx_runtime.jsx)(U, { datum: e3, keys: t2, index: r3, formatValue: o2, colorByKey: i2, startAngle: l2, endAngle: l2 + d, radius: a2, arcGenerator: s, tooltip: u, onClick: c }, r3);
  }) });
};
var $ = function(r2) {
  var t2 = r2.data, n2 = r2.keys, o2 = r2.getIndex, i2 = r2.colorByKey, a2 = r2.radiusScale, l = r2.rotation, d = r2.angleStep, c = r2.symbol, m = r2.size, g = void 0 === m ? 6 : m, v = r2.color, y = void 0 === v ? { from: "color" } : v, h = r2.borderWidth, p = void 0 === h ? 0 : h, b2 = r2.borderColor, x = void 0 === b2 ? { from: "color" } : b2, k = r2.enableLabel, B = void 0 !== k && k, L = r2.label, S = void 0 === L ? "value" : L, C = r2.formatValue, O = r2.labelYOffset, W = M(), I = Xe(y, W), K = Xe(x, W), D = bn(S), z3 = (0, import_react.useMemo)(function() {
    return t2.reduce(function(e2, r3, t3) {
      var c2 = o2(r3);
      return n2.forEach(function(n3) {
        var o3 = r3[n3], s = { index: c2, key: n3, value: o3, formattedValue: C(o3, n3), color: i2[n3] };
        e2.push({ key: n3 + "." + c2, label: B ? D(s) : void 0, style: H({ fill: I(s), stroke: K(s) }, ft(l + d * t3 - Math.PI / 2, a2(r3[n3]))), data: s });
      }), e2;
    }, []);
  }, [t2, n2, o2, i2, B, D, C, I, K, l, d, a2]);
  return (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: z3.map(function(e2) {
    return (0, import_jsx_runtime.jsx)(Ct, { x: e2.style.x, y: e2.style.y, symbol: c, size: g, color: e2.style.fill, borderWidth: p, borderColor: e2.style.stroke, label: e2.label, labelYOffset: O, datum: e2.data }, e2.key);
  }) });
};
var _ = { layers: ["grid", "layers", "slices", "dots", "legends"], maxValue: "auto", rotation: 0, curve: "linearClosed", borderWidth: 2, borderColor: { from: "color" }, gridLevels: 5, gridShape: "circular", gridLabelOffset: 16, gridLabel: function(e2) {
  var r2 = e2.id, t2 = e2.anchor, n2 = e2.animated, o2 = M();
  return (0, import_jsx_runtime.jsx)(animated.g, { transform: n2.transform, children: (0, import_jsx_runtime.jsx)(b, { style: o2.axis.ticks.text, dominantBaseline: "central", textAnchor: t2, children: r2 }) });
}, enableDots: true, dotSize: 6, dotColor: { from: "color" }, dotBorderWidth: 0, dotBorderColor: { from: "color" }, enableDotLabel: false, dotLabel: "formattedValue", dotLabelYOffset: -12, colors: { scheme: "nivo" }, fillOpacity: 0.25, blendMode: "normal", isInteractive: true, sliceTooltip: function(r2) {
  var t2 = r2.index, n2 = r2.data, o2 = (0, import_react.useMemo)(function() {
    return n2.map(function(e2) {
      return [(0, import_jsx_runtime.jsx)(w, { color: e2.color }, e2.id), e2.id, e2.formattedValue];
    });
  }, [n2]);
  return (0, import_jsx_runtime.jsx)(E, { title: (0, import_jsx_runtime.jsx)("strong", { children: t2 }), rows: o2 });
}, legends: [], role: "img", animate: true, motionConfig: "gentle", defs: [], fill: [] };
var ee = ["data"];
var re = ["isInteractive", "animate", "motionConfig", "theme", "renderWrapper"];
var te = function(t2) {
  var n2 = t2.data, o2 = t2.keys, i2 = t2.indexBy, l = t2.layers, d = void 0 === l ? _.layers : l, u = t2.rotation, c = void 0 === u ? _.rotation : u, f = t2.maxValue, h = void 0 === f ? _.maxValue : f, x = t2.valueFormat, B = t2.curve, L = void 0 === B ? _.curve : B, S = t2.margin, C = t2.width, O = t2.height, W = t2.borderWidth, I = void 0 === W ? _.borderWidth : W, M2 = t2.borderColor, K = void 0 === M2 ? _.borderColor : M2, V = t2.gridLevels, Y2 = void 0 === V ? _.gridLevels : V, z3 = t2.gridShape, A = void 0 === z3 ? _.gridShape : z3, F = t2.gridLabel, R = void 0 === F ? _.gridLabel : F, P = t2.gridLabelOffset, E4 = void 0 === P ? _.gridLabelOffset : P, G2 = t2.enableDots, q2 = void 0 === G2 ? _.enableDots : G2, J2 = t2.dotSymbol, N2 = t2.dotSize, U2 = void 0 === N2 ? _.dotSize : N2, re2 = t2.dotColor, te2 = void 0 === re2 ? _.dotColor : re2, ne2 = t2.dotBorderWidth, oe2 = void 0 === ne2 ? _.dotBorderWidth : ne2, ie2 = t2.dotBorderColor, ae = void 0 === ie2 ? _.dotBorderColor : ie2, le = t2.enableDotLabel, de = void 0 === le ? _.enableDotLabel : le, ue = t2.dotLabel, ce = void 0 === ue ? _.dotLabel : ue, se = t2.dotLabelYOffset, fe = void 0 === se ? _.dotLabelYOffset : se, me = t2.colors, ge = void 0 === me ? _.colors : me, ve = t2.fillOpacity, ye = void 0 === ve ? _.fillOpacity : ve, he = t2.blendMode, pe = void 0 === he ? _.blendMode : he, be = t2.isInteractive, xe = void 0 === be ? _.isInteractive : be, ke = t2.sliceTooltip, Be = void 0 === ke ? _.sliceTooltip : ke, Le = t2.legends, Se = void 0 === Le ? _.legends : Le, Ce = t2.role, Oe = t2.ariaLabel, We = t2.ariaLabelledBy, Ie = t2.ariaDescribedBy, Me = t2.defs, Ke = void 0 === Me ? _.defs : Me, Ve = t2.fill, De = void 0 === Ve ? _.fill : Ve, we = t2.onClick, Ye2 = t2.forwardedRef, ze = cn(C, O, S), Ae = ze.margin, Fe = ze.innerWidth, Re = ze.innerHeight, Pe = ze.outerWidth, Xe2 = ze.outerHeight, He = function(r2) {
    var t3 = r2.data, n3 = r2.keys, o3 = r2.indexBy, i3 = r2.rotationDegrees, a2 = r2.maxValue, l2 = r2.valueFormat, d2 = r2.curve, u2 = r2.width, c2 = r2.height, f2 = r2.colors, h2 = void 0 === f2 ? _.colors : f2, p = r2.legends, b2 = r2.defs, x2 = r2.fill, k = bn(o3), B2 = (0, import_react.useMemo)(function() {
      return t3.map(k);
    }, [t3, k]), L2 = hn(l2), S2 = ut(i3), C2 = hr(h2, "key"), O2 = (0, import_react.useMemo)(function() {
      return n3.reduce(function(e2, r3, t4) {
        return e2[r3] = C2({ key: r3, index: t4 }), e2;
      }, {});
    }, [n3, C2]), W2 = (0, import_react.useMemo)(function() {
      var e2 = n3.map(function(e3) {
        return { key: e3, color: O2[e3], data: t3, fill: null };
      }), r3 = Mn(b2, e2, x2), o4 = e2.reduce(function(e3, r4) {
        var t4 = r4.key, n4 = r4.fill;
        return e3[t4] = n4, e3;
      }, {});
      return { boundDefs: r3, fillByKey: o4 };
    }, [n3, t3, b2, x2, O2]), I2 = W2.boundDefs, M3 = W2.fillByKey, K2 = (0, import_react.useMemo)(function() {
      var e2 = t3.reduce(function(e3, r4) {
        return [].concat(e3, n3.map(function(e4) {
          return r4[e4];
        }));
      }, []), r3 = "auto" !== a2 ? a2 : Math.max.apply(Math, e2), o4 = Math.min(u2, c2) / 2;
      return { radius: o4, radiusScale: linear().range([0, o4]).domain([0, r3]), centerX: u2 / 2, centerY: c2 / 2, angleStep: 2 * Math.PI / t3.length };
    }, [n3, t3, a2, u2, c2]), V2 = K2.radius, w3 = K2.radiusScale, Y3 = K2.centerX, z4 = K2.centerY, A2 = K2.angleStep, F2 = $t(d2), R2 = (0, import_react.useMemo)(function() {
      return { data: t3, keys: n3, indices: B2, colorByKey: O2, centerX: Y3, centerY: z4, radiusScale: w3, angleStep: A2 };
    }, [t3, n3, B2, O2, Y3, z4, w3, A2]), P2 = (0, import_react.useMemo)(function() {
      return n3.map(function(e2) {
        return { id: e2, label: e2, color: O2[e2] };
      });
    }, [n3, O2]), j2 = (0, import_react.useMemo)(function() {
      return p.map(function(e2) {
        var r3 = e2.data, t4 = T(e2, ee), n4 = null == r3 ? void 0 : r3.map(function(e3) {
          return H({}, P2.find(function(r4) {
            return r4.id === e3.id;
          }) || {}, e3);
        });
        return H({}, t4, { data: n4 || P2 });
      });
    }, [p, P2]);
    return { getIndex: k, indices: B2, formatValue: L2, colorByKey: O2, fillByKey: M3, boundDefs: I2, rotation: S2, radius: V2, radiusScale: w3, centerX: Y3, centerY: z4, angleStep: A2, curveFactory: F2, legendData: P2, boundLegends: j2, customLayerProps: R2 };
  }({ data: n2, keys: o2, indexBy: i2, rotationDegrees: c, maxValue: h, valueFormat: x, curve: L, width: Fe, height: Re, colors: ge, legends: Se, defs: Ke, fill: De }), Te = He.getIndex, je = He.indices, Ee = He.formatValue, Ge = He.colorByKey, qe = He.fillByKey, Je = He.boundDefs, Ne = He.rotation, Qe = He.radius, Ue = He.radiusScale, Ze = He.centerX, $e = He.centerY, _e = He.angleStep, er = He.curveFactory, rr = He.boundLegends, tr = He.customLayerProps, nr = { grid: null, layers: null, slices: null, dots: null, legends: null };
  return d.includes("grid") && (nr.grid = (0, import_jsx_runtime.jsx)("g", { transform: "translate(" + Ze + ", " + $e + ")", children: (0, import_jsx_runtime.jsx)(Q, { levels: Y2, shape: A, radius: Qe, rotation: Ne, angleStep: _e, indices: je, label: R, labelOffset: E4 }) }, "grid")), d.includes("layers") && (nr.layers = (0, import_jsx_runtime.jsx)("g", { transform: "translate(" + Ze + ", " + $e + ")", children: o2.map(function(e2) {
    return (0, import_jsx_runtime.jsx)(j, { data: n2, item: e2, colorByKey: Ge, fillByKey: qe, radiusScale: Ue, rotation: Ne, angleStep: _e, curveFactory: er, borderWidth: I, borderColor: K, fillOpacity: ye, blendMode: pe }, e2);
  }) }, "layers")), d.includes("slices") && xe && (nr.slices = (0, import_jsx_runtime.jsx)("g", { transform: "translate(" + Ze + ", " + $e + ")", children: (0, import_jsx_runtime.jsx)(Z, { data: n2, keys: o2, getIndex: Te, formatValue: Ee, colorByKey: Ge, radius: Qe, rotation: Ne, angleStep: _e, tooltip: Be, onClick: we }) }, "slices")), d.includes("dots") && q2 && (nr.dots = (0, import_jsx_runtime.jsx)("g", { transform: "translate(" + Ze + ", " + $e + ")", children: (0, import_jsx_runtime.jsx)($, { data: n2, keys: o2, getIndex: Te, radiusScale: Ue, rotation: Ne, angleStep: _e, symbol: J2, size: U2, colorByKey: Ge, color: te2, borderWidth: oe2, borderColor: ae, enableLabel: de, label: ce, formatValue: Ee, labelYOffset: fe }) }, "dots")), d.includes("legends") && (nr.legends = (0, import_jsx_runtime.jsx)(import_react.Fragment, { children: rr.map(function(e2, r2) {
    return (0, import_jsx_runtime.jsx)(E2, H({}, e2, { containerWidth: C, containerHeight: O }), r2);
  }) }, "legends")), (0, import_jsx_runtime.jsx)(Rt, { defs: Je, width: Pe, height: Xe2, margin: Ae, role: Ce, ariaLabel: Oe, ariaLabelledBy: We, ariaDescribedBy: Ie, ref: Ye2, children: d.map(function(e2, t3) {
    var n3;
    return "function" == typeof e2 ? (0, import_jsx_runtime.jsx)(import_react.Fragment, { children: (0, import_react.createElement)(e2, tr) }, t3) : null != (n3 = null == nr ? void 0 : nr[e2]) ? n3 : null;
  }) });
};
var ne = (0, import_react.forwardRef)(function(e2, r2) {
  var t2 = e2.isInteractive, n2 = void 0 === t2 ? _.isInteractive : t2, o2 = e2.animate, i2 = void 0 === o2 ? _.animate : o2, a2 = e2.motionConfig, l = void 0 === a2 ? _.motionConfig : a2, d = e2.theme, u = e2.renderWrapper, c = T(e2, re);
  return (0, import_jsx_runtime.jsx)(Fr, { animate: i2, isInteractive: n2, motionConfig: l, renderWrapper: u, theme: d, children: (0, import_jsx_runtime.jsx)(te, H({ isInteractive: n2 }, c, { forwardedRef: r2 })) });
});
var oe = ["defaultWidth", "defaultHeight", "onResize", "debounceResize"];
var ie = (0, import_react.forwardRef)(function(e2, r2) {
  var t2 = e2.defaultWidth, n2 = e2.defaultHeight, o2 = e2.onResize, i2 = e2.debounceResize, a2 = T(e2, oe);
  return (0, import_jsx_runtime.jsx)($r, { defaultWidth: t2, defaultHeight: n2, onResize: o2, debounceResize: i2, children: function(e3) {
    var t3 = e3.width, n3 = e3.height;
    return (0, import_jsx_runtime.jsx)(ne, H({}, a2, { width: t3, height: n3, ref: r2 }));
  } });
});
export {
  ne as Radar,
  $ as RadarDots,
  ie as ResponsiveRadar,
  _ as svgDefaultProps
};
//# sourceMappingURL=@nivo_radar.js.map
