{"version": 3, "sources": ["../../lodash/uniqueId.js", "../../@nivo/line/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs", "../../robust-predicates/esm/util.js", "../../robust-predicates/esm/orient2d.js", "../../robust-predicates/esm/orient3d.js", "../../robust-predicates/esm/incircle.js", "../../robust-predicates/esm/insphere.js", "../../delaunator/index.js", "../../d3-delaunay/src/path.js", "../../d3-delaunay/src/polygon.js", "../../d3-delaunay/src/voronoi.js", "../../d3-delaunay/src/delaunay.js", "../../@nivo/voronoi/src/props.ts", "../../@nivo/voronoi/src/defaults.ts", "../../@nivo/voronoi/src/computeMesh.ts", "../../@nivo/voronoi/src/hooks.ts", "../../@nivo/voronoi/src/Voronoi.tsx", "../../@nivo/voronoi/src/ResponsiveVoronoi.tsx", "../../@nivo/voronoi/src/Mesh.tsx", "../../@nivo/voronoi/src/meshCanvas.ts", "../../@nivo/line/src/PointTooltip.tsx", "../../@nivo/line/src/defaults.ts", "../../@nivo/line/src/SliceTooltip.tsx", "../../@nivo/line/src/hooks.ts", "../../@nivo/line/src/Areas.tsx", "../../@nivo/line/src/LinesItem.tsx", "../../@nivo/line/src/Lines.tsx", "../../@nivo/line/src/SlicesItem.tsx", "../../@nivo/line/src/Slices.tsx", "../../@nivo/line/src/Points.tsx", "../../@nivo/line/src/Mesh.tsx", "../../@nivo/line/src/Line.tsx", "../../@nivo/line/src/ResponsiveLine.tsx", "../../@nivo/line/src/LineCanvas.tsx", "../../@nivo/line/src/ResponsiveLineCanvas.tsx", "../../@nivo/line/src/types.ts"], "sourcesContent": ["var toString = require('./toString');\n\n/** Used to generate unique IDs. */\nvar idCounter = 0;\n\n/**\n * Generates a unique ID. If `prefix` is given, the ID is appended to it.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Util\n * @param {string} [prefix=''] The value to prefix the ID with.\n * @returns {string} Returns the unique ID.\n * @example\n *\n * _.uniqueId('contact_');\n * // => 'contact_104'\n *\n * _.uniqueId();\n * // => '105'\n */\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nmodule.exports = uniqueId;\n", "// src/index.ts\nimport { Globals } from \"@react-spring/core\";\nimport { unstable_batchedUpdates } from \"react-dom\";\nimport { createStringInterpolator, colors } from \"@react-spring/shared\";\nimport { createHost } from \"@react-spring/animated\";\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\") return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\nimport { AnimatedObject } from \"@react-spring/animated\";\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver\n} from \"@react-spring/shared\";\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    eachProp(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (is.und(value)) return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push(toArray(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0]);\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    callFluidObservers(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\nexport * from \"@react-spring/core\";\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors\n});\nvar host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\nexport {\n  animated as a,\n  animated\n};\n", "export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nexport function estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nexport function vec(n) {\n    return new Float64Array(n);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum} from './util.js';\n\nconst ccwerrboundA = (3 + 16 * epsilon) * epsilon;\nconst ccwerrboundB = (2 + 12 * epsilon) * epsilon;\nconst ccwerrboundC = (9 + 64 * epsilon) * epsilon * epsilon;\n\nconst B = vec(4);\nconst C1 = vec(8);\nconst C2 = vec(12);\nconst D = vec(16);\nconst u = vec(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = estimate(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = sum(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = sum(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = sum(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nexport function orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nexport function orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, scale} from './util.js';\n\nconst o3derrboundA = (7 + 56 * epsilon) * epsilon;\nconst o3derrboundB = (3 + 28 * epsilon) * epsilon;\nconst o3derrboundC = (26 + 288 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst at_b = vec(4);\nconst at_c = vec(4);\nconst bt_c = vec(4);\nconst bt_a = vec(4);\nconst ct_a = vec(4);\nconst ct_b = vec(4);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abt = vec(8);\nconst u = vec(4);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _16 = vec(8);\nconst _12 = vec(12);\n\nlet fin = vec(192);\nlet fin2 = vec(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = sum(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            scale(4, bc, adz, _8), _8,\n            scale(4, ca, bdz, _8b), _8b, _16), _16,\n        scale(4, ab, cdz, _8), _8, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = sum(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, scale(bctlen, bct, adz, _16), _16);\n\n    const catlen = sum(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, scale(catlen, cat, bdz, _16), _16);\n\n    const abtlen = sum(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, scale(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, scale(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, scale(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, scale(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, scale(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nexport function orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale} from './util.js';\n\nconst iccerrboundA = (10 + 96 * epsilon) * epsilon;\nconst iccerrboundB = (4 + 48 * epsilon) * epsilon;\nconst iccerrboundC = (44 + 576 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst aa = vec(4);\nconst bb = vec(4);\nconst cc = vec(4);\nconst u = vec(4);\nconst v = vec(4);\nconst axtbc = vec(8);\nconst aytbc = vec(8);\nconst bxtca = vec(8);\nconst bytca = vec(8);\nconst cxtab = vec(8);\nconst cytab = vec(8);\nconst abt = vec(8);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abtt = vec(4);\nconst bctt = vec(4);\nconst catt = vec(4);\n\nconst _8 = vec(8);\nconst _16 = vec(16);\nconst _16b = vec(16);\nconst _16c = vec(16);\nconst _32 = vec(32);\nconst _32b = vec(32);\nconst _48 = vec(48);\nconst _64 = vec(64);\n\nlet fin = vec(1152);\nlet fin2 = vec(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = sum(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            sum(\n                scale(scale(4, bc, adx, _8), _8, adx, _16), _16,\n                scale(scale(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            sum(\n                scale(scale(4, ca, bdx, _8), _8, bdx, _16), _16,\n                scale(scale(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        sum(\n            scale(scale(4, ab, cdx, _8), _8, cdx, _16), _16,\n            scale(scale(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = scale(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, sum_three(\n            scale(axtbclen, axtbc, 2 * adx, _16), _16,\n            scale(scale(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            scale(scale(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = scale(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, sum_three(\n            scale(aytbclen, aytbc, 2 * ady, _16), _16,\n            scale(scale(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            scale(scale(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = scale(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, sum_three(\n            scale(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            scale(scale(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            scale(scale(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = scale(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, sum_three(\n            scale(bytcalen, bytca, 2 * bdy, _16), _16,\n            scale(scale(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            scale(scale(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = scale(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, sum_three(\n            scale(cxtablen, cxtab, 2 * cdx, _16), _16,\n            scale(scale(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            scale(scale(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = scale(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, sum_three(\n            scale(cytablen, cytab, 2 * cdy, _16), _16,\n            scale(scale(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            scale(scale(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = sum(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = scale(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(axtbclen, axtbc, adxtail, _16), _16,\n                scale(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * adx, _16), _16,\n                scale(len2, _8, adxtail, _16b), _16b,\n                scale(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = scale(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(aytbclen, aytbc, adytail, _16), _16,\n                scale(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * ady, _16), _16,\n                scale(len2, _8, adytail, _16b), _16b,\n                scale(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = sum(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = scale(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bxtcalen, bxtca, bdxtail, _16), _16,\n                scale(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdx, _16), _16,\n                scale(len2, _8, bdxtail, _16b), _16b,\n                scale(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = scale(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bytcalen, bytca, bdytail, _16), _16,\n                scale(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdy, _16), _16,\n                scale(len2, _8, bdytail, _16b), _16b,\n                scale(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = sum(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = scale(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cxtablen, cxtab, cdxtail, _16), _16,\n                scale(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdx, _16), _16,\n                scale(len2, _8, cdxtail, _16b), _16b,\n                scale(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = scale(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cytablen, cytab, cdytail, _16), _16,\n                scale(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdy, _16), _16,\n                scale(len2, _8, cdytail, _16b), _16b,\n                scale(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nexport function incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale, negate} from './util.js';\n\nconst isperrboundA = (16 + 224 * epsilon) * epsilon;\nconst isperrboundB = (5 + 72 * epsilon) * epsilon;\nconst isperrboundC = (71 + 1408 * epsilon) * epsilon * epsilon;\n\nconst ab = vec(4);\nconst bc = vec(4);\nconst cd = vec(4);\nconst de = vec(4);\nconst ea = vec(4);\nconst ac = vec(4);\nconst bd = vec(4);\nconst ce = vec(4);\nconst da = vec(4);\nconst eb = vec(4);\n\nconst abc = vec(24);\nconst bcd = vec(24);\nconst cde = vec(24);\nconst dea = vec(24);\nconst eab = vec(24);\nconst abd = vec(24);\nconst bce = vec(24);\nconst cda = vec(24);\nconst deb = vec(24);\nconst eac = vec(24);\n\nconst adet = vec(1152);\nconst bdet = vec(1152);\nconst cdet = vec(1152);\nconst ddet = vec(1152);\nconst edet = vec(1152);\nconst abdet = vec(2304);\nconst cddet = vec(2304);\nconst cdedet = vec(3456);\nconst deter = vec(5760);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _8c = vec(8);\nconst _16 = vec(16);\nconst _24 = vec(24);\nconst _48 = vec(48);\nconst _48b = vec(48);\nconst _96 = vec(96);\nconst _192 = vec(192);\nconst _384x = vec(384);\nconst _384y = vec(384);\nconst _384z = vec(384);\nconst _768 = vec(768);\n\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return sum_three(\n        scale(4, a, az, _8), _8,\n        scale(4, b, bz, _8b), _8b,\n        scale(4, c, cz, _8c), _8c, _16, out);\n}\n\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = sum(\n        sum(alen, a, blen, b, _48), _48,\n        negate(sum(clen, c, dlen, d, _48b), _48b), _48b, _96);\n\n    return sum_three(\n        scale(scale(len, _96, x, _192), _192, x, _384x), _384x,\n        scale(scale(len, _96, y, _192), _192, y, _384y), _384y,\n        scale(scale(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\n\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    s1 = ax * by;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n\n    const deterlen = sum_three(\n        liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet,\n        liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet,\n        sum_three(\n            liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet,\n            liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet,\n            liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n\n    return deter[deterlen - 1];\n}\n\nconst xdet = vec(96);\nconst ydet = vec(96);\nconst zdet = vec(96);\nconst fin = vec(1152);\n\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return sum_three(\n        scale(scale(len, _24, x, _48), _48, x, xdet), xdet,\n        scale(scale(len, _24, y, _48), _48, y, ydet), ydet,\n        scale(scale(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\n\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    s1 = aex * bey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n\n    const finlen = sum(\n        sum(\n            negate(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet,\n            liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet,\n        sum(\n            negate(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet,\n            liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 &&\n        bextail === 0 && beytail === 0 && beztail === 0 &&\n        cextail === 0 && ceytail === 0 && ceztail === 0 &&\n        dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n\n    errbound = isperrboundC * permanent + resulterrbound * Math.abs(det);\n\n    const abeps = (aex * beytail + bey * aextail) - (aey * bextail + bex * aeytail);\n    const bceps = (bex * ceytail + cey * bextail) - (bey * cextail + cex * beytail);\n    const cdeps = (cex * deytail + dey * cextail) - (cey * dextail + dex * ceytail);\n    const daeps = (dex * aeytail + aey * dextail) - (dey * aextail + aex * deytail);\n    const aceps = (aex * ceytail + cey * aextail) - (aey * cextail + cex * aeytail);\n    const bdeps = (bex * deytail + dey * bextail) - (bey * dextail + dex * beytail);\n    det +=\n        (((bex * bex + bey * bey + bez * bez) * ((cez * daeps + dez * aceps + aez * cdeps) +\n        (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) *\n        ((aez * bceps - bez * aceps + cez * abeps) + (aeztail * bc3 - beztail * ac3 + ceztail * ab3))) -\n        ((aex * aex + aey * aey + aez * aez) * ((bez * cdeps - cez * bdeps + dez * bceps) +\n        (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) *\n        ((dez * abeps + aez * bdeps + bez * daeps) + (deztail * ab3 + aeztail * bd3 + beztail * da3)))) +\n        2 * (((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) +\n        (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3)) -\n        ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) +\n        (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\n\nexport function insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    const det =\n        (clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab)) +\n        (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent =\n        (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift +\n        (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift +\n        (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift +\n        (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\n\nexport function inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    return (clift * dab - dlift * abc) + (alift * bcd - blift * cda);\n}\n", "\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nimport {orient2d} from 'robust-predicates';\n\nexport default class Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], orient2d(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], orient2d(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], orient2d(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n", "const epsilon = 1e-6;\n\nexport default class Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n", "export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n", "import Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\n\nexport default class Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n    let bx, by; // lazily computed barycenter of the hull\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (Math.abs(ab) < 1e-9) {\n        // For a degenerate triangle, the circumcenter is at the infinity, in a\n        // direction orthogonal to the halfedge and away from the “center” of\n        // the diagram <bx, by>, defined as the hull’s barycenter.\n        if (bx === undefined) {\n          bx = by = 0;\n          for (const i of hull) bx += points[i * 2], by += points[i * 2 + 1];\n          bx /= hull.length, by /= hull.length;\n        }\n        const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n        x = (x1 + x3) / 2 - a * ey;\n        y = (y1 + y3) / 2 + a * ex;\n      } else {\n        const d = 1 / ab;\n        const bl = dx * dx + dy * dy;\n        const cl = ex * ex + ey * ey;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new Polygon;\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] === cj[aj]\n              && ci[ai + 1] === cj[aj + 1]\n              && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj]\n              && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return this._simplify(V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points));\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1 = 0;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    // for more robustness, always consider the segment in the same order\n    const flip = c0 < c1;\n    if (flip) [x0, y0, x1, y1, c0, c1] = [x1, y1, x0, y0, c1, c0];\n    while (true) {\n      if (c0 === 0 && c1 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n      // undefined, the conditional statement will be executed.\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n  _simplify(P) {\n    if (P && P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n          P.splice(j, 2), i -= 2;\n        }\n      }\n      if (!P.length) P = null;\n    }\n    return P;\n  }\n}\n", "import Delaunator from \"delaunator\";\nimport Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\nimport Voronoi from \"./voronoi.js\";\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nexport default class Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new Delaunator(points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new Delaunator(points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) {\n        inedges[hull[1]] = 0;\n        this.triangles[1] = hull[1];\n        this.triangles[2] = hull[1];\n      }\n    }\n  }\n  voronoi(bounds) {\n    return new Voronoi(this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r) {\n    if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n    r = r == undefined ? 2 : +r;\n    const buffer = context == null ? context = new Path : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new Polygon;\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new Polygon;\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n", "import { VoronoiDomain, VoronoiLayer } from './types'\n\nexport const defaultVoronoiProps = {\n    xDomain: [0, 1] as VoronoiDomain,\n    yDomain: [0, 1] as VoronoiDomain,\n    layers: ['links', 'cells', 'points', 'bounds'] as VoronoiLayer[],\n    enableLinks: false,\n    linkLineWidth: 1,\n    linkLineColor: '#bbbbbb',\n    enableCells: true,\n    cellLineWidth: 2,\n    cellLineColor: '#000000',\n    enablePoints: true,\n    pointSize: 4,\n    pointColor: '#666666',\n    role: 'img',\n}\n", "import { Margin, defaultMargin as coreDefaultMargin } from '@nivo/core'\nimport { TooltipAnchor, TooltipPosition } from '@nivo/tooltip'\n\nexport const defaultNodePositionAccessor = (node: {\n    x: number\n    y: number\n}): [x: number, y: number] => [node.x, node.y]\n\nexport const defaultMargin: Margin = coreDefaultMargin\n\nexport const defaultTooltipPosition: TooltipPosition = 'cursor'\nexport const defaultTooltipAnchor: TooltipAnchor = 'top'\n", "import { Delaunay } from 'd3-delaunay'\nimport { Margin } from '@nivo/core'\nimport { NodePositionAccessor } from './types'\nimport { defaultNodePositionAccessor, defaultMargin } from './defaults'\n\n/**\n * The delaunay generator requires an array\n * where each point is defined as an array\n * of 2 elements: [x: number, y: number].\n *\n * Points represent the raw input data\n * and x/y represent accessors to x & y.\n */\nexport const computeMeshPoints = <Node>({\n    points,\n    getNodePosition = defaultNodePositionAccessor as NodePositionAccessor<Node>,\n    margin = defaultMargin,\n}: {\n    points: readonly Node[]\n    getNodePosition?: NodePositionAccessor<Node>\n    margin?: Margin\n}): [number, number][] => {\n    return points.map(node => {\n        const [x, y] = getNodePosition(node)\n\n        return [x + margin.left, y + margin.top]\n    })\n}\n\nexport const computeMesh = ({\n    points,\n    width,\n    height,\n    margin = defaultMargin,\n    debug,\n}: {\n    points: readonly [number, number][]\n    width: number\n    height: number\n    margin?: Margin\n    debug?: boolean\n}) => {\n    const delaunay = Delaunay.from(points)\n    const voronoi = debug\n        ? delaunay.voronoi([\n              0,\n              0,\n              margin.left + width + margin.right,\n              margin.top + height + margin.bottom,\n          ])\n        : undefined\n\n    return { points, delaunay, voronoi }\n}\n", "import {\n    MouseEvent,\n    MutableRefObject,\n    TouchEvent,\n    useCallback,\n    useEffect,\n    useMemo,\n    useRef,\n    useState,\n} from 'react'\nimport { scaleLinear } from 'd3-scale'\nimport { Delaunay } from 'd3-delaunay'\nimport { getDistance, getRelativeCursor, Margin } from '@nivo/core'\nimport { TooltipAnchor, TooltipPosition, useTooltip } from '@nivo/tooltip'\nimport { computeMeshPoints, computeMesh } from './computeMesh'\nimport {\n    VoronoiCommonProps,\n    VoronoiDatum,\n    VoronoiCustomLayerProps,\n    NodeMouseHandler,\n    // DatumTouchHandler,\n    NodePositionAccessor,\n    NodeTouchHandler,\n} from './types'\nimport {\n    defaultMargin,\n    defaultNodePositionAccessor,\n    defaultTooltipPosition,\n    defaultTooltipAnchor,\n} from './defaults'\n\nexport const useVoronoiMesh = <Node>({\n    points,\n    getNodePosition = defaultNodePositionAccessor as NodePositionAccessor<Node>,\n    width,\n    height,\n    margin = defaultMargin,\n    debug,\n}: {\n    points: readonly Node[]\n    getNodePosition?: NodePositionAccessor<Node>\n    // Margins are added to the chart's dimensions, so that mouse detection\n    // also works inside the margins, omit if that's not what you want.\n    // When including the margins, we recommend to set a `detectionRadius` as well.\n    margin?: Margin\n    width: number\n    height: number\n    debug?: boolean\n}) =>\n    useMemo(\n        () =>\n            computeMesh({\n                points: computeMeshPoints<Node>({ points, margin, getNodePosition }),\n                width,\n                height,\n                margin,\n                debug,\n            }),\n        [getNodePosition, points, width, height, margin, debug]\n    )\n\nexport const useVoronoi = ({\n    data,\n    width,\n    height,\n    xDomain,\n    yDomain,\n}: {\n    data: VoronoiDatum[]\n    width: number\n    height: number\n    xDomain: VoronoiCommonProps['xDomain']\n    yDomain: VoronoiCommonProps['yDomain']\n}) => {\n    const xScale = useMemo(() => scaleLinear().domain(xDomain).range([0, width]), [xDomain, width])\n    const yScale = useMemo(\n        () => scaleLinear().domain(yDomain).range([0, height]),\n        [yDomain, height]\n    )\n\n    const points = useMemo(\n        () =>\n            data.map(d => ({\n                x: xScale(d.x),\n                y: yScale(d.y),\n                data: d,\n            })),\n        [data, xScale, yScale]\n    )\n\n    return useMemo(() => {\n        const delaunay = Delaunay.from(points.map(p => [p.x, p.y]))\n        const voronoi = delaunay.voronoi([0, 0, width, height])\n\n        return {\n            points,\n            delaunay,\n            voronoi,\n        }\n    }, [points, width, height])\n}\n\n/**\n * Memoize the context to pass to custom layers.\n */\nexport const useVoronoiLayerContext = ({\n    points,\n    delaunay,\n    voronoi,\n}: VoronoiCustomLayerProps): VoronoiCustomLayerProps =>\n    useMemo(\n        () => ({\n            points,\n            delaunay,\n            voronoi,\n        }),\n        [points, delaunay, voronoi]\n    )\n\nexport const useMeshEvents = <Node, ElementType extends Element>({\n    elementRef,\n    nodes,\n    getNodePosition = defaultNodePositionAccessor as NodePositionAccessor<Node>,\n    delaunay,\n    setCurrent: setCurrentNode,\n    margin = defaultMargin,\n    detectionRadius = Infinity,\n    isInteractive = true,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    enableTouchCrosshair = false,\n    tooltip,\n    tooltipPosition = defaultTooltipPosition,\n    tooltipAnchor = defaultTooltipAnchor,\n}: {\n    elementRef: MutableRefObject<ElementType | null>\n    nodes: readonly Node[]\n    getNodePosition?: NodePositionAccessor<Node>\n    delaunay: Delaunay<Node>\n    setCurrent?: (node: Node | null) => void\n    margin?: Margin\n    detectionRadius?: number\n    isInteractive?: boolean\n    onMouseEnter?: NodeMouseHandler<Node>\n    onMouseMove?: NodeMouseHandler<Node>\n    onMouseLeave?: NodeMouseHandler<Node>\n    onMouseDown?: NodeMouseHandler<Node>\n    onMouseUp?: NodeMouseHandler<Node>\n    onClick?: NodeMouseHandler<Node>\n    onDoubleClick?: NodeMouseHandler<Node>\n    onTouchStart?: NodeTouchHandler<Node>\n    onTouchMove?: NodeTouchHandler<Node>\n    onTouchEnd?: NodeTouchHandler<Node>\n    enableTouchCrosshair?: boolean\n    tooltip?: (node: Node) => JSX.Element\n    tooltipPosition?: TooltipPosition\n    tooltipAnchor?: TooltipAnchor\n}) => {\n    // Store the index of the current point and the current node.\n    const [current, setCurrent] = useState<[number, Node] | null>(null)\n\n    // Keep track of the previous index and node, this is needed as we don't have enter/leave events\n    // for each node because we use a single rect element to capture events.\n    const previous = useRef<[number, Node] | null>(null)\n\n    useEffect(() => {\n        previous.current = current\n    }, [previous, current])\n\n    const findNode = useCallback(\n        (event: MouseEvent<ElementType> | TouchEvent<ElementType>): null | [number, Node] => {\n            if (!elementRef.current || nodes.length === 0) return null\n\n            const [x, y] = getRelativeCursor(elementRef.current, event)\n\n            let index: number | null = delaunay.find(x, y)\n            let node = index !== undefined ? nodes[index] : null\n\n            if (node && detectionRadius !== Infinity) {\n                const [nodeX, nodeY] = getNodePosition(node)\n                if (getDistance(x, y, nodeX + margin.left, nodeY + margin.top) > detectionRadius) {\n                    index = null\n                    node = null\n                }\n            }\n\n            if (index === null || node === null) return null\n\n            return [index, node]\n        },\n        [elementRef, delaunay, nodes, getNodePosition, margin, detectionRadius]\n    )\n\n    const { showTooltipAt, showTooltipFromEvent, hideTooltip } = useTooltip()\n    const showTooltip = useMemo(() => {\n        if (!tooltip) return undefined\n\n        if (tooltipPosition === 'cursor') {\n            // Following the cursor.\n            return (node: Node, event: MouseEvent<ElementType>) => {\n                showTooltipFromEvent(tooltip(node), event, tooltipAnchor)\n            }\n        }\n\n        // Fixed at the node's position.\n        return (node: Node) => {\n            const [x, y] = getNodePosition(node)\n            showTooltipAt(tooltip(node), [x + margin.left, y + margin.top], tooltipAnchor)\n        }\n    }, [\n        showTooltipAt,\n        showTooltipFromEvent,\n        tooltip,\n        tooltipPosition,\n        tooltipAnchor,\n        getNodePosition,\n        margin,\n    ])\n\n    // Mouse enter only occurs when entering the main element,\n    // not for each node.\n    const handleMouseEnter = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            const match = findNode(event)\n\n            setCurrent(match)\n            setCurrentNode?.(match ? match[1] : null)\n\n            if (match) {\n                const node = match[1]\n\n                showTooltip?.(node, event)\n                onMouseEnter?.(match[1], event)\n            }\n        },\n        [findNode, setCurrent, setCurrentNode, showTooltip, onMouseEnter]\n    )\n\n    // Handle mouse enter/move/leave, relying on `previous` to simulate events.\n    const handleMouseMove = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            const match = findNode(event)\n\n            setCurrent(match)\n\n            if (match) {\n                const [index, node] = match\n\n                setCurrentNode?.(node)\n                showTooltip?.(node, event)\n\n                if (previous.current) {\n                    const [previousIndex, previousNode] = previous.current\n                    if (index !== previousIndex) {\n                        // Simulate an enter event if the previous index is different.\n                        onMouseLeave?.(previousNode, event)\n                    } else {\n                        // If it's the same, trigger a regular move event.\n                        onMouseMove?.(node, event)\n                    }\n                } else {\n                    onMouseEnter?.(node, event)\n                }\n            } else {\n                setCurrentNode?.(null)\n                hideTooltip?.()\n\n                if (previous.current) {\n                    // Simulate a leave event if there's a previous node.\n                    onMouseLeave?.(previous.current[1], event)\n                }\n            }\n        },\n        [\n            findNode,\n            setCurrent,\n            setCurrentNode,\n            previous,\n            onMouseEnter,\n            onMouseMove,\n            onMouseLeave,\n            showTooltip,\n            hideTooltip,\n        ]\n    )\n\n    // Mouse leave only occurs when leaving the main element,\n    // not for each node.\n    const handleMouseLeave = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            setCurrent(null)\n            setCurrentNode?.(null)\n\n            hideTooltip()\n\n            if (onMouseLeave && previous.current) {\n                onMouseLeave(previous.current[1], event)\n            }\n        },\n        [setCurrent, setCurrentNode, previous, hideTooltip, onMouseLeave]\n    )\n\n    const handleMouseDown = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            const match = findNode(event)\n\n            setCurrent(match)\n\n            if (match) onMouseDown?.(match[1], event)\n        },\n        [findNode, setCurrent, onMouseDown]\n    )\n\n    const handleMouseUp = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            const match = findNode(event)\n\n            setCurrent(match)\n\n            if (match) onMouseUp?.(match[1], event)\n        },\n        [findNode, setCurrent, onMouseUp]\n    )\n\n    const handleClick = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            const match = findNode(event)\n\n            setCurrent(match)\n\n            if (match) onClick?.(match[1], event)\n        },\n        [findNode, setCurrent, onClick]\n    )\n\n    const handleDoubleClick = useCallback(\n        (event: MouseEvent<ElementType>) => {\n            const match = findNode(event)\n\n            setCurrent(match)\n\n            if (match) onDoubleClick?.(match[1], event)\n        },\n        [findNode, setCurrent, onDoubleClick]\n    )\n\n    const handleTouchStart = useCallback(\n        (event: TouchEvent<ElementType>) => {\n            const match = findNode(event)\n\n            if (enableTouchCrosshair) {\n                setCurrent(match)\n                setCurrentNode?.(match ? match[1] : null)\n            }\n\n            if (match) onTouchStart?.(match[1], event)\n        },\n        [findNode, setCurrent, setCurrentNode, enableTouchCrosshair, onTouchStart]\n    )\n\n    const handleTouchMove = useCallback(\n        (event: TouchEvent<ElementType>) => {\n            const match = findNode(event)\n\n            if (enableTouchCrosshair) {\n                setCurrent(match)\n                setCurrentNode?.(match ? match[1] : null)\n            }\n\n            if (match) onTouchMove?.(match[1], event)\n        },\n        [findNode, setCurrent, setCurrentNode, enableTouchCrosshair, onTouchMove]\n    )\n\n    const handleTouchEnd = useCallback(\n        (event: TouchEvent<SVGRectElement>) => {\n            if (enableTouchCrosshair) {\n                setCurrent(null)\n                setCurrentNode?.(null)\n            }\n\n            if (onTouchEnd && previous.current) {\n                onTouchEnd(previous.current[1], event)\n            }\n        },\n        [enableTouchCrosshair, setCurrent, setCurrentNode, onTouchEnd, previous]\n    )\n\n    return {\n        current,\n        handleMouseEnter: isInteractive ? handleMouseEnter : undefined,\n        handleMouseMove: isInteractive ? handleMouseMove : undefined,\n        handleMouseLeave: isInteractive ? handleMouseLeave : undefined,\n        handleMouseDown: isInteractive ? handleMouseDown : undefined,\n        handleMouseUp: isInteractive ? handleMouseUp : undefined,\n        handleClick: isInteractive ? handleClick : undefined,\n        handleDoubleClick: isInteractive ? handleDoubleClick : undefined,\n        handleTouchStart: isInteractive ? handleTouchStart : undefined,\n        handleTouchMove: isInteractive ? handleTouchMove : undefined,\n        handleTouchEnd: isInteractive ? handleTouchEnd : undefined,\n    }\n}\n\n/**\n * Compute a voronoi mesh and corresponding events.\n */\nexport const useMesh = <Node, ElementType extends Element>({\n    elementRef,\n    nodes,\n    getNodePosition,\n    width,\n    height,\n    margin = defaultMargin,\n    isInteractive = true,\n    detectionRadius = Infinity,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    tooltip,\n    tooltipPosition = defaultTooltipPosition,\n    tooltipAnchor = defaultTooltipAnchor,\n    debug = false,\n}: {\n    elementRef: MutableRefObject<ElementType | null>\n    nodes: readonly Node[]\n    getNodePosition?: NodePositionAccessor<Node>\n    width: number\n    height: number\n    margin?: Margin\n    isInteractive?: boolean\n    detectionRadius?: number\n    setCurrent?: (node: Node | null) => void\n    onMouseEnter?: NodeMouseHandler<Node>\n    onMouseMove?: NodeMouseHandler<Node>\n    onMouseLeave?: NodeMouseHandler<Node>\n    onMouseDown?: NodeMouseHandler<Node>\n    onMouseUp?: NodeMouseHandler<Node>\n    onClick?: NodeMouseHandler<Node>\n    onDoubleClick?: NodeMouseHandler<Node>\n    tooltip?: (node: Node) => JSX.Element\n    tooltipPosition?: TooltipPosition\n    tooltipAnchor?: TooltipAnchor\n    debug?: boolean\n}) => {\n    const { delaunay, voronoi } = useVoronoiMesh<Node>({\n        points: nodes,\n        getNodePosition,\n        width,\n        height,\n        margin,\n        debug,\n    })\n\n    const {\n        handleMouseEnter,\n        handleMouseMove,\n        handleMouseLeave,\n        handleMouseDown,\n        handleMouseUp,\n        handleClick,\n        handleDoubleClick,\n        current,\n    } = useMeshEvents<Node, ElementType>({\n        elementRef,\n        nodes,\n        margin,\n        setCurrent,\n        delaunay,\n        detectionRadius,\n        isInteractive,\n        onMouseEnter,\n        onMouseMove,\n        onMouseLeave,\n        onMouseDown,\n        onMouseUp,\n        onClick,\n        onDoubleClick,\n        tooltip,\n        tooltipPosition,\n        tooltipAnchor,\n    })\n\n    return {\n        delaunay,\n        voronoi,\n        current,\n        handleMouseEnter,\n        handleMouseMove,\n        handleMouseLeave,\n        handleMouseDown,\n        handleMouseUp,\n        handleClick,\n        handleDoubleClick,\n    }\n}\n", "import { createElement, Fragment, ReactNode, forwardRef, Ref } from 'react'\nimport { Container, SvgWrapper, useDimensions } from '@nivo/core'\nimport { VoronoiSvgProps, VoronoiLayerId } from './types'\nimport { defaultVoronoiProps } from './props'\nimport { useVoronoi, useVoronoiLayerContext } from './hooks'\n\ntype InnerVoronoiProps = Partial<Omit<VoronoiSvgProps, 'data' | 'width' | 'height'>> &\n    Pick<VoronoiSvgProps, 'data' | 'width' | 'height'> & {\n        forwardedRef: Ref<SVGSVGElement>\n    }\n\nconst InnerVoronoi = ({\n    data,\n    width,\n    height,\n    margin: partialMargin,\n    layers = defaultVoronoiProps.layers,\n    xDomain = defaultVoronoiProps.xDomain,\n    yDomain = defaultVoronoiProps.yDomain,\n    enableLinks = defaultVoronoiProps.enableLinks,\n    linkLineWidth = defaultVoronoiProps.linkLineWidth,\n    linkLineColor = defaultVoronoiProps.linkLineColor,\n    enableCells = defaultVoronoiProps.enableCells,\n    cellLineWidth = defaultVoronoiProps.cellLineWidth,\n    cellLineColor = defaultVoronoiProps.cellLineColor,\n    enablePoints = defaultVoronoiProps.enableCells,\n    pointSize = defaultVoronoiProps.pointSize,\n    pointColor = defaultVoronoiProps.pointColor,\n    role = defaultVoronoiProps.role,\n    forwardedRef,\n}: InnerVoronoiProps) => {\n    const { outerWidth, outerHeight, margin, innerWidth, innerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const { points, delaunay, voronoi } = useVoronoi({\n        data,\n        width: innerWidth,\n        height: innerHeight,\n        xDomain,\n        yDomain,\n    })\n\n    const layerById: Record<VoronoiLayerId, ReactNode> = {\n        links: null,\n        cells: null,\n        points: null,\n        bounds: null,\n    }\n\n    if (enableLinks && layers.includes('links')) {\n        layerById.links = (\n            <path\n                key=\"links\"\n                stroke={linkLineColor}\n                strokeWidth={linkLineWidth}\n                fill=\"none\"\n                d={delaunay.render()}\n            />\n        )\n    }\n\n    if (enableCells && layers.includes('cells')) {\n        layerById.cells = (\n            <path\n                key=\"cells\"\n                d={voronoi.render()}\n                fill=\"none\"\n                stroke={cellLineColor}\n                strokeWidth={cellLineWidth}\n            />\n        )\n    }\n\n    if (enablePoints && layers.includes('points')) {\n        layerById.points = (\n            <path\n                key=\"points\"\n                stroke=\"none\"\n                fill={pointColor}\n                d={delaunay.renderPoints(undefined, pointSize / 2)}\n            />\n        )\n    }\n\n    if (layers.includes('bounds')) {\n        layerById.bounds = (\n            <path\n                key=\"bounds\"\n                fill=\"none\"\n                stroke={cellLineColor}\n                strokeWidth={cellLineWidth}\n                d={voronoi.renderBounds()}\n            />\n        )\n    }\n\n    const layerContext = useVoronoiLayerContext({\n        points,\n        delaunay,\n        voronoi,\n    })\n\n    return (\n        <SvgWrapper\n            width={outerWidth}\n            height={outerHeight}\n            margin={margin}\n            role={role}\n            ref={forwardedRef}\n        >\n            {layers.map((layer, i) => {\n                if (layerById[layer as VoronoiLayerId] !== undefined) {\n                    return layerById[layer as VoronoiLayerId]\n                }\n\n                if (typeof layer === 'function') {\n                    return <Fragment key={i}>{createElement(layer, layerContext)}</Fragment>\n                }\n\n                return null\n            })}\n        </SvgWrapper>\n    )\n}\n\nexport const Voronoi = forwardRef(\n    (\n        {\n            theme,\n            ...props\n        }: Partial<Omit<VoronoiSvgProps, 'data' | 'width' | 'height'>> &\n            Pick<VoronoiSvgProps, 'data' | 'width' | 'height'>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <Container isInteractive={false} animate={false} theme={theme}>\n            <InnerVoronoi {...props} forwardedRef={ref} />\n        </Container>\n    )\n)\n", "import { forwardRef, Ref } from 'react'\nimport { ResponsiveWrapper, ResponsiveProps } from '@nivo/core'\nimport { VoronoiSvgProps } from './types'\nimport { Voronoi } from './Voronoi'\n\ntype ResponsiveVoronoiProps = ResponsiveProps<\n    Partial<Omit<VoronoiSvgProps, 'data'>> & Pick<VoronoiSvgProps, 'data'>\n>\n\nexport const ResponsiveVoronoi = forwardRef(\n    (\n        { defaultWidth, defaultHeight, onResize, debounceResize, ...props }: ResponsiveVoronoiProps,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <ResponsiveWrapper\n            defaultWidth={defaultWidth}\n            defaultHeight={defaultHeight}\n            onResize={onResize}\n            debounceResize={debounceResize}\n        >\n            {({ width, height }: { width: number; height: number }) => (\n                <Voronoi {...props} width={width} height={height} ref={ref} />\n            )}\n        </ResponsiveWrapper>\n    )\n)\n", "import { useMemo, useRef } from 'react'\nimport { Margin } from '@nivo/core'\nimport { TooltipAnchor, TooltipPosition } from '@nivo/tooltip'\nimport { useVoronoiMesh, useMeshEvents } from './hooks'\nimport { NodeMouseHand<PERSON>, NodePositionAccessor, NodeTouchHandler } from './types'\nimport { defaultMargin, defaultTooltipAnchor, defaultTooltipPosition } from './defaults'\n\ninterface MeshProps<Node> {\n    nodes: Node[]\n    width: number\n    height: number\n    margin?: Margin\n    getNodePosition?: NodePositionAccessor<Node>\n    // Can be used in case you want to keep track of the current node externally,\n    // the current node being the last hovered node.\n    setCurrent?: (node: Node | null) => void\n    onMouseEnter?: NodeMouseHandler<Node>\n    onMouseMove?: NodeMouseHandler<Node>\n    onMouseLeave?: NodeMouseHandler<Node>\n    onMouseDown?: NodeMouseHandler<Node>\n    onMouseUp?: NodeMouseHandler<Node>\n    onClick?: NodeMouseHandler<Node>\n    onDoubleClick?: NodeMouseHandler<Node>\n    onTouchStart?: NodeTouchHandler<Node>\n    onTouchMove?: NodeTouchHandler<Node>\n    onTouchEnd?: NodeTouchHandler<Node>\n    enableTouchCrosshair?: boolean\n    // Restrict the node detection to a given radius, default to `Infinity`.\n    detectionRadius?: number\n    // If specified, tooltips are going to be handled automatically.\n    tooltip?: (node: Node) => JSX.Element\n    tooltipPosition?: TooltipPosition\n    tooltipAnchor?: TooltipAnchor\n    // Display the voronoi mesh for debugging purpose.\n    debug?: boolean\n}\n\nexport const Mesh = <Node,>({\n    nodes,\n    width,\n    height,\n    margin = defaultMargin,\n    getNodePosition,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    enableTouchCrosshair = false,\n    detectionRadius = Infinity,\n    tooltip,\n    tooltipPosition = defaultTooltipPosition,\n    tooltipAnchor = defaultTooltipAnchor,\n    debug,\n}: MeshProps<Node>) => {\n    const elementRef = useRef<SVGRectElement | null>(null)\n\n    const { delaunay, voronoi } = useVoronoiMesh<Node>({\n        points: nodes,\n        getNodePosition,\n        width,\n        height,\n        margin,\n        debug,\n    })\n\n    const {\n        current,\n        handleMouseEnter,\n        handleMouseMove,\n        handleMouseLeave,\n        handleMouseDown,\n        handleMouseUp,\n        handleClick,\n        handleDoubleClick,\n        handleTouchStart,\n        handleTouchMove,\n        handleTouchEnd,\n    } = useMeshEvents<Node, SVGRectElement>({\n        elementRef,\n        nodes,\n        delaunay,\n        margin,\n        detectionRadius,\n        setCurrent,\n        onMouseEnter,\n        onMouseMove,\n        onMouseLeave,\n        onMouseDown,\n        onMouseUp,\n        onClick,\n        onDoubleClick,\n        onTouchStart,\n        onTouchMove,\n        onTouchEnd,\n        enableTouchCrosshair,\n        tooltip,\n        tooltipPosition,\n        tooltipAnchor,\n    })\n\n    const voronoiPath = useMemo(() => {\n        if (debug && voronoi) return voronoi.render()\n        return undefined\n    }, [debug, voronoi])\n\n    return (\n        <g ref={elementRef} transform={`translate(${-margin.left},${-margin.top})`}>\n            {debug && voronoi && (\n                <>\n                    <path d={voronoiPath} stroke=\"red\" strokeWidth={1} opacity={0.75} />\n                    {detectionRadius < Infinity && (\n                        <path\n                            stroke=\"red\"\n                            strokeWidth={0.35}\n                            fill=\"none\"\n                            d={delaunay.renderPoints(undefined, detectionRadius)}\n                        />\n                    )}\n                    {/* highlight the current cell */}\n                    {current && (\n                        <path fill=\"pink\" opacity={0.35} d={voronoi.renderCell(current[0])} />\n                    )}\n                </>\n            )}\n            {/* transparent rect to intercept mouse events */}\n            <rect\n                data-ref=\"mesh-interceptor\"\n                width={margin.left + width + margin.right}\n                height={margin.top + height + margin.bottom}\n                fill=\"red\"\n                opacity={0}\n                style={{ cursor: 'auto' }}\n                onMouseEnter={handleMouseEnter}\n                onMouseMove={handleMouseMove}\n                onMouseLeave={handleMouseLeave}\n                onMouseDown={handleMouseDown}\n                onMouseUp={handleMouseUp}\n                onTouchStart={handleTouchStart}\n                onTouchMove={handleTouchMove}\n                onTouchEnd={handleTouchEnd}\n                onClick={handleClick}\n                onDoubleClick={handleDoubleClick}\n            />\n        </g>\n    )\n}\n", "import { Delaunay, Voronoi } from 'd3-delaunay'\n\nexport const renderVoronoiToCanvas = (\n    ctx: CanvasRenderingContext2D,\n    voronoi: Voronoi<Delaunay.Point>\n) => {\n    ctx.save()\n\n    ctx.globalAlpha = 0.75\n    ctx.beginPath()\n    voronoi.render(ctx)\n    ctx.strokeStyle = 'red'\n    ctx.lineWidth = 1\n    ctx.stroke()\n\n    ctx.restore()\n}\n\nexport const renderDelaunayPointsToCanvas = (\n    ctx: CanvasRenderingContext2D,\n    delaunay: Delaunay<Delaunay.Point>,\n    radius: number\n) => {\n    ctx.save()\n\n    ctx.globalAlpha = 0.15\n    ctx.beginPath()\n    delaunay.renderPoints(ctx, radius)\n    ctx.strokeStyle = 'red'\n    ctx.lineWidth = 1\n    ctx.stroke()\n\n    ctx.restore()\n}\n\nexport const renderVoronoiCellToCanvas = (\n    ctx: CanvasRenderingContext2D,\n    voronoi: Voronoi<Delaunay.Point>,\n    index: number\n) => {\n    ctx.save()\n\n    ctx.globalAlpha = 0.35\n    ctx.beginPath()\n    voronoi.renderCell(index, ctx)\n    ctx.fillStyle = 'pink'\n    ctx.fill()\n\n    ctx.restore()\n}\n\nexport const renderDebugToCanvas = (\n    ctx: CanvasRenderingContext2D,\n    {\n        delaunay,\n        voronoi,\n        detectionRadius,\n        index,\n    }: {\n        delaunay: Delaunay<Delaunay.Point>\n        voronoi: Voronoi<Delaunay.Point>\n        detectionRadius: number\n        index: number | null\n    }\n) => {\n    renderVoronoiToCanvas(ctx, voronoi)\n\n    if (detectionRadius < Infinity) {\n        renderDelaunayPointsToCanvas(ctx, delaunay, detectionRadius)\n    }\n\n    if (index !== null) {\n        renderVoronoiCellToCanvas(ctx, voronoi, index)\n    }\n}\n", "import { memo } from 'react'\nimport { BasicTooltip } from '@nivo/tooltip'\nimport { LineSeries, PointTooltipProps } from './types'\n\nexport const NonMemoizedPointTooltip = <Series extends LineSeries>({\n    point,\n}: PointTooltipProps<Series>) => {\n    return (\n        <BasicTooltip\n            id={\n                <span>\n                    x: <strong>{point.data.xFormatted}</strong>, y:{' '}\n                    <strong>{point.data.yFormatted}</strong>\n                </span>\n            }\n            enableChip={true}\n            color={point.seriesColor}\n        />\n    )\n}\n\nexport const PointTooltip = memo(NonMemoizedPointTooltip)\n", "import { defaultAxisProps } from '@nivo/axes'\nimport {\n    CommonLineProps,\n    LineSvgPropsWithDefaults,\n    LineCanvasPropsWithDefaults,\n    DefaultSeries,\n    LineLayerId,\n} from './types'\nimport { PointTooltip } from './PointTooltip'\nimport { SliceTooltip } from './SliceTooltip'\n\nexport const commonDefaultProps: Omit<\n    CommonLineProps<DefaultSeries>,\n    | 'data'\n    | 'xFormat'\n    | 'yFormat'\n    | 'layers'\n    | 'width'\n    | 'height'\n    | 'margin'\n    | 'theme'\n    | 'pointSymbol'\n    | 'gridXValues'\n    | 'gridYValues'\n    | 'onMouseEnter'\n    | 'onMouseMove'\n    | 'onMouseLeave'\n    | 'onMouseDown'\n    | 'onMouseUp'\n    | 'onClick'\n    | 'onDoubleClick'\n    | 'onTouchStart'\n    | 'onTouchMove'\n    | 'onTouchEnd'\n> & {\n    layers: LineLayerId[]\n} = {\n    xScale: {\n        type: 'point',\n    },\n    yScale: {\n        type: 'linear',\n        min: 0,\n        max: 'auto',\n    },\n    curve: 'linear',\n    colors: { scheme: 'nivo' },\n    lineWidth: 2,\n    layers: [\n        'grid',\n        'markers',\n        'axes',\n        'areas',\n        'crosshair',\n        'lines',\n        'points',\n        'slices',\n        'mesh',\n        'legends',\n    ],\n    enablePoints: true,\n    pointSize: 6,\n    pointColor: { from: 'series.color' },\n    pointBorderWidth: 0,\n    pointBorderColor: { theme: 'background' },\n    enableArea: false,\n    areaBaselineValue: 0,\n    areaOpacity: 0.2,\n    enableGridX: true,\n    enableGridY: true,\n    legends: [],\n    isInteractive: true,\n    tooltip: PointTooltip,\n    sliceTooltip: SliceTooltip,\n    debugMesh: false,\n    renderWrapper: true,\n}\n\nexport const svgDefaultProps: Omit<\n    LineSvgPropsWithDefaults<DefaultSeries>,\n    'data' | 'width' | 'height' | 'margin' | 'theme'\n> = {\n    ...commonDefaultProps,\n    defs: [],\n    fill: [],\n    enablePointLabel: false,\n    pointLabel: 'data.yFormatted',\n    areaBlendMode: 'normal',\n    axisTop: null,\n    axisRight: null,\n    axisBottom: defaultAxisProps,\n    axisLeft: defaultAxisProps,\n    useMesh: false,\n    enableSlices: false,\n    debugSlices: false,\n    enableCrosshair: true,\n    crosshairType: 'bottom-left',\n    enableTouchCrosshair: false,\n    initialHiddenIds: [],\n    animate: true,\n    motionConfig: 'gentle',\n    role: 'img',\n    isFocusable: false,\n}\n\nexport const canvasDefaultProps: Omit<\n    LineCanvasPropsWithDefaults<DefaultSeries>,\n    'data' | 'width' | 'height' | 'margin' | 'theme'\n> = {\n    ...commonDefaultProps,\n    pixelRatio: typeof window !== 'undefined' ? (window.devicePixelRatio ?? 1) : 1,\n    axisTop: null,\n    axisRight: null,\n    axisBottom: defaultAxisProps,\n    axisLeft: defaultAxisProps,\n}\n", "import { memo } from 'react'\nimport { useTheme } from '@nivo/theming'\nimport { Chip, TableTooltip } from '@nivo/tooltip'\nimport { LineSeries, SliceTooltipProps } from './types'\n\nexport const NonMemoizedSliceTooltip = <Series extends LineSeries>({\n    slice,\n    axis,\n}: SliceTooltipProps<Series>) => {\n    const theme = useTheme()\n    const otherAxis = axis === 'x' ? 'y' : 'x'\n\n    return (\n        <TableTooltip\n            rows={slice.points.map(point => [\n                <Chip key=\"chip\" color={point.seriesColor} style={theme.tooltip.chip} />,\n                point.seriesId,\n                <span key=\"value\" style={theme.tooltip.tableCellValue}>\n                    {point.data[`${otherAxis}Formatted`]}\n                </span>,\n            ])}\n        />\n    )\n}\n\nexport const SliceTooltip = memo(NonMemoizedSliceTooltip)\n", "import { useCallback, useMemo, useState } from 'react'\nimport { area, line } from 'd3-shape'\nimport uniqueId from 'lodash/uniqueId.js'\nimport { curveFromProp, useValueFormatter } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport {\n    useOrdinalColorScale,\n    useInheritedColor,\n    OrdinalColorScaleConfig,\n    InheritedColorConfig,\n} from '@nivo/colors'\nimport { computeXYScalesForSeries } from '@nivo/scales'\nimport { commonDefaultProps, svgDefaultProps } from './defaults'\nimport {\n    LineSeries,\n    CommonLineProps,\n    DataProps,\n    InferX,\n    InferY,\n    InferSeriesId,\n    LineSvgProps,\n    LineGenerator,\n    AreaGenerator,\n    AllowedValue,\n    ComputedSeries,\n    Point,\n    SliceData,\n    PointColorContext,\n} from './types'\n\nexport function useLineGenerator(curve: CommonLineProps<LineSeries>['curve']): LineGenerator {\n    return useMemo(() => {\n        return line<{\n            x: number\n            y: number\n        }>()\n            .defined(d => d.x !== null && d.y !== null)\n            .x(d => d.x)\n            .y(d => d.y)\n            .curve(curveFromProp(curve))\n    }, [curve])\n}\n\nexport function useAreaGenerator<Y extends AllowedValue>({\n    curve,\n    yScale,\n    areaBaselineValue,\n}: {\n    curve: CommonLineProps<LineSeries>['curve']\n    yScale: (y: Y) => number\n    areaBaselineValue: Y\n}): AreaGenerator {\n    return useMemo(() => {\n        return area<{\n            x: number\n            y: number\n        }>()\n            .defined(d => d.x !== null && d.y !== null)\n            .x(d => d.x)\n            .y1(d => d.y)\n            .curve(curveFromProp(curve))\n            .y0(yScale(areaBaselineValue))\n    }, [curve, yScale, areaBaselineValue])\n}\n\nfunction usePoints<Series extends LineSeries>({\n    series,\n    getPointColor,\n    getPointBorderColor,\n    formatX,\n    formatY,\n}: {\n    series: ComputedSeries<Series>[]\n    getPointColor: (context: PointColorContext<Series>) => string\n    getPointBorderColor: (point: Omit<Point<Series>, 'borderColor'>) => string\n    formatX: (x: InferX<Series>) => string\n    formatY: (y: InferY<Series>) => string\n}) {\n    return useMemo(() => {\n        return series.reduce((acc, seriesItem, seriesIndex) => {\n            return [\n                ...acc,\n                ...seriesItem.data\n                    .filter(datum => datum.position.x !== null && datum.position.y !== null)\n                    .map((datum, indexInSeries) => {\n                        const point: Omit<Point<Series>, 'color' | 'borderColor'> & {\n                            color?: string\n                            borderColor?: string\n                        } = {\n                            id: `${seriesItem.id}.${indexInSeries}`,\n                            indexInSeries,\n                            absIndex: acc.length + indexInSeries,\n                            seriesIndex,\n                            seriesId: seriesItem.id,\n                            seriesColor: seriesItem.color,\n                            x: datum.position.x,\n                            y: datum.position.y,\n                            data: {\n                                ...datum.data,\n                                xFormatted: formatX(datum.data.x as InferX<Series>),\n                                yFormatted: formatY(datum.data.y as InferY<Series>),\n                            },\n                        }\n                        point.color = getPointColor({\n                            series: seriesItem,\n                            point: point as Omit<Point<Series>, 'color' | 'borderColor'>,\n                        })\n                        point.borderColor = getPointBorderColor(\n                            point as Omit<Point<Series>, 'borderColor'>\n                        )\n\n                        return point as Point<Series>\n                    }),\n            ]\n        }, [] as Point<Series>[])\n    }, [series, getPointColor, getPointBorderColor, formatX, formatY])\n}\n\nexport const useSlices = <Series extends LineSeries>({\n    componentId,\n    enableSlices,\n    points,\n    width,\n    height,\n}: {\n    componentId: string\n    enableSlices: Exclude<LineSvgProps<Series>['enableSlices'], undefined>\n    points: Point<Series>[]\n    width: number\n    height: number\n}) => {\n    return useMemo(() => {\n        if (enableSlices === 'x') {\n            const map = new Map()\n            points.forEach(point => {\n                if (point.data.x === null || point.data.y === null) return\n                if (!map.has(point.x)) map.set(point.x, [point])\n                else map.get(point.x).push(point)\n            })\n\n            return Array.from(map.entries())\n                .sort((a, b) => a[0] - b[0])\n                .map(([x, slicePoints], i, slices) => {\n                    const prevSlice = slices[i - 1]\n                    const nextSlice = slices[i + 1]\n\n                    let x0\n                    if (!prevSlice) x0 = x\n                    else x0 = x - (x - prevSlice[0]) / 2\n\n                    let sliceWidth\n                    if (!nextSlice) sliceWidth = width - x0\n                    else sliceWidth = x - x0 + (nextSlice[0] - x) / 2\n\n                    return {\n                        id: `slice:${componentId}:${x}`,\n                        x0,\n                        x,\n                        y0: 0,\n                        y: 0,\n                        width: sliceWidth,\n                        height,\n                        points: slicePoints.reverse(),\n                    } as SliceData<Series>\n                })\n        } else if (enableSlices === 'y') {\n            const map = new Map()\n            points.forEach(point => {\n                if (point.data.x === null || point.data.y === null) return\n                if (!map.has(point.y)) map.set(point.y, [point])\n                else map.get(point.y).push(point)\n            })\n\n            return Array.from(map.entries())\n                .sort((a, b) => a[0] - b[0])\n                .map(([y, slicePoints], i, slices) => {\n                    const prevSlice = slices[i - 1]\n                    const nextSlice = slices[i + 1]\n\n                    let y0\n                    if (!prevSlice) y0 = y\n                    else y0 = y - (y - prevSlice[0]) / 2\n\n                    let sliceHeight\n                    if (!nextSlice) sliceHeight = height - y0\n                    else sliceHeight = y - y0 + (nextSlice[0] - y) / 2\n\n                    return {\n                        id: y,\n                        x0: 0,\n                        x: 0,\n                        y0,\n                        y,\n                        width,\n                        height: sliceHeight,\n                        points: slicePoints.reverse(),\n                    } as SliceData<Series>\n                })\n        }\n\n        return []\n    }, [componentId, enableSlices, height, points, width])\n}\n\nexport const LINE_UNIQUE_ID_PREFIX = 'line'\n\nexport const useLine = <Series extends LineSeries>({\n    data,\n    xScale: xScaleSpec = commonDefaultProps.xScale,\n    xFormat,\n    yScale: yScaleSpec = commonDefaultProps.yScale,\n    yFormat,\n    width,\n    height,\n    colors = commonDefaultProps.colors as OrdinalColorScaleConfig<Series>,\n    curve = commonDefaultProps.curve,\n    areaBaselineValue = commonDefaultProps.areaBaselineValue as InferY<Series>,\n    pointColor = commonDefaultProps.pointColor as InheritedColorConfig<PointColorContext<Series>>,\n    pointBorderColor = commonDefaultProps.pointBorderColor as InheritedColorConfig<\n        Omit<Point<Series>, 'borderColor'>\n    >,\n    enableSlices = svgDefaultProps.enableSlices as Exclude<\n        LineSvgProps<Series>['enableSlices'],\n        undefined\n    >,\n    initialHiddenIds = svgDefaultProps.initialHiddenIds as InferSeriesId<Series>[],\n}: DataProps<Series> &\n    Pick<\n        CommonLineProps<Series>,\n        | 'xScale'\n        | 'yScale'\n        | 'colors'\n        | 'curve'\n        | 'areaBaselineValue'\n        | 'pointColor'\n        | 'pointBorderColor'\n    > & {\n        xFormat?: CommonLineProps<Series>['xFormat']\n        yFormat?: CommonLineProps<Series>['yFormat']\n    } & Pick<LineSvgProps<Series>, 'enableSlices' | 'initialHiddenIds'> & {\n        width: number\n        height: number\n    }): {\n    legendData: {\n        id: InferSeriesId<Series>\n        label: string\n        color: string\n        hidden: boolean\n    }[]\n    toggleSeries: (id: InferSeriesId<Series>) => void\n    lineGenerator: LineGenerator\n    areaGenerator: AreaGenerator\n    getColor: (series: Series) => string\n    series: ComputedSeries<Series>[]\n    xScale: (x: InferX<Series>) => number\n    yScale: (y: InferY<Series>) => number\n    slices: SliceData<Series>[]\n    points: Point<Series>[]\n} => {\n    const [componentId] = useState(uniqueId(LINE_UNIQUE_ID_PREFIX))\n    const formatX = useValueFormatter(xFormat)\n    const formatY = useValueFormatter(yFormat)\n    const getColor = useOrdinalColorScale(colors, 'id')\n    const theme = useTheme()\n    const getPointColor = useInheritedColor(pointColor, theme)\n    const getPointBorderColor = useInheritedColor(pointBorderColor, theme)\n    const [hiddenIds, setHiddenIds] = useState(initialHiddenIds ?? [])\n\n    const {\n        xScale,\n        yScale,\n        series: rawSeries,\n    } = useMemo(\n        () =>\n            computeXYScalesForSeries<Series, Series['data'][number]>(\n                data.filter(item => hiddenIds.indexOf(item.id as InferSeriesId<Series>) === -1),\n                xScaleSpec,\n                yScaleSpec,\n                width,\n                height\n            ),\n        [data, hiddenIds, xScaleSpec, yScaleSpec, width, height]\n    )\n\n    const { legendData, series } = useMemo(() => {\n        const dataWithColor = data.map(seriesItem => ({\n            id: seriesItem.id as InferSeriesId<Series>,\n            label: `${seriesItem.id}`,\n            color: getColor(seriesItem),\n        }))\n\n        const series = dataWithColor\n            .map(datum => ({\n                ...rawSeries.find(seriesItem => seriesItem.id === datum.id)!,\n                color: datum.color,\n            }))\n            .filter(item => Boolean(item.id)) as unknown as ComputedSeries<Series>[]\n\n        const legendData = dataWithColor\n            .map(item => ({\n                ...item,\n                hidden: !series.find(seriesItem => seriesItem.id === item.id),\n            }))\n            .reverse()\n\n        return { legendData, series }\n    }, [data, rawSeries, getColor])\n\n    const toggleSeries = useCallback((id: InferSeriesId<Series>) => {\n        setHiddenIds(state =>\n            state.indexOf(id) > -1 ? state.filter(item => item !== id) : [...state, id]\n        )\n    }, [])\n\n    const points = usePoints<Series>({\n        series,\n        getPointColor,\n        getPointBorderColor,\n        formatX,\n        formatY,\n    })\n\n    const slices = useSlices<Series>({\n        componentId,\n        enableSlices,\n        points,\n        width,\n        height,\n    })\n\n    const lineGenerator = useLineGenerator(curve)\n    const areaGenerator = useAreaGenerator<InferY<Series>>({\n        curve,\n        yScale: yScale as (y: InferY<Series>) => number,\n        areaBaselineValue,\n    })\n\n    return {\n        legendData,\n        toggleSeries,\n        lineGenerator,\n        areaGenerator,\n        getColor,\n        series,\n        xScale: xScale as (x: InferX<Series>) => number,\n        yScale: yScale as (y: InferY<Series>) => number,\n        slices,\n        points,\n    }\n}\n", "import { memo } from 'react'\nimport { useSpring, animated } from '@react-spring/web'\nimport { CssMixBlendMode, useAnimatedPath, useMotionConfig } from '@nivo/core'\nimport { LineSeries, ComputedSeries, AreaGenerator } from './types'\n\nconst AreaPath = ({\n    areaBlendMode,\n    areaOpacity,\n    color,\n    fill,\n    path,\n}: {\n    areaBlendMode: CssMixBlendMode\n    areaOpacity: number\n    color: string\n    fill?: string\n    path: string\n}) => {\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedPath = useAnimatedPath(path)\n    const animatedProps = useSpring({\n        color,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <animated.path\n            d={animatedPath}\n            fill={fill ? fill : animatedProps.color}\n            fillOpacity={areaOpacity}\n            strokeWidth={0}\n            style={{\n                mixBlendMode: areaBlendMode,\n            }}\n        />\n    )\n}\n\nconst NonMemoizedAreas = <Series extends LineSeries>({\n    areaGenerator,\n    areaOpacity,\n    areaBlendMode,\n    series,\n}: {\n    areaGenerator: AreaGenerator\n    areaOpacity: number\n    areaBlendMode: CssMixBlendMode\n    series: readonly ComputedSeries<Series>[]\n}) => {\n    const reversedSeries = series.slice(0).reverse()\n\n    return (\n        <g>\n            {reversedSeries.map(seriesItem => (\n                <AreaPath\n                    key={`${seriesItem.id}`}\n                    path={areaGenerator(seriesItem.data.map(d => d.position))!}\n                    {...{ areaOpacity, areaBlendMode, ...seriesItem }}\n                />\n            ))}\n        </g>\n    )\n}\n\nexport const Areas = memo(NonMemoizedAreas) as typeof NonMemoizedAreas\n", "import { memo, useMemo } from 'react'\nimport { animated } from '@react-spring/web'\nimport { useAnimatedPath } from '@nivo/core'\nimport { LineGenerator } from './types'\n\nexport const NonMemoizedLinesItem = ({\n    lineGenerator,\n    points,\n    color,\n    thickness,\n}: {\n    lineGenerator: LineGenerator\n    points: { x: number; y: number }[]\n    color: string\n    thickness: number\n}) => {\n    const path = useMemo(() => lineGenerator(points), [lineGenerator, points])\n    const animatedPath = useAnimatedPath(path!)\n\n    return <animated.path d={animatedPath} fill=\"none\" strokeWidth={thickness} stroke={color} />\n}\n\nexport const LinesItem = memo(NonMemoizedLinesItem) as typeof NonMemoizedLinesItem\n", "import { memo } from 'react'\nimport { LineSeries, ComputedSeries, LineGenerator } from './types'\nimport { LinesItem } from './LinesItem'\n\nexport const NonMemoizedLines = <Series extends LineSeries>({\n    series,\n    lineGenerator,\n    lineWidth,\n}: {\n    series: readonly ComputedSeries<Series>[]\n    lineGenerator: LineGenerator\n    lineWidth: number\n}) => {\n    return (\n        <>\n            {series\n                .slice(0)\n                .reverse()\n                .map(({ id, data, color }) => (\n                    <LinesItem\n                        key={id}\n                        points={data.map(d => d.position)}\n                        lineGenerator={lineGenerator}\n                        color={color}\n                        thickness={lineWidth}\n                    />\n                ))}\n        </>\n    )\n}\n\nexport const Lines = memo(NonMemoizedLines) as typeof NonMemoizedLines\n", "import { createElement, memo, useCallback, MouseEvent, TouchEvent } from 'react'\nimport { useTooltip } from '@nivo/tooltip'\nimport { LineSeries, SliceData, CommonLineProps, LineSvgProps } from './types'\n\nexport const NonMemoizedSlicesItem = <Series extends LineSeries>({\n    slice,\n    slices,\n    axis,\n    debug,\n    tooltip,\n    isCurrent,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n}: {\n    slice: SliceData<Series>\n    slices: readonly SliceData<Series>[]\n    axis: Exclude<LineSvgProps<Series>['enableSlices'], undefined | false>\n    debug: boolean\n    tooltip: CommonLineProps<Series>['sliceTooltip']\n    isCurrent: boolean\n    setCurrent: (slice: SliceData<Series> | null) => void\n    onMouseEnter?: CommonLineProps<Series>['onMouseEnter']\n    onMouseMove?: CommonLineProps<Series>['onMouseMove']\n    onMouseLeave?: CommonLineProps<Series>['onMouseLeave']\n    onMouseDown?: CommonLineProps<Series>['onMouseDown']\n    onMouseUp?: CommonLineProps<Series>['onMouseUp']\n    onClick?: CommonLineProps<Series>['onClick']\n    onDoubleClick?: CommonLineProps<Series>['onDoubleClick']\n    onTouchStart?: CommonLineProps<Series>['onTouchStart']\n    onTouchMove?: CommonLineProps<Series>['onTouchMove']\n    onTouchEnd?: CommonLineProps<Series>['onTouchEnd']\n}) => {\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleMouseEnter = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            showTooltipFromEvent(createElement(tooltip, { slice, axis }), event, 'right')\n            setCurrent(slice)\n            onMouseEnter?.(slice, event)\n        },\n        [showTooltipFromEvent, tooltip, slice, axis, setCurrent, onMouseEnter]\n    )\n\n    const handleMouseMove = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            showTooltipFromEvent(createElement(tooltip, { slice, axis }), event, 'right')\n            onMouseMove?.(slice, event)\n        },\n        [showTooltipFromEvent, tooltip, slice, axis, onMouseMove]\n    )\n\n    const handleMouseLeave = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            hideTooltip()\n            setCurrent(null)\n            onMouseLeave?.(slice, event)\n        },\n        [hideTooltip, setCurrent, onMouseLeave, slice]\n    )\n\n    const handleMouseDown = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onMouseDown?.(slice, event)\n        },\n        [slice, onMouseDown]\n    )\n\n    const handleMouseUp = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onMouseUp?.(slice, event)\n        },\n        [slice, onMouseUp]\n    )\n\n    const handleClick = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onClick?.(slice, event)\n        },\n        [slice, onClick]\n    )\n\n    const handleDoubleClick = useCallback(\n        (event: MouseEvent<SVGRectElement>) => {\n            onDoubleClick?.(slice, event)\n        },\n        [slice, onDoubleClick]\n    )\n\n    const handeOnTouchStart = useCallback(\n        (event: TouchEvent<SVGRectElement>) => {\n            showTooltipFromEvent(createElement(tooltip, { slice, axis }), event, 'right')\n            setCurrent(slice)\n            onTouchStart?.(slice, event)\n        },\n        [axis, onTouchStart, setCurrent, showTooltipFromEvent, slice, tooltip]\n    )\n\n    const handeOnTouchMove = useCallback(\n        (event: TouchEvent<SVGRectElement>) => {\n            // This event will be locked to the element that was touched originally\n            // We find the element that is currently being \"hovered over\" by getting the element at the touch point\n            const touchPoint = event.touches[0]\n            const touchingElement = document.elementFromPoint(\n                touchPoint.clientX,\n                touchPoint.clientY\n            )\n            // Is this a nivo ref?\n            const touchingSliceId = touchingElement?.getAttribute('data-ref')\n            if (touchingSliceId) {\n                // Is this a slice for this graph?\n                const slice = slices.find(slice => slice.id === touchingSliceId)\n                if (slice) {\n                    showTooltipFromEvent(createElement(tooltip, { slice, axis }), event, 'right')\n                    setCurrent(slice)\n                }\n            }\n\n            // Note here, this will pass the original slice, not the one we found\n            // But this can be found with document.elementFromPoint()\n            onTouchMove?.(slice, event)\n        },\n        [axis, onTouchMove, setCurrent, showTooltipFromEvent, slice, slices, tooltip]\n    )\n\n    const handleOnTouchEnd = useCallback(\n        (event: TouchEvent<SVGRectElement>) => {\n            hideTooltip()\n            setCurrent(null)\n            onTouchEnd?.(slice, event)\n        },\n        [hideTooltip, setCurrent, onTouchEnd, slice]\n    )\n\n    return (\n        <rect\n            x={slice.x0}\n            y={slice.y0}\n            width={slice.width}\n            height={slice.height}\n            stroke=\"red\"\n            strokeWidth={debug ? 1 : 0}\n            strokeOpacity={0.75}\n            fill=\"red\"\n            fillOpacity={isCurrent && debug ? 0.35 : 0}\n            onMouseEnter={handleMouseEnter}\n            onMouseMove={handleMouseMove}\n            onMouseLeave={handleMouseLeave}\n            onMouseDown={handleMouseDown}\n            onMouseUp={handleMouseUp}\n            onClick={handleClick}\n            onDoubleClick={handleDoubleClick}\n            onTouchStart={handeOnTouchStart}\n            onTouchMove={handeOnTouchMove}\n            onTouchEnd={handleOnTouchEnd}\n            data-ref={slice.id}\n        />\n    )\n}\n\nexport const SlicesItem = memo(NonMemoizedSlicesItem) as typeof NonMemoizedSlicesItem\n", "import { memo } from 'react'\nimport { SlicesItem } from './SlicesItem'\nimport { LineSeries, SliceData, CommonLineProps, LineSvgProps } from './types'\n\nexport const NonMemoizedSlices = <Series extends LineSeries>({\n    slices,\n    axis,\n    debug,\n    tooltip,\n    current,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n}: {\n    slices: readonly SliceData<Series>[]\n    axis: Exclude<LineSvgProps<Series>['enableSlices'], undefined | false>\n    debug: boolean\n    tooltip: CommonLineProps<Series>['sliceTooltip']\n    current: SliceData<Series> | null\n    setCurrent: (slice: SliceData<Series> | null) => void\n    onMouseEnter?: CommonLineProps<Series>['onMouseEnter']\n    onMouseMove?: CommonLineProps<Series>['onMouseMove']\n    onMouseLeave?: CommonLineProps<Series>['onMouseLeave']\n    onMouseDown?: CommonLineProps<Series>['onMouseDown']\n    onMouseUp?: CommonLineProps<Series>['onMouseUp']\n    onClick?: CommonLineProps<Series>['onClick']\n    onDoubleClick?: CommonLineProps<Series>['onDoubleClick']\n    onTouchStart?: CommonLineProps<Series>['onTouchStart']\n    onTouchMove?: CommonLineProps<Series>['onTouchMove']\n    onTouchEnd?: CommonLineProps<Series>['onTouchEnd']\n}) => {\n    return (\n        <>\n            {slices.map(slice => (\n                <SlicesItem<Series>\n                    key={slice.id}\n                    slice={slice}\n                    slices={slices}\n                    axis={axis}\n                    debug={debug}\n                    tooltip={tooltip}\n                    setCurrent={setCurrent}\n                    isCurrent={current !== null && current.id === slice.id}\n                    onMouseEnter={onMouseEnter}\n                    onMouseMove={onMouseMove}\n                    onMouseLeave={onMouseLeave}\n                    onMouseDown={onMouseDown}\n                    onMouseUp={onMouseUp}\n                    onClick={onClick}\n                    onDoubleClick={onDoubleClick}\n                    onTouchStart={onTouchStart}\n                    onTouchMove={onTouchMove}\n                    onTouchEnd={onTouchEnd}\n                />\n            ))}\n        </>\n    )\n}\n\nexport const Slices = memo(NonMemoizedSlices) as typeof NonMemoizedSlices\n", "import { createElement, memo } from 'react'\n// @ts-expect-error no types\nimport { getLabelGenerator } from '@nivo/core'\nimport { DotsItem, Margin } from '@nivo/core'\nimport { useTooltip } from '@nivo/tooltip'\nimport { LineSeries, LineSvgPropsWithDefaults, Point } from './types'\n\nconst NonMemoizedPoints = <Series extends LineSeries>({\n    points,\n    symbol,\n    size,\n    borderWidth,\n    enableLabel,\n    label,\n    labelYOffset,\n    isFocusable,\n    setCurrentPoint,\n    tooltip,\n    margin,\n    ariaLabel,\n    ariaLabelledBy,\n    ariaDescribedBy,\n    ariaHidden,\n    ariaDisabled,\n}: {\n    points: readonly Point<Series>[]\n    symbol: LineSvgPropsWithDefaults<Series>['pointSymbol']\n    size: number\n    borderWidth: LineSvgPropsWithDefaults<Series>['pointBorderWidth']\n    enableLabel: LineSvgPropsWithDefaults<Series>['enablePointLabel']\n    label: LineSvgPropsWithDefaults<Series>['pointLabel']\n    labelYOffset: LineSvgPropsWithDefaults<Series>['pointLabelYOffset']\n    isFocusable: LineSvgPropsWithDefaults<Series>['isFocusable']\n    setCurrentPoint: (point: Point<Series> | null) => void\n    tooltip: LineSvgPropsWithDefaults<Series>['tooltip']\n    margin: Margin\n    ariaLabel: LineSvgPropsWithDefaults<Series>['pointAriaLabel']\n    ariaLabelledBy: LineSvgPropsWithDefaults<Series>['pointAriaLabelledBy']\n    ariaDescribedBy: LineSvgPropsWithDefaults<Series>['pointAriaDescribedBy']\n    ariaHidden: LineSvgPropsWithDefaults<Series>['pointAriaHidden']\n    ariaDisabled: LineSvgPropsWithDefaults<Series>['pointAriaDisabled']\n}) => {\n    const getLabel = getLabelGenerator(label)\n\n    const { showTooltipAt, hideTooltip } = useTooltip()\n\n    // We sort the points so that the lower series are drawn on top of the higher ones.\n    const mappedPoints = points\n        .slice(0)\n        .sort((a, b) => a.indexInSeries - b.indexInSeries)\n        .sort((a, b) => b.seriesIndex - a.seriesIndex)\n        .map(point => {\n            return {\n                id: point.id,\n                x: point.x,\n                y: point.y,\n                datum: point.data,\n                fill: point.color,\n                stroke: point.borderColor,\n                label: enableLabel ? getLabel(point) : null,\n                ariaLabel: ariaLabel ? ariaLabel(point) : undefined,\n                ariaLabelledBy: ariaLabelledBy ? ariaLabelledBy(point) : undefined,\n                ariaDescribedBy: ariaDescribedBy ? ariaDescribedBy(point) : undefined,\n                ariaHidden: ariaHidden ? ariaHidden(point) : undefined,\n                ariaDisabled: ariaDisabled ? ariaDisabled(point) : undefined,\n                onFocus: isFocusable\n                    ? () => {\n                          setCurrentPoint(point)\n                          showTooltipAt(\n                              createElement(tooltip, { point }),\n                              [margin.left + point.x, margin.top + point.y],\n                              'top'\n                          )\n                      }\n                    : undefined,\n                onBlur: isFocusable\n                    ? () => {\n                          setCurrentPoint(null)\n                          hideTooltip()\n                      }\n                    : undefined,\n            }\n        })\n\n    return (\n        <g>\n            {mappedPoints.map(point => (\n                <DotsItem\n                    key={point.id}\n                    x={point.x}\n                    y={point.y}\n                    datum={point.datum}\n                    symbol={symbol as any}\n                    size={size}\n                    color={point.fill}\n                    borderWidth={borderWidth}\n                    borderColor={point.stroke}\n                    label={point.label}\n                    labelYOffset={labelYOffset}\n                    ariaLabel={point.ariaLabel}\n                    ariaLabelledBy={point.ariaLabelledBy}\n                    ariaDescribedBy={point.ariaDescribedBy}\n                    ariaHidden={point.ariaHidden}\n                    ariaDisabled={point.ariaDisabled}\n                    isFocusable={isFocusable}\n                    onFocus={point.onFocus}\n                    onBlur={point.onBlur}\n                    testId={`line.point.${point.id}`}\n                />\n            ))}\n        </g>\n    )\n}\n\nexport const Points = memo(NonMemoizedPoints) as typeof NonMemoizedPoints\n", "import { createElement, memo, useCallback, MouseEvent, TouchEvent } from 'react'\nimport { Margin } from '@nivo/core'\nimport { useTooltip } from '@nivo/tooltip'\nimport { Mesh as BaseMesh } from '@nivo/voronoi'\nimport { LineSeries, Point, LineSvgProps, LineSvgPropsWithDefaults } from './types'\n\nconst NonMemoizedMesh = <Series extends LineSeries>({\n    points,\n    width,\n    height,\n    margin,\n    setCurrent,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    onTouchStart,\n    onTouchMove,\n    onTouchEnd,\n    tooltip,\n    debug,\n    enableTouchCrosshair,\n}: {\n    points: Point<Series>[]\n    width: number\n    height: number\n    margin: Margin\n    setCurrent: (point: Point<Series> | null) => void\n    onMouseEnter?: LineSvgProps<Series>['onMouseEnter']\n    onMouseMove?: LineSvgProps<Series>['onMouseMove']\n    onMouseLeave?: LineSvgProps<Series>['onMouseLeave']\n    onMouseDown?: LineSvgProps<Series>['onMouseDown']\n    onMouseUp?: LineSvgProps<Series>['onMouseUp']\n    onClick?: LineSvgProps<Series>['onClick']\n    onDoubleClick?: LineSvgProps<Series>['onDoubleClick']\n    onTouchStart?: LineSvgProps<Series>['onTouchStart']\n    onTouchMove?: LineSvgProps<Series>['onTouchMove']\n    onTouchEnd?: LineSvgProps<Series>['onTouchEnd']\n    tooltip: LineSvgPropsWithDefaults<Series>['tooltip']\n    debug: boolean\n    enableTouchCrosshair: LineSvgPropsWithDefaults<Series>['enableTouchCrosshair']\n}) => {\n    const { showTooltipAt, hideTooltip } = useTooltip()\n\n    const handleMouseEnter = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            showTooltipAt(\n                createElement(tooltip, { point }),\n                [point.x + margin.left, point.y + margin.top],\n                'top'\n            )\n            onMouseEnter?.(point, event)\n        },\n        [showTooltipAt, tooltip, onMouseEnter, margin]\n    )\n\n    const handleMouseMove = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            showTooltipAt(\n                createElement(tooltip, { point }),\n                [point.x + margin.left, point.y + margin.top],\n                'top'\n            )\n            onMouseMove?.(point, event)\n        },\n        [showTooltipAt, tooltip, margin.left, margin.top, onMouseMove]\n    )\n\n    const handleMouseLeave = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            hideTooltip()\n            onMouseLeave?.(point, event)\n        },\n        [hideTooltip, onMouseLeave]\n    )\n\n    const handleMouseDown = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            onMouseDown?.(point, event)\n        },\n        [onMouseDown]\n    )\n\n    const handleMouseUp = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            onMouseUp?.(point, event)\n        },\n        [onMouseUp]\n    )\n\n    const handleClick = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            onClick?.(point, event)\n        },\n        [onClick]\n    )\n\n    const handleDoubleClick = useCallback(\n        (point: Point<Series>, event: MouseEvent) => {\n            onDoubleClick?.(point, event)\n        },\n        [onDoubleClick]\n    )\n\n    const handleTouchStart = useCallback(\n        (point: Point<Series>, event: TouchEvent) => {\n            showTooltipAt(\n                createElement(tooltip, { point }),\n                [point.x + margin.left, point.y + margin.top],\n                'top'\n            )\n            onTouchStart?.(point, event)\n        },\n        [margin.left, margin.top, onTouchStart, showTooltipAt, tooltip]\n    )\n\n    const handleTouchMove = useCallback(\n        (point: Point<Series>, event: TouchEvent) => {\n            showTooltipAt(\n                createElement(tooltip, { point }),\n                [point.x + margin.left, point.y + margin.top],\n                'top'\n            )\n            onTouchMove?.(point, event)\n        },\n        [margin.left, margin.top, onTouchMove, showTooltipAt, tooltip]\n    )\n\n    const handleTouchEnd = useCallback(\n        (point: Point<Series>, event: TouchEvent) => {\n            hideTooltip()\n            onTouchEnd?.(point, event)\n        },\n        [onTouchEnd, hideTooltip]\n    )\n\n    return (\n        <BaseMesh<Point<Series>>\n            nodes={points}\n            width={width}\n            height={height}\n            setCurrent={setCurrent}\n            onMouseEnter={handleMouseEnter}\n            onMouseMove={handleMouseMove}\n            onMouseLeave={handleMouseLeave}\n            onMouseDown={handleMouseDown}\n            onMouseUp={handleMouseUp}\n            onClick={handleClick}\n            onDoubleClick={handleDoubleClick}\n            onTouchStart={handleTouchStart}\n            onTouchMove={handleTouchMove}\n            onTouchEnd={handleTouchEnd}\n            enableTouchCrosshair={enableTouchCrosshair}\n            debug={debug}\n        />\n    )\n}\n\nexport const Mesh = memo(NonMemoizedMesh) as typeof NonMemoizedMesh\n", "import { Fragment, ReactNode, useState, forwardRef, Ref, ReactElement } from 'react'\nimport {\n    // @ts-expect-error no types\n    bindDefs,\n    useDimensions,\n    SvgWrapper,\n    CartesianMarkers,\n    Container,\n    LineCurveFactoryId,\n    WithChartRef,\n} from '@nivo/core'\nimport { InheritedColorConfig, OrdinalColorScaleConfig } from '@nivo/colors'\nimport { Axes, Grid } from '@nivo/axes'\nimport { BoxLegendSvg } from '@nivo/legends'\nimport { Crosshair, CrosshairType } from '@nivo/tooltip'\nimport { AnyScale } from '@nivo/scales'\nimport { useLine } from './hooks'\nimport { Areas } from './Areas'\nimport { Lines } from './Lines'\nimport { Slices } from './Slices'\nimport { Points } from './Points'\nimport { Mesh } from './Mesh'\nimport {\n    LineSeries,\n    InferSeriesId,\n    InferY,\n    LineLayerId,\n    LineSvgProps,\n    Point,\n    PointColorContext,\n    SliceData,\n    LineCustomSvgLayerProps,\n    PointTooltipComponent,\n    SliceTooltipComponent,\n    LineSvgPropsWithDefaults,\n} from './types'\nimport { svgDefaultProps } from './defaults'\n\nfunction InnerLine<Series extends LineSeries>(\n    props: LineSvgProps<Series> & { forwardedRef: Ref<SVGSVGElement> }\n) {\n    const {\n        data,\n        xScale: xScaleSpec = svgDefaultProps.xScale,\n        xFormat,\n        yScale: yScaleSpec = svgDefaultProps.yScale,\n        yFormat,\n        curve = svgDefaultProps.curve as LineCurveFactoryId,\n        margin: partialMargin,\n        width,\n        height,\n        colors = svgDefaultProps.colors as OrdinalColorScaleConfig<Series>,\n        lineWidth = svgDefaultProps.lineWidth as number,\n        layers = svgDefaultProps.layers as readonly LineLayerId[],\n        enableArea = svgDefaultProps.enableArea,\n        areaBaselineValue = svgDefaultProps.areaBaselineValue as InferY<Series>,\n        areaOpacity = svgDefaultProps.areaOpacity,\n        areaBlendMode = svgDefaultProps.areaBlendMode,\n        enablePoints = svgDefaultProps.enablePoints,\n        pointSymbol,\n        pointSize = svgDefaultProps.pointSize,\n        pointColor = svgDefaultProps.pointColor as InheritedColorConfig<PointColorContext<Series>>,\n        pointBorderWidth = svgDefaultProps.pointBorderWidth,\n        pointBorderColor = svgDefaultProps.pointBorderColor as InheritedColorConfig<\n            Omit<Point<Series>, 'borderColor'>\n        >,\n        enablePointLabel = svgDefaultProps.enablePointLabel,\n        pointLabel = svgDefaultProps.pointLabel as string,\n        pointLabelYOffset,\n        enableGridX = svgDefaultProps.enableGridX,\n        gridXValues,\n        enableGridY = svgDefaultProps.enableGridY,\n        gridYValues,\n        axisTop,\n        axisRight,\n        axisBottom = svgDefaultProps.axisBottom,\n        axisLeft = svgDefaultProps.axisLeft,\n        defs = svgDefaultProps.defs,\n        fill = svgDefaultProps.fill,\n        markers,\n        legends = svgDefaultProps.legends,\n        isInteractive = svgDefaultProps.isInteractive,\n        useMesh = svgDefaultProps.useMesh,\n        debugMesh = svgDefaultProps.debugMesh,\n        onMouseEnter,\n        onMouseMove,\n        onMouseLeave,\n        onMouseDown,\n        onMouseUp,\n        onClick,\n        onDoubleClick,\n        onTouchStart,\n        onTouchMove,\n        onTouchEnd,\n        tooltip = svgDefaultProps.tooltip as PointTooltipComponent<Series>,\n        enableSlices = svgDefaultProps.enableSlices,\n        debugSlices = svgDefaultProps.debugSlices,\n        sliceTooltip = svgDefaultProps.sliceTooltip as SliceTooltipComponent<Series>,\n        enableCrosshair = svgDefaultProps.enableCrosshair,\n        crosshairType = svgDefaultProps.crosshairType as CrosshairType,\n        enableTouchCrosshair = svgDefaultProps.enableTouchCrosshair,\n        role = svgDefaultProps.role,\n        ariaLabel,\n        ariaLabelledBy,\n        ariaDescribedBy,\n        isFocusable = svgDefaultProps.isFocusable,\n        pointAriaLabel,\n        pointAriaLabelledBy,\n        pointAriaDescribedBy,\n        pointAriaHidden,\n        pointAriaDisabled,\n        initialHiddenIds = svgDefaultProps.initialHiddenIds as InferSeriesId<Series>[],\n        forwardedRef,\n    } = props\n\n    const { margin, innerWidth, innerHeight, outerWidth, outerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const {\n        legendData,\n        toggleSeries,\n        lineGenerator,\n        areaGenerator,\n        series,\n        xScale,\n        yScale,\n        slices,\n        points,\n    } = useLine<Series>({\n        data,\n        xScale: xScaleSpec,\n        xFormat,\n        yScale: yScaleSpec,\n        yFormat,\n        width: innerWidth,\n        height: innerHeight,\n        colors,\n        curve,\n        areaBaselineValue,\n        pointColor,\n        pointBorderColor,\n        enableSlices,\n        initialHiddenIds,\n    })\n\n    const [currentPoint, setCurrentPoint] = useState<Point<Series> | null>(null)\n    const [currentSlice, setCurrentSlice] = useState<SliceData<Series> | null>(null)\n\n    const layerById: Record<LineLayerId, ReactNode> = {\n        grid: null,\n        markers: null,\n        axes: null,\n        areas: null,\n        crosshair: null,\n        lines: null,\n        points: null,\n        slices: null,\n        mesh: null,\n        legends: null,\n    }\n\n    if (layers.includes('grid') && (enableGridX || enableGridY)) {\n        layerById.grid = (\n            <Grid\n                key=\"grid\"\n                width={innerWidth}\n                height={innerHeight}\n                xScale={enableGridX ? (xScale as AnyScale) : null}\n                yScale={enableGridY ? (yScale as AnyScale) : null}\n                xValues={gridXValues}\n                yValues={gridYValues}\n            />\n        )\n    }\n\n    if (layers.includes('markers') && Array.isArray(markers) && markers.length > 0) {\n        layerById.markers = (\n            <CartesianMarkers\n                key=\"markers\"\n                markers={markers}\n                width={innerWidth}\n                height={innerHeight}\n                xScale={xScale}\n                yScale={yScale}\n            />\n        )\n    }\n\n    if (layers.includes('axes')) {\n        layerById.axes = (\n            <Axes\n                key=\"axes\"\n                xScale={xScale as AnyScale}\n                yScale={yScale as AnyScale}\n                width={innerWidth}\n                height={innerHeight}\n                top={axisTop}\n                right={axisRight}\n                bottom={axisBottom}\n                left={axisLeft}\n            />\n        )\n    }\n\n    if (layers.includes('lines')) {\n        layerById.lines = (\n            <Lines<Series>\n                key=\"lines\"\n                series={series}\n                lineGenerator={lineGenerator}\n                lineWidth={lineWidth}\n            />\n        )\n    }\n\n    if (layers.includes('legends') && legends.length > 0) {\n        layerById.legends = (\n            <Fragment key=\"legends\">\n                {legends.map((legend, i) => (\n                    <BoxLegendSvg\n                        key={i}\n                        {...legend}\n                        containerWidth={innerWidth}\n                        containerHeight={innerHeight}\n                        data={legend.data || legendData}\n                        toggleSerie={\n                            legend.toggleSerie\n                                ? (toggleSeries as (id: string | number) => void)\n                                : undefined\n                        }\n                    />\n                ))}\n            </Fragment>\n        )\n    }\n\n    const boundDefs = bindDefs(defs, series, fill)\n\n    if (enableArea) {\n        layerById.areas = (\n            <Areas<Series>\n                key=\"areas\"\n                areaGenerator={areaGenerator}\n                areaOpacity={areaOpacity}\n                areaBlendMode={areaBlendMode}\n                series={series}\n            />\n        )\n    }\n\n    if (isInteractive && enableSlices !== false) {\n        layerById.slices = (\n            <Slices<Series>\n                key=\"slices\"\n                slices={slices}\n                axis={enableSlices}\n                debug={debugSlices}\n                tooltip={sliceTooltip}\n                current={currentSlice}\n                setCurrent={setCurrentSlice}\n                onMouseEnter={onMouseEnter}\n                onMouseMove={onMouseMove}\n                onMouseLeave={onMouseLeave}\n                onMouseDown={onMouseDown}\n                onMouseUp={onMouseUp}\n                onClick={onClick}\n                onDoubleClick={onDoubleClick}\n                onTouchStart={onTouchStart}\n                onTouchMove={onTouchMove}\n                onTouchEnd={onTouchEnd}\n            />\n        )\n    }\n\n    if (enablePoints) {\n        layerById.points = (\n            <Points<Series>\n                key=\"points\"\n                points={points}\n                symbol={pointSymbol}\n                size={pointSize}\n                borderWidth={pointBorderWidth}\n                enableLabel={enablePointLabel}\n                label={pointLabel}\n                labelYOffset={pointLabelYOffset}\n                isFocusable={isFocusable}\n                setCurrentPoint={setCurrentPoint}\n                tooltip={tooltip}\n                margin={margin}\n                ariaLabel={pointAriaLabel}\n                ariaLabelledBy={pointAriaLabelledBy}\n                ariaDescribedBy={pointAriaDescribedBy}\n                ariaHidden={pointAriaHidden}\n                ariaDisabled={pointAriaDisabled}\n            />\n        )\n    }\n\n    if (isInteractive && enableCrosshair) {\n        if (currentPoint !== null) {\n            layerById.crosshair = (\n                <Crosshair\n                    key=\"crosshair\"\n                    width={innerWidth}\n                    height={innerHeight}\n                    x={currentPoint.x}\n                    y={currentPoint.y}\n                    type={crosshairType}\n                />\n            )\n        }\n        if (currentSlice !== null && enableSlices) {\n            layerById.crosshair = (\n                <Crosshair\n                    key=\"crosshair\"\n                    width={innerWidth}\n                    height={innerHeight}\n                    x={currentSlice.x}\n                    y={currentSlice.y}\n                    type={enableSlices}\n                />\n            )\n        }\n    }\n\n    if (isInteractive && useMesh && enableSlices === false) {\n        layerById.mesh = (\n            <Mesh<Series>\n                key=\"mesh\"\n                points={points}\n                width={innerWidth}\n                height={innerHeight}\n                margin={margin}\n                setCurrent={setCurrentPoint}\n                onMouseEnter={onMouseEnter}\n                onMouseMove={onMouseMove}\n                onMouseLeave={onMouseLeave}\n                onMouseDown={onMouseDown}\n                onMouseUp={onMouseUp}\n                onClick={onClick}\n                onDoubleClick={onDoubleClick}\n                onTouchStart={onTouchStart}\n                onTouchMove={onTouchMove}\n                onTouchEnd={onTouchEnd}\n                tooltip={tooltip}\n                enableTouchCrosshair={enableTouchCrosshair}\n                debug={debugMesh}\n            />\n        )\n    }\n\n    const customLayerProps: LineCustomSvgLayerProps<Series> = {\n        ...(props as LineSvgPropsWithDefaults<Series>),\n        innerWidth,\n        innerHeight,\n        series,\n        slices,\n        points,\n        xScale,\n        yScale,\n        lineGenerator,\n        areaGenerator,\n        currentPoint,\n        setCurrentPoint,\n        currentSlice,\n        setCurrentSlice,\n    }\n\n    return (\n        <SvgWrapper\n            defs={boundDefs}\n            width={outerWidth}\n            height={outerHeight}\n            margin={margin}\n            role={role}\n            ariaLabel={ariaLabel}\n            ariaLabelledBy={ariaLabelledBy}\n            ariaDescribedBy={ariaDescribedBy}\n            isFocusable={isFocusable}\n            ref={forwardedRef}\n        >\n            {layers.map((layer, i) => {\n                if (typeof layer === 'function') {\n                    return <Fragment key={i}>{layer(customLayerProps)}</Fragment>\n                }\n\n                return layerById[layer]\n            })}\n        </SvgWrapper>\n    )\n}\n\nexport const Line = forwardRef(\n    <Series extends LineSeries>(\n        {\n            isInteractive = svgDefaultProps.isInteractive,\n            animate = svgDefaultProps.animate,\n            motionConfig = svgDefaultProps.motionConfig,\n            theme,\n            renderWrapper,\n            ...otherProps\n        }: LineSvgProps<Series>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <Container\n            animate={animate}\n            isInteractive={isInteractive}\n            motionConfig={motionConfig}\n            renderWrapper={renderWrapper}\n            theme={theme}\n        >\n            <InnerLine<Series> isInteractive={isInteractive} {...otherProps} forwardedRef={ref} />\n        </Container>\n    )\n) as <Series extends LineSeries>(\n    props: WithChartRef<LineSvgProps<Series>, SVGSVGElement>\n) => ReactElement\n", "import { forwardRef, Ref, ReactElement } from 'react'\nimport { ResponsiveWrapper, ResponsiveProps, WithChartRef } from '@nivo/core'\nimport { Line } from './Line'\nimport { LineSvgProps, LineSeries } from './types'\n\nexport const ResponsiveLine = forwardRef(\n    <Series extends LineSeries>(\n        {\n            defaultWidth,\n            defaultHeight,\n            onResize,\n            debounceResize,\n            ...props\n        }: ResponsiveProps<LineSvgProps<Series>>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <ResponsiveWrapper\n            defaultWidth={defaultWidth}\n            defaultHeight={defaultHeight}\n            onResize={onResize}\n            debounceResize={debounceResize}\n        >\n            {({ width, height }: { width: number; height: number }) => (\n                <Line<Series> width={width} height={height} {...props} ref={ref} />\n            )}\n        </ResponsiveWrapper>\n    )\n) as <Series extends LineSeries>(\n    props: WithChartRef<ResponsiveProps<LineSvgProps<Series>>, SVGSVGElement>\n) => ReactElement\n", "import {\n    createElement,\n    useRef,\n    useEffect,\n    useState,\n    useCallback,\n    forwardRef,\n    ForwardedRef,\n    MouseEvent,\n    useMemo,\n    ReactElement,\n    Ref,\n} from 'react'\nimport { useDimensions, getRelativeCursor, isCursorInRect, Container, mergeRefs } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { renderAxesToCanvas, renderGridLinesToCanvas } from '@nivo/axes'\nimport { renderLegendToCanvas } from '@nivo/legends'\nimport { useTooltip } from '@nivo/tooltip'\nimport { useVoronoiMesh, renderVoronoiToCanvas, renderVoronoiCellToCanvas } from '@nivo/voronoi'\nimport { OrdinalColorScaleConfig, InheritedColorConfig } from '@nivo/colors'\nimport { useLine } from './hooks'\nimport {\n    InferY,\n    LineCanvasProps,\n    LineLayerId,\n    LineSeries,\n    PointTooltipComponent,\n    Point,\n    LineCustomCanvasLayerProps,\n    PointColorContext,\n} from './types'\nimport { canvasDefaultProps } from './defaults'\nimport { AnyScale } from '@nivo/scales'\n\nconst InnerLineCanvas = <Series extends LineSeries>({\n    width,\n    height,\n    margin: partialMargin,\n    pixelRatio = canvasDefaultProps.pixelRatio,\n    data,\n    xScale: xScaleSpec = canvasDefaultProps.xScale,\n    xFormat,\n    yScale: yScaleSpec = canvasDefaultProps.yScale,\n    yFormat,\n    curve = canvasDefaultProps.curve,\n    layers = canvasDefaultProps.layers as LineLayerId[],\n    colors = canvasDefaultProps.colors as OrdinalColorScaleConfig<Series>,\n    lineWidth = canvasDefaultProps.lineWidth,\n    enableArea = canvasDefaultProps.enableArea,\n    areaBaselineValue = canvasDefaultProps.areaBaselineValue as InferY<Series>,\n    areaOpacity = canvasDefaultProps.areaOpacity,\n    enablePoints = canvasDefaultProps.enablePoints,\n    pointSize = canvasDefaultProps.pointSize,\n    pointColor = canvasDefaultProps.pointColor as InheritedColorConfig<PointColorContext<Series>>,\n    pointBorderWidth = canvasDefaultProps.pointBorderWidth,\n    pointBorderColor = canvasDefaultProps.pointBorderColor as InheritedColorConfig<\n        Omit<Point<Series>, 'borderColor'>\n    >,\n    enableGridX = canvasDefaultProps.enableGridX,\n    gridXValues,\n    enableGridY = canvasDefaultProps.enableGridY,\n    gridYValues,\n    axisTop,\n    axisRight,\n    axisBottom = canvasDefaultProps.axisBottom,\n    axisLeft = canvasDefaultProps.axisLeft,\n    legends = canvasDefaultProps.legends,\n    isInteractive = canvasDefaultProps.isInteractive,\n    debugMesh = canvasDefaultProps.debugMesh,\n    onMouseLeave,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onDoubleClick,\n    tooltip = canvasDefaultProps.tooltip as PointTooltipComponent<Series>,\n    role,\n    forwardedRef,\n}: Omit<LineCanvasProps<Series>, 'renderWrapper' | 'theme'> & {\n    forwardedRef: Ref<HTMLCanvasElement>\n}) => {\n    const canvasEl = useRef<HTMLCanvasElement | null>(null)\n\n    const { margin, innerWidth, innerHeight, outerWidth, outerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n    const theme = useTheme()\n    const [currentPoint, setCurrentPoint] = useState<Point<Series> | null>(null)\n\n    const { lineGenerator, areaGenerator, series, xScale, yScale, points } = useLine<Series>({\n        data,\n        xScale: xScaleSpec,\n        xFormat,\n        yScale: yScaleSpec,\n        yFormat,\n        width: innerWidth,\n        height: innerHeight,\n        colors,\n        curve,\n        areaBaselineValue,\n        pointColor,\n        pointBorderColor,\n    })\n\n    const customLayerProps: LineCustomCanvasLayerProps<Series> = useMemo(\n        () => ({\n            innerWidth,\n            innerHeight,\n            series,\n            points,\n            xScale,\n            yScale,\n            lineWidth,\n            lineGenerator,\n            areaGenerator,\n            currentPoint,\n            setCurrentPoint,\n        }),\n        [\n            innerWidth,\n            innerHeight,\n            series,\n            points,\n            xScale,\n            yScale,\n            lineWidth,\n            lineGenerator,\n            areaGenerator,\n            currentPoint,\n            setCurrentPoint,\n        ]\n    )\n\n    const { delaunay, voronoi } = useVoronoiMesh({\n        points,\n        width: innerWidth,\n        height: innerHeight,\n        debug: debugMesh,\n    })\n\n    useEffect(() => {\n        if (canvasEl.current === null) return\n\n        canvasEl.current.width = outerWidth * pixelRatio\n        canvasEl.current.height = outerHeight * pixelRatio\n\n        const ctx = canvasEl.current.getContext('2d')!\n\n        ctx.scale(pixelRatio, pixelRatio)\n\n        ctx.fillStyle = theme.background\n        ctx.fillRect(0, 0, outerWidth, outerHeight)\n        ctx.translate(margin.left, margin.top)\n\n        layers.forEach(layer => {\n            if (typeof layer === 'function') {\n                layer(ctx, customLayerProps)\n            }\n\n            const gridLineWidth = theme.grid.line.strokeWidth ?? 0\n            if (layer === 'grid' && typeof gridLineWidth !== 'string' && gridLineWidth > 0) {\n                ctx.lineWidth = gridLineWidth\n                ctx.strokeStyle = theme.grid.line.stroke as string\n\n                if (enableGridX) {\n                    renderGridLinesToCanvas(ctx, {\n                        width: innerWidth,\n                        height: innerHeight,\n                        scale: xScale as AnyScale,\n                        axis: 'x',\n                        values: gridXValues,\n                    })\n                }\n\n                if (enableGridY) {\n                    renderGridLinesToCanvas(ctx, {\n                        width: innerWidth,\n                        height: innerHeight,\n                        scale: yScale as AnyScale,\n                        axis: 'y',\n                        values: gridYValues,\n                    })\n                }\n            }\n\n            if (layer === 'axes') {\n                renderAxesToCanvas(ctx, {\n                    xScale: xScale as AnyScale,\n                    yScale: yScale as AnyScale,\n                    width: innerWidth,\n                    height: innerHeight,\n                    top: axisTop,\n                    right: axisRight,\n                    bottom: axisBottom,\n                    left: axisLeft,\n                    theme,\n                })\n            }\n\n            if (layer === 'areas' && enableArea === true) {\n                ctx.save()\n                ctx.globalAlpha = areaOpacity\n\n                areaGenerator.context(ctx)\n                for (let i = series.length - 1; i >= 0; i--) {\n                    ctx.fillStyle = series[i].color\n                    ctx.beginPath()\n                    areaGenerator(series[i].data.map(d => d.position))\n                    ctx.fill()\n                }\n\n                ctx.restore()\n            }\n\n            if (layer === 'lines') {\n                lineGenerator.context(ctx)\n                series.forEach(seriesItem => {\n                    ctx.strokeStyle = seriesItem.color\n                    ctx.lineWidth = lineWidth\n                    ctx.beginPath()\n                    lineGenerator(seriesItem.data.map(d => d.position))\n                    ctx.stroke()\n                })\n            }\n\n            if (layer === 'points' && enablePoints === true && pointSize > 0) {\n                points.forEach(point => {\n                    ctx.fillStyle = point.color\n                    ctx.beginPath()\n                    ctx.arc(point.x, point.y, pointSize / 2, 0, 2 * Math.PI)\n                    ctx.fill()\n\n                    if (pointBorderWidth > 0) {\n                        ctx.strokeStyle = point.borderColor\n                        ctx.lineWidth = pointBorderWidth\n                        ctx.stroke()\n                    }\n                })\n            }\n\n            if (layer === 'mesh' && debugMesh === true && voronoi !== undefined) {\n                renderVoronoiToCanvas(ctx, voronoi)\n                if (currentPoint) {\n                    renderVoronoiCellToCanvas(ctx, voronoi, currentPoint.absIndex)\n                }\n            }\n\n            if (layer === 'legends') {\n                const legendData = series\n                    .map(serie => ({\n                        id: serie.id,\n                        label: serie.id,\n                        color: serie.color,\n                    }))\n                    .reverse()\n\n                legends.forEach(legend => {\n                    renderLegendToCanvas(ctx, {\n                        ...legend,\n                        data: legend.data || legendData,\n                        containerWidth: innerWidth,\n                        containerHeight: innerHeight,\n                        theme,\n                    })\n                })\n            }\n        })\n    }, [\n        canvasEl,\n        innerWidth,\n        outerWidth,\n        innerHeight,\n        outerHeight,\n        margin.left,\n        margin.top,\n        pixelRatio,\n        layers,\n        theme,\n        lineGenerator,\n        series,\n        xScale,\n        yScale,\n        enableGridX,\n        gridXValues,\n        enableGridY,\n        gridYValues,\n        axisTop,\n        axisRight,\n        axisBottom,\n        axisLeft,\n        legends,\n        points,\n        enablePoints,\n        pointSize,\n        pointBorderWidth,\n        currentPoint,\n        customLayerProps,\n        debugMesh,\n        enableArea,\n        areaGenerator,\n        areaOpacity,\n        lineWidth,\n        voronoi,\n    ])\n\n    const getPointFromMouseEvent = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (!canvasEl.current) return null\n\n            const [x, y] = getRelativeCursor(canvasEl.current, event)\n            if (!isCursorInRect(margin.left, margin.top, innerWidth, innerHeight, x, y)) return null\n\n            const pointIndex = delaunay.find(x - margin.left, y - margin.top)\n            return points[pointIndex]\n        },\n        [canvasEl, margin, innerWidth, innerHeight, delaunay, points]\n    )\n\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleMouseHover = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            const point = getPointFromMouseEvent(event)\n            setCurrentPoint(point)\n\n            if (point) {\n                showTooltipFromEvent(createElement(tooltip, { point }), event)\n            } else {\n                hideTooltip()\n            }\n        },\n        [getPointFromMouseEvent, setCurrentPoint, showTooltipFromEvent, hideTooltip, tooltip]\n    )\n\n    const handleMouseLeave = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            hideTooltip()\n            setCurrentPoint(null)\n            if (currentPoint) onMouseLeave?.(currentPoint, event)\n        },\n        [hideTooltip, setCurrentPoint, onMouseLeave, currentPoint]\n    )\n\n    const handleMouseDown = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (onMouseDown) {\n                const point = getPointFromMouseEvent(event)\n                if (point) onMouseDown(point, event)\n            }\n        },\n        [getPointFromMouseEvent, onMouseDown]\n    )\n\n    const handleMouseUp = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (onMouseUp) {\n                const point = getPointFromMouseEvent(event)\n                if (point) onMouseUp(point, event)\n            }\n        },\n        [getPointFromMouseEvent, onMouseUp]\n    )\n\n    const handleClick = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (onClick) {\n                const point = getPointFromMouseEvent(event)\n                if (point) onClick(point, event)\n            }\n        },\n        [getPointFromMouseEvent, onClick]\n    )\n\n    const handleDoubleClick = useCallback(\n        (event: MouseEvent<HTMLCanvasElement>) => {\n            if (onDoubleClick) {\n                const point = getPointFromMouseEvent(event)\n                if (point) onDoubleClick(point, event)\n            }\n        },\n        [getPointFromMouseEvent, onDoubleClick]\n    )\n\n    return (\n        <canvas\n            ref={mergeRefs<HTMLCanvasElement>(canvasEl, forwardedRef)}\n            width={outerWidth * pixelRatio}\n            height={outerHeight * pixelRatio}\n            style={{\n                width: outerWidth,\n                height: outerHeight,\n                cursor: isInteractive ? 'auto' : 'normal',\n            }}\n            onMouseEnter={isInteractive ? handleMouseHover : undefined}\n            onMouseMove={isInteractive ? handleMouseHover : undefined}\n            onMouseLeave={isInteractive ? handleMouseLeave : undefined}\n            onMouseDown={isInteractive ? handleMouseDown : undefined}\n            onMouseUp={isInteractive ? handleMouseUp : undefined}\n            onClick={isInteractive ? handleClick : undefined}\n            onDoubleClick={isInteractive ? handleDoubleClick : undefined}\n            role={role}\n        />\n    )\n}\n\nexport const LineCanvas = forwardRef(\n    <Series extends LineSeries>(\n        { isInteractive, renderWrapper, theme, ...props }: LineCanvasProps<Series>,\n        ref: Ref<HTMLCanvasElement>\n    ) => (\n        <Container {...{ isInteractive, renderWrapper, theme }} animate={false}>\n            <InnerLineCanvas<Series> {...props} forwardedRef={ref} />\n        </Container>\n    )\n) as <Series extends LineSeries>(\n    props: LineCanvasProps<Series> & {\n        ref?: ForwardedRef<HTMLCanvasElement>\n    }\n) => ReactElement\n", "import { forwardRef, Ref, ReactElement } from 'react'\nimport { ResponsiveWrapper, ResponsiveProps, WithChartRef } from '@nivo/core'\nimport { LineCanvasProps, LineSeries } from './types'\nimport { LineCanvas } from './LineCanvas'\n\nexport const ResponsiveLineCanvas = forwardRef(\n    <Series extends LineSeries>(\n        {\n            defaultWidth,\n            defaultHeight,\n            onResize,\n            debounceResize,\n            ...props\n        }: ResponsiveProps<LineCanvasProps<Series>>,\n        ref: Ref<HTMLCanvasElement>\n    ) => (\n        <ResponsiveWrapper\n            defaultWidth={defaultWidth}\n            defaultHeight={defaultWidth}\n            onResize={onResize}\n            debounceResize={debounceResize}\n        >\n            {({ width, height }: { width: number; height: number }) => (\n                <LineCanvas<Series> width={width} height={height} {...props} ref={ref} />\n            )}\n        </ResponsiveWrapper>\n    )\n) as <Series extends LineSeries>(\n    props: WithChartRef<ResponsiveProps<LineCanvasProps<Series>>, HTMLCanvasElement>\n) => ReactElement\n", "import { FunctionComponent, MouseEvent, TouchEvent, AriaAttributes } from 'react'\nimport { Line, Area } from 'd3-shape'\nimport {\n    Dimensions,\n    Box,\n    MotionProps,\n    CssMixBlendMode,\n    ValueFormat,\n    SvgDefsAndFill,\n    CartesianMarkerProps,\n    PropertyAccessor,\n    LineCurveFactoryId,\n    DotsItemSymbolComponent,\n} from '@nivo/core'\nimport { PartialTheme } from '@nivo/theming'\nimport { AxisProps, CanvasAxisProps } from '@nivo/axes'\nimport { InheritedColorConfig, OrdinalColorScaleConfig } from '@nivo/colors'\nimport { ScaleSpec, TicksSpec } from '@nivo/scales'\nimport { LegendProps } from '@nivo/legends'\nimport { CrosshairType } from '@nivo/tooltip'\n\nexport type AllowedSeriesId = string | number\n// `null` is allowed in the data to indicate a missing value.\nexport type AllowedValue = string | number | Date | null\nexport type LineSeries = {\n    id: AllowedSeriesId\n    data: readonly { x: AllowedValue; y: AllowedValue }[]\n}\n\nexport type InferSeriesId<T> = T extends { id: infer Id } ? Id : never\n\nexport type InferX<T> = T extends { data: ReadonlyArray<infer D> }\n    ? D extends { x: infer X }\n        ? X\n        : never\n    : never\nexport type InferY<T> = T extends { data: ReadonlyArray<infer D> }\n    ? D extends { y: infer Y }\n        ? Y\n        : never\n    : never\n\nexport type DefaultSeries = {\n    id: string\n    data: readonly {\n        x: string | null\n        y: number | null\n    }[]\n}\n\nexport interface ComputedDatum<Series extends LineSeries> {\n    data: Series['data'][number]\n    position: {\n        x: number\n        y: number\n    }\n}\n\nexport type ComputedSeries<Series extends LineSeries> = Omit<Series, 'data' | 'id'> & {\n    id: InferSeriesId<Series>\n    data: readonly ComputedDatum<Series>[]\n    color: string\n}\n\nexport interface Point<Series extends LineSeries> {\n    id: string\n    indexInSeries: number\n    absIndex: number\n    seriesIndex: number\n    seriesId: InferSeriesId<Series>\n    seriesColor: string\n    x: number\n    y: number\n    color: string\n    borderColor: string\n    data: Series['data'][number] & {\n        xFormatted: string\n        yFormatted: string\n    }\n}\n\nexport type PointColorContext<Series extends LineSeries> = {\n    series: ComputedSeries<Series>\n    point: Omit<Point<Series>, 'color' | 'borderColor'>\n}\n\nexport interface SliceData<Series extends LineSeries> {\n    id: string\n    x0: number\n    x: number\n    y0: number\n    y: number\n    width: number\n    height: number\n    points: readonly Point<Series>[]\n}\n\nexport type PointOrSliceData<Series extends LineSeries> = Point<Series> | SliceData<Series>\nexport function isPoint<Series extends LineSeries>(\n    data: PointOrSliceData<Series>\n): data is Point<Series> {\n    return (data as Point<Series>).seriesId !== undefined\n}\nexport function isSliceData<Series extends LineSeries>(\n    data: PointOrSliceData<Series>\n): data is SliceData<Series> {\n    return (data as SliceData<Series>).points !== undefined\n}\n\nexport interface DataProps<Series extends LineSeries> {\n    data: readonly Series[]\n}\n\nexport type LineGenerator = Line<{ x: number; y: number }>\nexport type AreaGenerator = Area<{ x: number; y: number }>\n\nexport interface PointTooltipProps<Series extends LineSeries> {\n    point: Point<Series>\n}\nexport type PointTooltipComponent<Series extends LineSeries> = FunctionComponent<\n    PointTooltipProps<Series>\n>\n\nexport interface SliceTooltipProps<Series extends LineSeries> {\n    axis: 'x' | 'y'\n    slice: SliceData<Series>\n}\nexport type SliceTooltipComponent<Series extends LineSeries> = FunctionComponent<\n    SliceTooltipProps<Series>\n>\n\nexport type PointOrSliceMouseHandler<Series extends LineSeries> = (\n    datum: Readonly<Point<Series>> | Readonly<SliceData<Series>>,\n    event: MouseEvent\n) => void\nexport type PointOrSliceTouchHandler<Series extends LineSeries> = (\n    datum: Readonly<Point<Series>> | Readonly<SliceData<Series>>,\n    event: TouchEvent\n) => void\n\nexport type LineLayerId =\n    | 'grid'\n    | 'markers'\n    | 'axes'\n    | 'areas'\n    | 'crosshair'\n    | 'lines'\n    | 'slices'\n    | 'points'\n    | 'mesh'\n    | 'legends'\n\nexport interface CommonCustomLayerProps<Series extends LineSeries> {\n    innerWidth: number\n    innerHeight: number\n    series: readonly ComputedSeries<Series>[]\n    points: readonly Point<Series>[]\n    xScale: (x: InferX<Series>) => number\n    yScale: (y: InferY<Series>) => number\n    lineGenerator: LineGenerator\n    areaGenerator: AreaGenerator\n    currentPoint: Point<Series> | null\n    setCurrentPoint: (point: Point<Series> | null) => void\n}\n\nexport type LineCustomSvgLayerProps<Series extends LineSeries> = Omit<\n    LineSvgPropsWithDefaults<Series>,\n    'xScale' | 'yScale'\n> &\n    CommonCustomLayerProps<Series> & {\n        slices: readonly SliceData<Series>[]\n        currentSlice: SliceData<Series> | null\n        setCurrentSlice: (slice: SliceData<Series> | null) => void\n    }\nexport type LineCustomSvgLayer<Series extends LineSeries> = FunctionComponent<\n    LineCustomSvgLayerProps<Series>\n>\nexport type LineSvgLayer<Series extends LineSeries> = LineLayerId | LineCustomSvgLayer<Series>\n\nexport type LineCustomCanvasLayerProps<Series extends LineSeries> = CommonCustomLayerProps<Series>\nexport type LineCustomCanvasLayer<Series extends LineSeries> = (\n    context: CanvasRenderingContext2D,\n    props: LineCustomCanvasLayerProps<Series>\n) => void\nexport type LineCanvasLayer<Series extends LineSeries> = LineLayerId | LineCustomCanvasLayer<Series>\n\nexport type CommonLineProps<Series extends LineSeries> = {\n    xScale: ScaleSpec\n    xFormat?: ValueFormat<InferX<Series>>\n    yScale: ScaleSpec\n    yFormat?: ValueFormat<InferY<Series>>\n    margin: Box\n    curve: LineCurveFactoryId\n    theme: PartialTheme\n    colors: OrdinalColorScaleConfig<Series>\n    lineWidth: number\n    enablePoints: boolean\n    pointSymbol?: DotsItemSymbolComponent<Point<Series>>\n    pointSize: number\n    pointColor: InheritedColorConfig<PointColorContext<Series>>\n    pointBorderWidth: number\n    pointBorderColor: InheritedColorConfig<Omit<Point<Series>, 'borderColor'>>\n    enableArea: boolean\n    areaBaselineValue: InferY<Series>\n    areaOpacity: number\n    enableGridX: boolean\n    gridXValues?: TicksSpec<InferX<Series>>\n    enableGridY: boolean\n    gridYValues?: TicksSpec<InferY<Series>>\n    legends: readonly LegendProps[]\n    isInteractive: boolean\n    debugMesh: boolean\n    onMouseEnter?: PointOrSliceMouseHandler<Series>\n    onMouseMove?: PointOrSliceMouseHandler<Series>\n    onMouseLeave?: PointOrSliceMouseHandler<Series>\n    onMouseDown?: PointOrSliceMouseHandler<Series>\n    onMouseUp?: PointOrSliceMouseHandler<Series>\n    onClick?: PointOrSliceMouseHandler<Series>\n    onDoubleClick?: PointOrSliceMouseHandler<Series>\n    onTouchStart?: PointOrSliceTouchHandler<Series>\n    onTouchMove?: PointOrSliceTouchHandler<Series>\n    onTouchEnd?: PointOrSliceTouchHandler<Series>\n    tooltip: PointTooltipComponent<Series>\n    sliceTooltip: SliceTooltipComponent<Series>\n    renderWrapper: boolean\n    role?: string\n}\n\nexport interface LineSvgExtraProps<Series extends LineSeries> {\n    layers: readonly LineSvgLayer<Series>[]\n    enablePointLabel: boolean\n    pointLabel: PropertyAccessor<Point<Series>, string>\n    pointLabelYOffset?: number\n    areaBlendMode: CssMixBlendMode\n    axisTop?: AxisProps | null\n    axisRight?: AxisProps | null\n    axisBottom?: AxisProps | null\n    axisLeft?: AxisProps | null\n    useMesh: boolean\n    enableSlices: 'x' | 'y' | false\n    debugSlices: boolean\n    enableCrosshair: boolean\n    crosshairType: CrosshairType\n    enableTouchCrosshair: boolean\n    markers?: readonly CartesianMarkerProps[]\n    initialHiddenIds: readonly InferSeriesId<Series>[]\n    animate: boolean\n    motionConfig: MotionProps['motionConfig']\n    ariaLabel?: AriaAttributes['aria-label']\n    ariaLabelledBy?: AriaAttributes['aria-labelledby']\n    ariaDescribedBy?: AriaAttributes['aria-describedby']\n    isFocusable: boolean\n    pointAriaLabel?: (point: Point<Series>) => AriaAttributes['aria-label']\n    pointAriaLabelledBy?: (point: Point<Series>) => AriaAttributes['aria-labelledby']\n    pointAriaDescribedBy?: (point: Point<Series>) => AriaAttributes['aria-describedby']\n    pointAriaHidden?: (point: Point<Series>) => AriaAttributes['aria-hidden']\n    pointAriaDisabled?: (point: Point<Series>) => AriaAttributes['aria-disabled']\n}\nexport type LineSvgProps<Series extends LineSeries> = DataProps<Series> &\n    Dimensions &\n    Partial<CommonLineProps<Series>> &\n    Partial<LineSvgExtraProps<Series>> &\n    SvgDefsAndFill<any>\nexport type LineSvgPropsWithDefaults<Series extends LineSeries> = DataProps<Series> &\n    Dimensions &\n    CommonLineProps<Series> &\n    LineSvgExtraProps<Series> &\n    SvgDefsAndFill<any>\n\nexport interface LineCanvasExtraProps<Series extends LineSeries> {\n    layers: readonly LineCanvasLayer<Series>[]\n    pixelRatio: number\n    axisTop?: CanvasAxisProps | null\n    axisRight?: CanvasAxisProps | null\n    axisBottom?: CanvasAxisProps | null\n    axisLeft?: CanvasAxisProps | null\n}\nexport type LineCanvasProps<Series extends LineSeries> = DataProps<Series> &\n    Dimensions &\n    Partial<CommonLineProps<Series>> &\n    Partial<LineCanvasExtraProps<Series>>\nexport type LineCanvasPropsWithDefaults<Series extends LineSeries> = DataProps<Series> &\n    Dimensions &\n    CommonLineProps<Series> &\n    LineCanvasExtraProps<Series>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAGf,QAAI,YAAY;AAmBhB,aAAS,SAAS,QAAQ;AACxB,UAAI,KAAK,EAAE;AACX,aAAO,SAAS,MAAM,IAAI;AAAA,IAC5B;AAEA,WAAO,UAAU;AAAA;AAAA;;;;;;;;ACzBjB,uBAAwC;AAKxC,IAAI,iBAAiB;AACrB,SAAS,oBAAoB,MAAM,OAAO;AACxC,MAAI,SAAS,QAAQ,OAAO,UAAU,aAAa,UAAU,GAAI,QAAO;AACxE,MAAI,OAAO,UAAU,YAAY,UAAU,KAAK,CAAC,eAAe,KAAK,IAAI,KAAK,EAAE,iBAAiB,eAAe,IAAI,KAAK,iBAAiB,IAAI;AAC5I,WAAO,QAAQ;AACjB,UAAQ,KAAK,OAAO,KAAK;AAC3B;AACA,IAAI,iBAAiB,CAAC;AACtB,SAAS,oBAAoB,UAAU,OAAO;AAC5C,MAAI,CAAC,SAAS,YAAY,CAAC,SAAS,cAAc;AAChD,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,SAAS,aAAa,YAAY,SAAS,cAAc,SAAS,WAAW,aAAa;AAClH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,IACpC,CAAC,SAAS,mBAAmB,SAAS,aAAa,IAAI,IAAI,OAAO,eAAe,IAAI,MAAM,eAAe,IAAI,IAAI,KAAK;AAAA,MACrH;AAAA;AAAA,MAEA,CAACA,OAAM,MAAMA,GAAE,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,aAAS,cAAc;AAAA,EACzB;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,eAAe,IAAI,GAAG;AAC9B,YAAM,QAAQ,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,iBAAS,MAAM,YAAY,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,iBAAS,MAAM,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,MAAMC,OAAM;AACzB,aAAS,aAAa,MAAM,OAAOA,EAAC,CAAC;AAAA,EACvC,CAAC;AACD,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,eAAe,QAAQ;AACzB,aAAS,aAAa;AAAA,EACxB;AACA,MAAI,YAAY,QAAQ;AACtB,aAAS,aAAa,WAAW,OAAO;AAAA,EAC1C;AACF;AACA,IAAI,mBAAmB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AACA,IAAI,YAAY,CAAC,QAAQ,QAAQ,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AACvF,IAAI,WAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAC1C,mBAAmB,OAAO,KAAK,gBAAgB,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,WAAS,QAAQ,CAAC,WAAW,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AACrE,SAAO;AACT,GAAG,gBAAgB;AAgBnB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,UAAU,CAAC,OAAO,SAAS,GAAG,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,OAAO;AAC7E,IAAI,kBAAkB,CAAC,OAAO,OAAO,GAAG,IAAI,KAAK,IAAI,MAAM,MAAM,CAACC,OAAM,gBAAgBA,IAAG,EAAE,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,KAAK,WAAW,KAAK,MAAM;AACvJ,IAAI,gBAAgB,cAAc,eAAe;AAAA,EAC/C,YAAY,EAAE,GAAAC,IAAG,GAAAC,IAAG,GAAAC,IAAG,GAAG,MAAM,GAAG;AACjC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,CAAC;AACpB,QAAIF,MAAKC,MAAKC,IAAG;AACf,aAAO,KAAK,CAACF,MAAK,GAAGC,MAAK,GAAGC,MAAK,CAAC,CAAC;AACpC,iBAAW,KAAK,CAAC,QAAQ;AAAA,QACvB,eAAe,IAAI,IAAI,CAACH,OAAM,QAAQA,IAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,QAEzD,gBAAgB,KAAK,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AACA,aAAS,OAAO,CAAC,OAAO,QAAQ;AAC9B,UAAI,QAAQ,aAAa;AACvB,eAAO,KAAK,CAAC,SAAS,EAAE,CAAC;AACzB,mBAAW,KAAK,CAAC,cAAc,CAAC,WAAW,cAAc,EAAE,CAAC;AAAA,MAC9D,WAAW,cAAc,KAAK,GAAG,GAAG;AAClC,eAAO,MAAM,GAAG;AAChB,YAAI,GAAG,IAAI,KAAK,EAAG;AACnB,cAAM,OAAO,aAAa,KAAK,GAAG,IAAI,OAAO,cAAc,KAAK,GAAG,IAAI,QAAQ;AAC/E,eAAO,KAAK,QAAQ,KAAK,CAAC;AAC1B,mBAAW;AAAA,UACT,QAAQ,aAAa,CAAC,CAACI,KAAIC,KAAIC,KAAI,GAAG,MAAM;AAAA,YAC1C,YAAYF,GAAE,IAAIC,GAAE,IAAIC,GAAE,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,YAChD,gBAAgB,KAAK,CAAC;AAAA,UACxB,IAAI,CAAC,UAAU;AAAA,YACb,GAAG,GAAG,IAAI,MAAM,IAAI,CAACN,OAAM,QAAQA,IAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACtD,gBAAgB,OAAO,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY,IAAI,eAAe,QAAQ,UAAU;AAAA,IACzD;AACA,UAAM,KAAK;AAAA,EACb;AACF;AACA,IAAI,iBAAiB,cAAc,WAAW;AAAA,EAC5C,YAAY,QAAQ,YAAY;AAC9B,UAAM;AACN,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM;AACJ,WAAO,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA,EACA,OAAO;AACL,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,SAAK,KAAK,QAAQ,CAAC,OAAOD,OAAM;AAC9B,YAAM,OAAO,cAAc,MAAM,CAAC,CAAC;AACnC,YAAM,CAACQ,IAAG,EAAE,IAAI,KAAK,WAAWR,EAAC;AAAA,QAC/B,GAAG,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,aAAa;AAAA,MAC/C;AACA,mBAAa,MAAMQ;AACnB,iBAAW,YAAY;AAAA,IACzB,CAAC;AACD,WAAO,WAAW,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,iBAAiB,OAAO,IAAI;AAAA,QACjE;AAAA,MACF;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,oBAAoB,OAAO,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,uBAAmB,MAAM,KAAK;AAAA,EAChC;AACF;AAGA,IAAI,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,gBAAQ,OAAO;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA;AACF,CAAC;AACD,IAAI,OAAO,WAAW,YAAY;AAAA,EAChC;AAAA,EACA,qBAAqB,CAAC,UAAU,IAAI,cAAc,KAAK;AAAA;AAAA,EAEvD,mBAAmB,CAAC,EAAE,WAAW,YAAY,GAAG,MAAM,MAAM;AAC9D,CAAC;AACD,IAAI,WAAW,KAAK;;;;;;AC1Xb,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,kBAAkB,IAAI,IAAI,WAAW;AAG3C,SAAS,IAAI,MAAMC,IAAG,MAAM,GAAG,GAAG;AACrC,MAAI,GAAG,MAAM,IAAI;AACjB,MAAI,OAAOA,GAAE,CAAC;AACd,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,QAAI;AACJ,WAAOA,GAAE,EAAE,MAAM;AAAA,EACrB,OAAO;AACH,QAAI;AACJ,WAAO,EAAE,EAAE,MAAM;AAAA,EACrB;AACA,MAAI,SAAS;AACb,MAAI,SAAS,QAAQ,SAAS,MAAM;AAChC,QAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,aAAO,OAAO;AACd,WAAK,KAAK,OAAO;AACjB,aAAOA,GAAE,EAAE,MAAM;AAAA,IACrB,OAAO;AACH,aAAO,OAAO;AACd,WAAK,KAAK,OAAO;AACjB,aAAO,EAAE,EAAE,MAAM;AAAA,IACrB;AACA,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AACA,WAAO,SAAS,QAAQ,SAAS,MAAM;AACnC,UAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,eAAO,IAAI;AACX,gBAAQ,OAAO;AACf,aAAK,KAAK,OAAO,UAAU,OAAO;AAClC,eAAOA,GAAE,EAAE,MAAM;AAAA,MACrB,OAAO;AACH,eAAO,IAAI;AACX,gBAAQ,OAAO;AACf,aAAK,KAAK,OAAO,UAAU,OAAO;AAClC,eAAO,EAAE,EAAE,MAAM;AAAA,MACrB;AACA,UAAI;AACJ,UAAI,OAAO,GAAG;AACV,UAAE,QAAQ,IAAI;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB,WAAO,IAAI;AACX,YAAQ,OAAO;AACf,SAAK,KAAK,OAAO,UAAU,OAAO;AAClC,WAAOA,GAAE,EAAE,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB,WAAO,IAAI;AACX,YAAQ,OAAO;AACf,SAAK,KAAK,OAAO,UAAU,OAAO;AAClC,WAAO,EAAE,EAAE,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,MAAI,MAAM,KAAK,WAAW,GAAG;AACzB,MAAE,QAAQ,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AAsDO,SAAS,SAAS,MAAMC,IAAG;AAC9B,MAAI,IAAIA,GAAE,CAAC;AACX,WAASC,KAAI,GAAGA,KAAI,MAAMA,KAAK,MAAKD,GAAEC,EAAC;AACvC,SAAO;AACX;AAEO,SAAS,IAAIC,IAAG;AACnB,SAAO,IAAI,aAAaA,EAAC;AAC7B;;;ACvIA,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,IAAI,KAAK,WAAW,UAAU;AAEpD,IAAM,IAAI,IAAI,CAAC;AACf,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,EAAE;AACjB,IAAM,IAAI,IAAI,EAAE;AAChB,IAAM,IAAI,IAAI,CAAC;AAEf,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AACnD,MAAI,SAAS,SAAS,SAAS;AAC/B,MAAI,OAAO,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAIC;AAE9D,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AAEjB,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AAEP,MAAI,MAAM,SAAS,GAAG,CAAC;AACvB,MAAI,WAAW,eAAe;AAC9B,MAAI,OAAO,YAAY,CAAC,OAAO,UAAU;AACrC,WAAO;AAAA,EACX;AAEA,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AAExC,MAAI,YAAY,KAAK,YAAY,KAAK,YAAY,KAAK,YAAY,GAAG;AAClE,WAAO;AAAA,EACX;AAEA,aAAW,eAAe,SAAS,iBAAiB,KAAK,IAAI,GAAG;AAChE,SAAQ,MAAM,UAAU,MAAM,WAAY,MAAM,UAAU,MAAM;AAChE,MAAI,OAAO,YAAY,CAAC,OAAO,SAAU,QAAO;AAEhD,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE;AAEhC,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,QAAQ,IAAI,OAAO,IAAI,GAAG,GAAG,EAAE;AAErC,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,OAAO,IAAI,OAAO,IAAI,GAAG,GAAG,CAAC;AAEnC,SAAO,EAAE,OAAO,CAAC;AACrB;AAEO,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7C,QAAM,WAAW,KAAK,OAAO,KAAK;AAClC,QAAM,YAAY,KAAK,OAAO,KAAK;AACnC,QAAM,MAAM,UAAU;AAEtB,QAAM,SAAS,KAAK,IAAI,UAAU,QAAQ;AAC1C,MAAI,KAAK,IAAI,GAAG,KAAK,eAAe,OAAQ,QAAO;AAEnD,SAAO,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AACxD;;;ACjLA,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,KAAK,MAAM,WAAW,UAAU;AAEtD,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAMC,KAAI,IAAI,CAAC;AAEf,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAI,OAAO,IAAI,GAAG;;;ACxBlB,IAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,KAAK,MAAM,WAAW,UAAU;AAEtD,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAMC,KAAI,IAAI,CAAC;AACf,IAAM,IAAI,IAAI,CAAC;AACf,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAElB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAIC,OAAM,IAAI,IAAI;AAClB,IAAIC,QAAO,IAAI,IAAI;;;ACnCnB,IAAM,gBAAgB,KAAK,MAAM,WAAW;AAC5C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,KAAK,OAAO,WAAW,UAAU;AAEvD,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAEhB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,QAAQ,IAAI,IAAI;AACtB,IAAM,QAAQ,IAAI,IAAI;AACtB,IAAM,SAAS,IAAI,IAAI;AACvB,IAAM,QAAQ,IAAI,IAAI;AAEtB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,GAAG;AACpB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,OAAO,IAAI,GAAG;AAgVpB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAMC,OAAM,IAAI,IAAI;;;ACpYpB,IAAM,UAAU,KAAK,IAAI,GAAG,GAAG;AAC/B,IAAM,aAAa,IAAI,YAAY,GAAG;AAItC,IAAqB,aAArB,MAAqB,YAAW;AAAA,EAE5B,OAAO,KAAK,QAAQ,OAAO,aAAa,OAAO,aAAa;AACxD,UAAMC,KAAI,OAAO;AACjB,UAAM,SAAS,IAAI,aAAaA,KAAI,CAAC;AAErC,aAASC,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,YAAM,IAAI,OAAOA,EAAC;AAClB,aAAO,IAAIA,EAAC,IAAI,KAAK,CAAC;AACtB,aAAO,IAAIA,KAAI,CAAC,IAAI,KAAK,CAAC;AAAA,IAC9B;AAEA,WAAO,IAAI,YAAW,MAAM;AAAA,EAChC;AAAA,EAEA,YAAY,QAAQ;AAChB,UAAMD,KAAI,OAAO,UAAU;AAC3B,QAAIA,KAAI,KAAK,OAAO,OAAO,CAAC,MAAM,SAAU,OAAM,IAAI,MAAM,qCAAqC;AAEjG,SAAK,SAAS;AAGd,UAAM,eAAe,KAAK,IAAI,IAAIA,KAAI,GAAG,CAAC;AAC1C,SAAK,aAAa,IAAI,YAAY,eAAe,CAAC;AAClD,SAAK,aAAa,IAAI,WAAW,eAAe,CAAC;AAGjD,SAAK,YAAY,KAAK,KAAK,KAAK,KAAKA,EAAC,CAAC;AACvC,SAAK,YAAY,IAAI,YAAYA,EAAC;AAClC,SAAK,YAAY,IAAI,YAAYA,EAAC;AAClC,SAAK,WAAW,IAAI,YAAYA,EAAC;AACjC,SAAK,YAAY,IAAI,WAAW,KAAK,SAAS;AAG9C,SAAK,OAAO,IAAI,YAAYA,EAAC;AAC7B,SAAK,SAAS,IAAI,aAAaA,EAAC;AAEhC,SAAK,OAAO;AAAA,EAChB;AAAA,EAEA,SAAS;AACL,UAAM,EAAC,QAAQ,WAAW,UAAU,WAAW,UAAU,UAAU,SAAS,WAAW,SAAQ,IAAK;AACpG,UAAMA,KAAI,OAAO,UAAU;AAG3B,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,aAASC,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,YAAMC,KAAI,OAAO,IAAID,EAAC;AACtB,YAAME,KAAI,OAAO,IAAIF,KAAI,CAAC;AAC1B,UAAIC,KAAI,KAAM,QAAOA;AACrB,UAAIC,KAAI,KAAM,QAAOA;AACrB,UAAID,KAAI,KAAM,QAAOA;AACrB,UAAIC,KAAI,KAAM,QAAOA;AACrB,WAAK,KAAKF,EAAC,IAAIA;AAAA,IACnB;AACA,UAAM,MAAM,OAAO,QAAQ;AAC3B,UAAM,MAAM,OAAO,QAAQ;AAE3B,QAAI,IAAI,IAAIG;AAGZ,aAASH,KAAI,GAAG,UAAU,UAAUA,KAAID,IAAGC,MAAK;AAC5C,YAAM,IAAI,KAAK,IAAI,IAAI,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC;AACvD,UAAI,IAAI,SAAS;AACb,aAAKA;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,UAAM,MAAM,OAAO,IAAI,EAAE;AACzB,UAAM,MAAM,OAAO,IAAI,KAAK,CAAC;AAG7B,aAASA,KAAI,GAAG,UAAU,UAAUA,KAAID,IAAGC,MAAK;AAC5C,UAAIA,OAAM,GAAI;AACd,YAAM,IAAI,KAAK,KAAK,KAAK,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC;AACzD,UAAI,IAAI,WAAW,IAAI,GAAG;AACtB,aAAKA;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAI,EAAE;AACvB,QAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAE3B,QAAI,YAAY;AAGhB,aAASA,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,UAAIA,OAAM,MAAMA,OAAM,GAAI;AAC1B,YAAMI,KAAI,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,IAAIJ,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC;AAC3E,UAAII,KAAI,WAAW;AACf,QAAAD,MAAKH;AACL,oBAAYI;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAID,GAAE;AACvB,QAAI,MAAM,OAAO,IAAIA,MAAK,CAAC;AAE3B,QAAI,cAAc,UAAU;AAGxB,eAASH,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,aAAK,OAAOA,EAAC,IAAK,OAAO,IAAIA,EAAC,IAAI,OAAO,CAAC,KAAO,OAAO,IAAIA,KAAI,CAAC,IAAI,OAAO,CAAC;AAAA,MACjF;AACA,gBAAU,KAAK,MAAM,KAAK,QAAQ,GAAGD,KAAI,CAAC;AAC1C,YAAM,OAAO,IAAI,YAAYA,EAAC;AAC9B,UAAIM,KAAI;AACR,eAASL,KAAI,GAAG,KAAK,WAAWA,KAAID,IAAGC,MAAK;AACxC,cAAM,KAAK,KAAK,KAAKA,EAAC;AACtB,cAAM,IAAI,KAAK,OAAO,EAAE;AACxB,YAAI,IAAI,IAAI;AACR,eAAKK,IAAG,IAAI;AACZ,eAAK;AAAA,QACT;AAAA,MACJ;AACA,WAAK,OAAO,KAAK,SAAS,GAAGA,EAAC;AAC9B,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC;AAAA,IACJ;AAGA,QAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG;AAC5C,YAAML,KAAI;AACV,YAAMC,KAAI;AACV,YAAMC,KAAI;AACV,WAAKC;AACL,YAAM;AACN,YAAM;AACN,MAAAA,MAAKH;AACL,YAAMC;AACN,YAAMC;AAAA,IACV;AAEA,UAAM,SAAS,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxD,SAAK,MAAM,OAAO;AAClB,SAAK,MAAM,OAAO;AAElB,aAASF,KAAI,GAAGA,KAAID,IAAGC,MAAK;AACxB,WAAK,OAAOA,EAAC,IAAI,KAAK,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,IAC9E;AAGA,cAAU,KAAK,MAAM,KAAK,QAAQ,GAAGD,KAAI,CAAC;AAG1C,SAAK,aAAa;AAClB,QAAI,WAAW;AAEf,aAAS,EAAE,IAAI,SAASI,GAAE,IAAI;AAC9B,aAAS,EAAE,IAAI,SAAS,EAAE,IAAIA;AAC9B,aAASA,GAAE,IAAI,SAAS,EAAE,IAAI;AAE9B,YAAQ,EAAE,IAAI;AACd,YAAQ,EAAE,IAAI;AACd,YAAQA,GAAE,IAAI;AAEd,aAAS,KAAK,EAAE;AAChB,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAIA;AAEpC,SAAK,eAAe;AACpB,SAAK,aAAa,IAAI,IAAIA,KAAI,IAAI,IAAI,EAAE;AAExC,aAASG,KAAI,GAAG,IAAI,IAAIA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAC/C,YAAMN,KAAI,KAAK,KAAKM,EAAC;AACrB,YAAML,KAAI,OAAO,IAAID,EAAC;AACtB,YAAME,KAAI,OAAO,IAAIF,KAAI,CAAC;AAG1B,UAAIM,KAAI,KAAK,KAAK,IAAIL,KAAI,EAAE,KAAK,WAAW,KAAK,IAAIC,KAAI,EAAE,KAAK,QAAS;AACzE,WAAKD;AACL,WAAKC;AAGL,UAAIF,OAAM,MAAMA,OAAM,MAAMA,OAAMG,IAAI;AAGtC,UAAI,QAAQ;AACZ,eAASE,KAAI,GAAG,MAAM,KAAK,SAASJ,IAAGC,EAAC,GAAGG,KAAI,KAAK,WAAWA,MAAK;AAChE,gBAAQ,UAAU,MAAMA,MAAK,KAAK,SAAS;AAC3C,YAAI,UAAU,MAAM,UAAU,SAAS,KAAK,EAAG;AAAA,MACnD;AAEA,cAAQ,SAAS,KAAK;AACtB,UAAIE,KAAI,OAAOC;AACf,aAAOA,KAAI,SAASD,EAAC,GAAG,SAASN,IAAGC,IAAG,OAAO,IAAIK,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAIC,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC,KAAK,GAAG;AAC7G,QAAAD,KAAIC;AACJ,YAAID,OAAM,OAAO;AACb,UAAAA,KAAI;AACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAIA,OAAM,GAAI;AAGd,UAAIE,KAAI,KAAK,aAAaF,IAAGP,IAAG,SAASO,EAAC,GAAG,IAAI,IAAI,QAAQA,EAAC,CAAC;AAG/D,cAAQP,EAAC,IAAI,KAAK,UAAUS,KAAI,CAAC;AACjC,cAAQF,EAAC,IAAIE;AACb;AAGA,UAAIV,KAAI,SAASQ,EAAC;AAClB,aAAOC,KAAI,SAAST,EAAC,GAAG,SAASE,IAAGC,IAAG,OAAO,IAAIH,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAIS,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC,IAAI,GAAG;AAC5G,QAAAC,KAAI,KAAK,aAAaV,IAAGC,IAAGQ,IAAG,QAAQR,EAAC,GAAG,IAAI,QAAQD,EAAC,CAAC;AACzD,gBAAQC,EAAC,IAAI,KAAK,UAAUS,KAAI,CAAC;AACjC,iBAASV,EAAC,IAAIA;AACd;AACA,QAAAA,KAAIS;AAAA,MACR;AAGA,UAAID,OAAM,OAAO;AACb,eAAOC,KAAI,SAASD,EAAC,GAAG,SAASN,IAAGC,IAAG,OAAO,IAAIM,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAID,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC,IAAI,GAAG;AAC5G,UAAAE,KAAI,KAAK,aAAaD,IAAGR,IAAGO,IAAG,IAAI,QAAQA,EAAC,GAAG,QAAQC,EAAC,CAAC;AACzD,eAAK,UAAUC,KAAI,CAAC;AACpB,kBAAQD,EAAC,IAAIC;AACb,mBAASF,EAAC,IAAIA;AACd;AACA,UAAAA,KAAIC;AAAA,QACR;AAAA,MACJ;AAGA,WAAK,aAAa,SAASR,EAAC,IAAIO;AAChC,eAASA,EAAC,IAAI,SAASR,EAAC,IAAIC;AAC5B,eAASA,EAAC,IAAID;AAGd,eAAS,KAAK,SAASE,IAAGC,EAAC,CAAC,IAAIF;AAChC,eAAS,KAAK,SAAS,OAAO,IAAIO,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,CAAC,CAAC,IAAIA;AAAA,IAChE;AAEA,SAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,aAASP,KAAI,GAAGO,KAAI,KAAK,YAAYP,KAAI,UAAUA,MAAK;AACpD,WAAK,KAAKA,EAAC,IAAIO;AACf,MAAAA,KAAI,SAASA,EAAC;AAAA,IAClB;AAGA,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAC9D,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAAA,EAClE;AAAA,EAEA,SAASN,IAAGC,IAAG;AACX,WAAO,KAAK,MAAM,YAAYD,KAAI,KAAK,KAAKC,KAAI,KAAK,GAAG,IAAI,KAAK,SAAS,IAAI,KAAK;AAAA,EACvF;AAAA,EAEA,UAAUQ,IAAG;AACT,UAAM,EAAC,YAAY,WAAW,YAAY,WAAW,OAAM,IAAI;AAE/D,QAAIV,KAAI;AACR,QAAI,KAAK;AAGT,WAAO,MAAM;AACT,YAAMW,KAAI,UAAUD,EAAC;AAiBrB,YAAM,KAAKA,KAAIA,KAAI;AACnB,WAAK,MAAMA,KAAI,KAAK;AAEpB,UAAIC,OAAM,IAAI;AACV,YAAIX,OAAM,EAAG;AACb,QAAAU,KAAI,WAAW,EAAEV,EAAC;AAClB;AAAA,MACJ;AAEA,YAAM,KAAKW,KAAIA,KAAI;AACnB,YAAM,KAAK,MAAMD,KAAI,KAAK;AAC1B,YAAM,KAAK,MAAMC,KAAI,KAAK;AAE1B,YAAM,KAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAUD,EAAC;AACtB,YAAM,KAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAU,EAAE;AAEvB,YAAM,UAAU;AAAA,QACZ,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,MAAC;AAEtC,UAAI,SAAS;AACT,kBAAUA,EAAC,IAAI;AACf,kBAAUC,EAAC,IAAI;AAEf,cAAM,MAAM,UAAU,EAAE;AAGxB,YAAI,QAAQ,IAAI;AACZ,cAAIJ,KAAI,KAAK;AACb,aAAG;AACC,gBAAI,KAAK,SAASA,EAAC,MAAM,IAAI;AACzB,mBAAK,SAASA,EAAC,IAAIG;AACnB;AAAA,YACJ;AACA,YAAAH,KAAI,KAAK,UAAUA,EAAC;AAAA,UACxB,SAASA,OAAM,KAAK;AAAA,QACxB;AACA,aAAK,MAAMG,IAAG,GAAG;AACjB,aAAK,MAAMC,IAAG,UAAU,EAAE,CAAC;AAC3B,aAAK,MAAM,IAAI,EAAE;AAEjB,cAAM,KAAK,MAAMA,KAAI,KAAK;AAG1B,YAAIX,KAAI,WAAW,QAAQ;AACvB,qBAAWA,IAAG,IAAI;AAAA,QACtB;AAAA,MACJ,OAAO;AACH,YAAIA,OAAM,EAAG;AACb,QAAAU,KAAI,WAAW,EAAEV,EAAC;AAAA,MACtB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAMU,IAAGC,IAAG;AACR,SAAK,WAAWD,EAAC,IAAIC;AACrB,QAAIA,OAAM,GAAI,MAAK,WAAWA,EAAC,IAAID;AAAA,EACvC;AAAA;AAAA,EAGA,aAAa,IAAI,IAAIP,KAAIO,IAAGC,IAAG,GAAG;AAC9B,UAAMF,KAAI,KAAK;AAEf,SAAK,WAAWA,EAAC,IAAI;AACrB,SAAK,WAAWA,KAAI,CAAC,IAAI;AACzB,SAAK,WAAWA,KAAI,CAAC,IAAIN;AAEzB,SAAK,MAAMM,IAAGC,EAAC;AACf,SAAK,MAAMD,KAAI,GAAGE,EAAC;AACnB,SAAK,MAAMF,KAAI,GAAG,CAAC;AAEnB,SAAK,gBAAgB;AAErB,WAAOA;AAAA,EACX;AACJ;AAGA,SAAS,YAAY,IAAI,IAAI;AACzB,QAAM,IAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC1C,UAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACtC;AAEA,SAAS,KAAK,IAAI,IAAI,IAAI,IAAI;AAC1B,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AAC1B;AAEA,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC9C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAE1B,SAAO,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MAAM;AACtC;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAMR,MAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAMC,MAAK,KAAK,KAAK,KAAK,MAAM;AAEhC,SAAOD,KAAIA,KAAIC,KAAIA;AACvB;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAMD,KAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AACrC,QAAMC,KAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AAErC,SAAO,EAAC,GAAAD,IAAG,GAAAC,GAAC;AAChB;AAEA,SAAS,UAAU,KAAK,OAAO,MAAM,OAAO;AACxC,MAAI,QAAQ,QAAQ,IAAI;AACpB,aAASF,KAAI,OAAO,GAAGA,MAAK,OAAOA,MAAK;AACpC,YAAM,OAAO,IAAIA,EAAC;AAClB,YAAM,WAAW,MAAM,IAAI;AAC3B,UAAIK,KAAIL,KAAI;AACZ,aAAOK,MAAK,QAAQ,MAAM,IAAIA,EAAC,CAAC,IAAI,SAAU,KAAIA,KAAI,CAAC,IAAI,IAAIA,IAAG;AAClE,UAAIA,KAAI,CAAC,IAAI;AAAA,IACjB;AAAA,EACJ,OAAO;AACH,UAAM,SAAU,OAAO,SAAU;AACjC,QAAIL,KAAI,OAAO;AACf,QAAIK,KAAI;AACR,SAAK,KAAK,QAAQL,EAAC;AACnB,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,EAAG,MAAK,KAAK,MAAM,KAAK;AAC/D,QAAI,MAAM,IAAIA,EAAC,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,EAAG,MAAK,KAAKA,IAAG,KAAK;AACzD,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAIA,EAAC,CAAC,EAAG,MAAK,KAAK,MAAMA,EAAC;AAEvD,UAAM,OAAO,IAAIA,EAAC;AAClB,UAAM,WAAW,MAAM,IAAI;AAC3B,WAAO,MAAM;AACT;AAAG,QAAAA;AAAA,aAAY,MAAM,IAAIA,EAAC,CAAC,IAAI;AAC/B;AAAG,QAAAK;AAAA,aAAY,MAAM,IAAIA,EAAC,CAAC,IAAI;AAC/B,UAAIA,KAAIL,GAAG;AACX,WAAK,KAAKA,IAAGK,EAAC;AAAA,IAClB;AACA,QAAI,OAAO,CAAC,IAAI,IAAIA,EAAC;AACrB,QAAIA,EAAC,IAAI;AAET,QAAI,QAAQL,KAAI,KAAKK,KAAI,MAAM;AAC3B,gBAAU,KAAK,OAAOL,IAAG,KAAK;AAC9B,gBAAU,KAAK,OAAO,MAAMK,KAAI,CAAC;AAAA,IACrC,OAAO;AACH,gBAAU,KAAK,OAAO,MAAMA,KAAI,CAAC;AACjC,gBAAU,KAAK,OAAOL,IAAG,KAAK;AAAA,IAClC;AAAA,EACJ;AACJ;AAEA,SAAS,KAAK,KAAKA,IAAGK,IAAG;AACrB,QAAM,MAAM,IAAIL,EAAC;AACjB,MAAIA,EAAC,IAAI,IAAIK,EAAC;AACd,MAAIA,EAAC,IAAI;AACb;AAEA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;;;AC/dA,IAAMO,WAAU;AAEhB,IAAqB,OAArB,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,MAAM,KAAK;AAAA,IAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,OAAOC,IAAGC,IAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAACC,EAAC;AAAA,EACpE;AAAA,EACA,YAAY;AACV,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAOD,IAAGC,IAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,CAACD,EAAC,IAAI,KAAK,MAAM,CAACC,EAAC;AAAA,EAC9C;AAAA,EACA,IAAID,IAAGC,IAAGC,IAAG;AACX,IAAAF,KAAI,CAACA,IAAGC,KAAI,CAACA,IAAGC,KAAI,CAACA;AACrB,UAAM,KAAKF,KAAIE;AACf,UAAM,KAAKD;AACX,QAAIC,KAAI,EAAG,OAAM,IAAI,MAAM,iBAAiB;AAC5C,QAAI,KAAK,QAAQ,KAAM,MAAK,KAAK,IAAI,EAAE,IAAI,EAAE;AAAA,aACpC,KAAK,IAAI,KAAK,MAAM,EAAE,IAAIH,YAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAIA,SAAS,MAAK,KAAK,MAAM,KAAK,MAAM;AAC5G,QAAI,CAACG,GAAG;AACR,SAAK,KAAK,IAAIA,EAAC,IAAIA,EAAC,UAAUF,KAAIE,EAAC,IAAID,EAAC,IAAIC,EAAC,IAAIA,EAAC,UAAU,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,EAC5F;AAAA,EACA,KAAKF,IAAGC,IAAGE,IAAG,GAAG;AACf,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAACH,EAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAACC,EAAC,IAAI,CAACE,EAAC,IAAI,CAAC,CAAC,IAAI,CAACA,EAAC;AAAA,EACtF;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;;;ACpCA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,cAAc;AACZ,SAAK,IAAI,CAAC;AAAA,EACZ;AAAA,EACA,OAAOC,IAAGC,IAAG;AACX,SAAK,EAAE,KAAK,CAACD,IAAGC,EAAC,CAAC;AAAA,EACpB;AAAA,EACA,YAAY;AACV,SAAK,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,EAC/B;AAAA,EACA,OAAOD,IAAGC,IAAG;AACX,SAAK,EAAE,KAAK,CAACD,IAAGC,EAAC,CAAC;AAAA,EACpB;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,EAClC;AACF;;;ACbA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,YAAY,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG;AACjE,QAAI,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,OAAQ,OAAM,IAAI,MAAM,gBAAgB;AAChH,SAAK,WAAW;AAChB,SAAK,iBAAiB,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AACjE,SAAK,UAAU,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AAC1D,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,SAAS,OAAO;AACrB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,EAAC,UAAU,EAAC,QAAQ,MAAM,UAAS,GAAG,QAAO,IAAI;AACvD,QAAI,IAAI;AAGR,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,eAAe,SAAS,GAAG,UAAU,SAAS,IAAI,CAAC;AACnG,aAASC,KAAI,GAAGC,KAAI,GAAGC,KAAI,UAAU,QAAQC,IAAGC,IAAGJ,KAAIE,IAAGF,MAAK,GAAGC,MAAK,GAAG;AACxE,YAAM,KAAK,UAAUD,EAAC,IAAI;AAC1B,YAAMK,MAAK,UAAUL,KAAI,CAAC,IAAI;AAC9B,YAAM,KAAK,UAAUA,KAAI,CAAC,IAAI;AAC9B,YAAMM,MAAK,OAAO,EAAE;AACpB,YAAMC,MAAK,OAAO,KAAK,CAAC;AACxB,YAAMC,MAAK,OAAOH,GAAE;AACpB,YAAMI,MAAK,OAAOJ,MAAK,CAAC;AACxB,YAAM,KAAK,OAAO,EAAE;AACpB,YAAM,KAAK,OAAO,KAAK,CAAC;AAExB,YAAM,KAAKG,MAAKF;AAChB,YAAM,KAAKG,MAAKF;AAChB,YAAM,KAAK,KAAKD;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAMG,OAAM,KAAK,KAAK,KAAK,MAAM;AAEjC,UAAI,KAAK,IAAIA,GAAE,IAAI,MAAM;AAIvB,YAAI,OAAO,QAAW;AACpB,eAAK,KAAK;AACV,qBAAWV,MAAK,KAAM,OAAM,OAAOA,KAAI,CAAC,GAAG,MAAM,OAAOA,KAAI,IAAI,CAAC;AACjE,gBAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,QAChC;AACA,cAAMW,KAAI,MAAM,KAAK,MAAM,KAAKL,OAAM,MAAM,KAAKC,OAAM,EAAE;AACzD,QAAAJ,MAAKG,MAAK,MAAM,IAAIK,KAAI;AACxB,QAAAP,MAAKG,MAAK,MAAM,IAAII,KAAI;AAAA,MAC1B,OAAO;AACL,cAAM,IAAI,IAAID;AACd,cAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,cAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAAP,KAAIG,OAAM,KAAK,KAAK,KAAK,MAAM;AAC/B,QAAAF,KAAIG,OAAM,KAAK,KAAK,KAAK,MAAM;AAAA,MACjC;AACA,oBAAcN,EAAC,IAAIE;AACnB,oBAAcF,KAAI,CAAC,IAAIG;AAAA,IACzB;AAGA,QAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,OAAO,IAAI,CAAC;AACzB,QAAI,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC;AAC7B,YAAQ,KAAK,CAAC;AACd,aAASJ,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AACpC,UAAI,KAAKA,EAAC;AACV,WAAK,IAAI,KAAK,IAAI,KAAK;AACvB,WAAK,IAAI,GAAG,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,IAAI,CAAC;AACrD,cAAQ,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,KAAK;AACrC,cAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,UAAU,EAAC,WAAW,SAAS,KAAI,GAAG,eAAe,QAAO,IAAI;AACvE,QAAI,KAAK,UAAU,EAAG,QAAO;AAC7B,aAASA,KAAI,GAAGE,KAAI,UAAU,QAAQF,KAAIE,IAAG,EAAEF,IAAG;AAChD,YAAMC,KAAI,UAAUD,EAAC;AACrB,UAAIC,KAAID,GAAG;AACX,YAAM,KAAK,KAAK,MAAMA,KAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,KAAK,MAAMC,KAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,WAAK,eAAe,IAAI,IAAI,IAAI,IAAI,OAAO;AAAA,IAC7C;AACA,QAAI,IAAI,KAAK,KAAK,KAAK,SAAS,CAAC;AACjC,aAASD,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AACpC,WAAK,IAAI,KAAK,KAAKA,EAAC;AACpB,YAAMY,KAAI,KAAK,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACxC,YAAMT,KAAI,cAAcS,EAAC;AACzB,YAAMR,KAAI,cAAcQ,KAAI,CAAC;AAC7B,YAAMC,KAAI,KAAK;AACf,YAAM,IAAI,KAAK,SAASV,IAAGC,IAAG,QAAQS,KAAI,CAAC,GAAG,QAAQA,KAAI,CAAC,CAAC;AAC5D,UAAI,EAAG,MAAK,eAAeV,IAAGC,IAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;AAAA,IACtD;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,YAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI;AAC/E,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAWJ,IAAG,SAAS;AACrB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,SAAS,KAAK,MAAMA,EAAC;AAC3B,QAAI,WAAW,QAAQ,CAAC,OAAO,OAAQ;AACvC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACnC,QAAIE,KAAI,OAAO;AACf,WAAO,OAAO,CAAC,MAAM,OAAOA,KAAE,CAAC,KAAK,OAAO,CAAC,MAAM,OAAOA,KAAE,CAAC,KAAKA,KAAI,EAAG,CAAAA,MAAK;AAC7E,aAASF,KAAI,GAAGA,KAAIE,IAAGF,MAAK,GAAG;AAC7B,UAAI,OAAOA,EAAC,MAAM,OAAOA,KAAE,CAAC,KAAK,OAAOA,KAAE,CAAC,MAAM,OAAOA,KAAE,CAAC;AACzD,gBAAQ,OAAO,OAAOA,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IAC3C;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,eAAe;AACd,UAAM,EAAC,UAAU,EAAC,OAAM,EAAC,IAAI;AAC7B,aAASA,KAAI,GAAGE,KAAI,OAAO,SAAS,GAAGF,KAAIE,IAAG,EAAEF,IAAG;AACjD,YAAM,OAAO,KAAK,YAAYA,EAAC;AAC/B,UAAI,KAAM,MAAK,QAAQA,IAAG,MAAM;AAAA,IAClC;AAAA,EACF;AAAA,EACA,YAAYA,IAAG;AACb,UAAM,UAAU,IAAI;AACpB,SAAK,WAAWA,IAAG,OAAO;AAC1B,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAe,IAAI,IAAI,IAAI,IAAI,SAAS;AACtC,QAAIc;AACJ,UAAM,KAAK,KAAK,YAAY,IAAI,EAAE;AAClC,UAAM,KAAK,KAAK,YAAY,IAAI,EAAE;AAClC,QAAI,OAAO,KAAK,OAAO,GAAG;AACxB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AAAA,IACvB,WAAWA,KAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AACxD,cAAQ,OAAOA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACzB,cAAQ,OAAOA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,SAASd,IAAGG,IAAGC,IAAG;AAChB,SAAKD,KAAI,CAACA,IAAGA,OAAMA,QAAOC,KAAI,CAACA,IAAGA,OAAMA,IAAI,QAAO;AACnD,WAAO,KAAK,SAAS,MAAMJ,IAAGG,IAAGC,EAAC,MAAMJ;AAAA,EAC1C;AAAA,EACA,CAAC,UAAUA,IAAG;AACZ,UAAM,KAAK,KAAK,MAAMA,EAAC;AACvB,QAAI,GAAI,YAAWC,MAAK,KAAK,SAAS,UAAUD,EAAC,GAAG;AAClD,YAAM,KAAK,KAAK,MAAMC,EAAC;AAEvB,UAAI,GAAI,MAAM,UAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AAC/D,iBAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AACjD,cAAI,GAAG,EAAE,MAAM,GAAG,EAAE,KACb,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KACxB,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,KAC3C,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,GAAG;AACnD,kBAAMA;AACN,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAMD,IAAG;AACP,UAAM,EAAC,eAAe,UAAU,EAAC,SAAS,WAAW,UAAS,EAAC,IAAI;AACnE,UAAM,KAAK,QAAQA,EAAC;AACpB,QAAI,OAAO,GAAI,QAAO;AACtB,UAAM,SAAS,CAAC;AAChB,QAAIe,KAAI;AACR,OAAG;AACD,YAAMH,KAAI,KAAK,MAAMG,KAAI,CAAC;AAC1B,aAAO,KAAK,cAAcH,KAAI,CAAC,GAAG,cAAcA,KAAI,IAAI,CAAC,CAAC;AAC1D,MAAAG,KAAIA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI;AAC9B,UAAI,UAAUA,EAAC,MAAMf,GAAG;AACxB,MAAAe,KAAI,UAAUA,EAAC;AAAA,IACjB,SAASA,OAAM,MAAMA,OAAM;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,MAAMf,IAAG;AAEP,QAAIA,OAAM,KAAK,KAAK,SAAS,KAAK,WAAW,GAAG;AAC9C,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,UAAM,SAAS,KAAK,MAAMA,EAAC;AAC3B,QAAI,WAAW,KAAM,QAAO;AAC5B,UAAM,EAAC,SAASgB,GAAC,IAAI;AACrB,UAAMH,KAAIb,KAAI;AACd,WAAO,KAAK,UAAUgB,GAAEH,EAAC,KAAKG,GAAEH,KAAI,CAAC,IAC/B,KAAK,cAAcb,IAAG,QAAQgB,GAAEH,EAAC,GAAGG,GAAEH,KAAI,CAAC,GAAGG,GAAEH,KAAI,CAAC,GAAGG,GAAEH,KAAI,CAAC,CAAC,IAChE,KAAK,YAAYb,IAAG,MAAM,CAAC;AAAA,EACnC;AAAA,EACA,YAAYA,IAAG,QAAQ;AACrB,UAAME,KAAI,OAAO;AACjB,QAAIe,KAAI;AACR,QAAI,IAAI,IAAI,KAAK,OAAOf,KAAI,CAAC,GAAG,KAAK,OAAOA,KAAI,CAAC;AACjD,QAAI,IAAI,KAAK,KAAK,YAAY,IAAI,EAAE;AACpC,QAAI,IAAI,KAAK;AACb,aAASD,KAAI,GAAGA,KAAIC,IAAGD,MAAK,GAAG;AAC7B,WAAK,IAAI,KAAK,IAAI,KAAK,OAAOA,EAAC,GAAG,KAAK,OAAOA,KAAI,CAAC;AACnD,WAAK,IAAI,KAAK,KAAK,YAAY,IAAI,EAAE;AACrC,UAAI,OAAO,KAAK,OAAO,GAAG;AACxB,aAAK,IAAI,KAAK;AACd,YAAIgB,GAAG,CAAAA,GAAE,KAAK,IAAI,EAAE;AAAA,YACf,CAAAA,KAAI,CAAC,IAAI,EAAE;AAAA,MAClB,OAAO;AACL,YAAIH,IAAG,KAAK,KAAK,KAAK;AACtB,YAAI,OAAO,GAAG;AACZ,eAAKA,KAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,OAAO,KAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAIA;AAAA,QACzB,OAAO;AACL,eAAKA,KAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,OAAO,KAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAIA;AACvB,eAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,cAAI,MAAM,GAAI,MAAK,MAAMd,IAAG,IAAI,IAAIiB,IAAGA,GAAE,MAAM;AAC/C,cAAIA,GAAG,CAAAA,GAAE,KAAK,KAAK,GAAG;AAAA,cACjB,CAAAA,KAAI,CAAC,KAAK,GAAG;AAAA,QACpB;AACA,aAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,YAAI,MAAM,GAAI,MAAK,MAAMjB,IAAG,IAAI,IAAIiB,IAAGA,GAAE,MAAM;AAC/C,YAAIA,GAAG,CAAAA,GAAE,KAAK,KAAK,GAAG;AAAA,YACjB,CAAAA,KAAI,CAAC,KAAK,GAAG;AAAA,MACpB;AAAA,IACF;AACA,QAAIA,IAAG;AACL,WAAK,IAAI,KAAK,KAAK,UAAUA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACvC,UAAI,MAAM,GAAI,MAAK,MAAMjB,IAAG,IAAI,IAAIiB,IAAGA,GAAE,MAAM;AAAA,IACjD,WAAW,KAAK,SAASjB,KAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,WAAOiB;AAAA,EACT;AAAA,EACA,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAEnC,UAAM,OAAO,KAAK;AAClB,QAAI,KAAM,EAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC5D,WAAO,MAAM;AACX,UAAI,OAAO,KAAK,OAAO,EAAG,QAAO,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;AAC1E,UAAI,KAAK,GAAI,QAAO;AACpB,UAAId,IAAGC,IAAG,IAAI,MAAM;AACpB,UAAI,IAAI,EAAQ,CAAAD,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAKC,KAAI,KAAK;AAAA,eACnE,IAAI,EAAQ,CAAAD,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAKC,KAAI,KAAK;AAAA,eACxE,IAAI,EAAQ,CAAAA,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAKD,KAAI,KAAK;AAAA,UAC5E,CAAAC,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAKD,KAAI,KAAK;AACjE,UAAI,GAAI,MAAKA,IAAG,KAAKC,IAAG,KAAK,KAAK,YAAY,IAAI,EAAE;AAAA,UAC/C,MAAKD,IAAG,KAAKC,IAAG,KAAK,KAAK,YAAY,IAAI,EAAE;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAcJ,IAAG,QAAQ,KAAK,KAAK,KAAK,KAAK;AAC3C,QAAIiB,KAAI,MAAM,KAAK,MAAM,GAAG;AAC5B,QAAI,IAAI,KAAK,SAASA,GAAE,CAAC,GAAGA,GAAE,CAAC,GAAG,KAAK,GAAG,EAAG,CAAAA,GAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACjE,QAAI,IAAI,KAAK,SAASA,GAAEA,GAAE,SAAS,CAAC,GAAGA,GAAEA,GAAE,SAAS,CAAC,GAAG,KAAK,GAAG,EAAG,CAAAA,GAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpF,QAAIA,KAAI,KAAK,YAAYjB,IAAGiB,EAAC,GAAG;AAC9B,eAAShB,KAAI,GAAGC,KAAIe,GAAE,QAAQ,IAAI,KAAK,KAAK,UAAUA,GAAEf,KAAI,CAAC,GAAGe,GAAEf,KAAI,CAAC,CAAC,GAAGD,KAAIC,IAAGD,MAAK,GAAG;AACxF,aAAK,IAAI,KAAK,KAAK,UAAUgB,GAAEhB,EAAC,GAAGgB,GAAEhB,KAAI,CAAC,CAAC;AAC3C,YAAI,MAAM,GAAI,CAAAA,KAAI,KAAK,MAAMD,IAAG,IAAI,IAAIiB,IAAGhB,EAAC,GAAGC,KAAIe,GAAE;AAAA,MACvD;AAAA,IACF,WAAW,KAAK,SAASjB,KAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,MAAAiB,KAAI,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAC7F;AACA,WAAOA;AAAA,EACT;AAAA,EACA,MAAMjB,IAAG,IAAI,IAAIiB,IAAGhB,IAAG;AACrB,WAAO,OAAO,IAAI;AAChB,UAAIE,IAAGC;AACP,cAAQ,IAAI;AAAA,QACV,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQD,KAAI,KAAK,MAAMC,KAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,IAAQD,KAAI,KAAK,MAAMC,KAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQD,KAAI,KAAK,MAAMC,KAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQD,KAAI,KAAK,MAAMC,KAAI,KAAK;AAAM;AAAA,MAC1D;AAGA,WAAKa,GAAEhB,EAAC,MAAME,MAAKc,GAAEhB,KAAI,CAAC,MAAMG,OAAM,KAAK,SAASJ,IAAGG,IAAGC,EAAC,GAAG;AAC5D,QAAAa,GAAE,OAAOhB,IAAG,GAAGE,IAAGC,EAAC,GAAGH,MAAK;AAAA,MAC7B;AAAA,IACF;AACA,WAAOA;AAAA,EACT;AAAA,EACA,SAAS,IAAI,IAAI,IAAI,IAAI;AACvB,QAAIW,KAAI,UAAU,GAAGT,IAAGC;AACxB,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMQ,GAAG,CAAAR,KAAI,KAAK,MAAMD,KAAI,MAAMS,KAAI,KAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMA,GAAG,CAAAR,KAAI,KAAK,MAAMD,KAAI,MAAMS,KAAI,KAAK;AAAA,IACzE;AACA,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMA,GAAG,CAAAT,KAAI,KAAK,MAAMC,KAAI,MAAMQ,KAAI,KAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAMA,GAAG,CAAAT,KAAI,KAAK,MAAMC,KAAI,MAAMQ,KAAI,KAAK;AAAA,IACzE;AACA,WAAO,CAACT,IAAGC,EAAC;AAAA,EACd;AAAA,EACA,UAAUD,IAAGC,IAAG;AACd,YAAQD,OAAM,KAAK,OAAO,IACpBA,OAAM,KAAK,OAAO,IAAS,MAC1BC,OAAM,KAAK,OAAO,IACnBA,OAAM,KAAK,OAAO,IAAS;AAAA,EACnC;AAAA,EACA,YAAYD,IAAGC,IAAG;AAChB,YAAQD,KAAI,KAAK,OAAO,IAClBA,KAAI,KAAK,OAAO,IAAS,MACxBC,KAAI,KAAK,OAAO,IACjBA,KAAI,KAAK,OAAO,IAAS;AAAA,EACjC;AAAA,EACA,UAAUa,IAAG;AACX,QAAIA,MAAKA,GAAE,SAAS,GAAG;AACrB,eAASjB,KAAI,GAAGA,KAAIiB,GAAE,QAAQjB,MAAI,GAAG;AACnC,cAAMC,MAAKD,KAAI,KAAKiB,GAAE,QAAQC,MAAKlB,KAAI,KAAKiB,GAAE;AAC9C,YAAIA,GAAEjB,EAAC,MAAMiB,GAAEhB,EAAC,KAAKgB,GAAEhB,EAAC,MAAMgB,GAAEC,EAAC,KAAKD,GAAEjB,KAAI,CAAC,MAAMiB,GAAEhB,KAAI,CAAC,KAAKgB,GAAEhB,KAAI,CAAC,MAAMgB,GAAEC,KAAI,CAAC,GAAG;AACpF,UAAAD,GAAE,OAAOhB,IAAG,CAAC,GAAGD,MAAK;AAAA,QACvB;AAAA,MACF;AACA,UAAI,CAACiB,GAAE,OAAQ,CAAAA,KAAI;AAAA,IACrB;AACA,WAAOA;AAAA,EACT;AACF;;;ACtUA,IAAM,MAAM,IAAI,KAAK;AAArB,IAAyB,MAAM,KAAK;AAEpC,SAAS,OAAO,GAAG;AACjB,SAAO,EAAE,CAAC;AACZ;AAEA,SAAS,OAAO,GAAG;AACjB,SAAO,EAAE,CAAC;AACZ;AAGA,SAAS,UAAU,GAAG;AACpB,QAAM,EAAC,WAAW,OAAM,IAAI;AAC5B,WAASE,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK,GAAG;AAC5C,UAAMC,KAAI,IAAI,UAAUD,EAAC,GACnBE,KAAI,IAAI,UAAUF,KAAI,CAAC,GACvB,IAAI,IAAI,UAAUA,KAAI,CAAC,GACvB,SAAS,OAAO,CAAC,IAAI,OAAOC,EAAC,MAAM,OAAOC,KAAI,CAAC,IAAI,OAAOD,KAAI,CAAC,MACtD,OAAOC,EAAC,IAAI,OAAOD,EAAC,MAAM,OAAO,IAAI,CAAC,IAAI,OAAOA,KAAI,CAAC;AACrE,QAAI,QAAQ,MAAO,QAAO;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,SAAS,OAAOE,IAAGC,IAAGC,IAAG;AACvB,SAAO,CAACF,KAAI,KAAK,IAAIA,KAAIC,EAAC,IAAIC,IAAGD,KAAI,KAAK,IAAID,KAAIC,EAAC,IAAIC,EAAC;AAC1D;AAEA,IAAqB,WAArB,MAAqB,UAAS;AAAA,EAC5B,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,MAAM;AAClD,WAAO,IAAI,UAAS,YAAY,SAC1B,UAAU,QAAQ,IAAI,IAAI,IAAI,IAC9B,aAAa,KAAK,aAAa,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC7D;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,cAAc,IAAI,WAAW,MAAM;AACxC,SAAK,UAAU,IAAI,WAAW,OAAO,SAAS,CAAC;AAC/C,SAAK,aAAa,IAAI,WAAW,OAAO,SAAS,CAAC;AAClD,SAAK,SAAS,KAAK,YAAY;AAC/B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,YAAY,OAAO;AACxB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,IAAI,KAAK,aAAa,SAAS,KAAK;AAG1C,QAAI,EAAE,QAAQ,EAAE,KAAK,SAAS,KAAK,UAAU,CAAC,GAAG;AAC/C,WAAK,YAAY,WAAW,KAAK,EAAC,QAAQ,OAAO,SAAO,EAAC,GAAG,CAACC,IAAEN,OAAMA,EAAC,EACnE,KAAK,CAACA,IAAGO,OAAM,OAAO,IAAIP,EAAC,IAAI,OAAO,IAAIO,EAAC,KAAK,OAAO,IAAIP,KAAI,CAAC,IAAI,OAAO,IAAIO,KAAI,CAAC,CAAC;AACxF,YAAMC,KAAI,KAAK,UAAU,CAAC,GAAG,IAAI,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC,GACvE,SAAS,CAAE,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAE,GAC9EH,KAAI,OAAO,KAAK,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AACpE,eAASL,KAAI,GAAGS,KAAI,OAAO,SAAS,GAAGT,KAAIS,IAAG,EAAET,IAAG;AACjD,cAAM,IAAI,OAAO,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAGK,EAAC;AACpD,eAAO,IAAIL,EAAC,IAAI,EAAE,CAAC;AACnB,eAAO,IAAIA,KAAI,CAAC,IAAI,EAAE,CAAC;AAAA,MACzB;AACA,WAAK,cAAc,IAAI,WAAW,MAAM;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,OAAO,KAAK,OAAO,KAAK,YAAY;AAC1C,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,UAAU,KAAK,QAAQ,KAAK,EAAE;AACpC,UAAM,YAAY,KAAK,WAAW,KAAK,EAAE;AAKzC,aAASQ,KAAI,GAAGC,KAAI,UAAU,QAAQD,KAAIC,IAAG,EAAED,IAAG;AAChD,YAAM,IAAI,UAAUA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI,CAAC;AAC/C,UAAI,UAAUA,EAAC,MAAM,MAAM,QAAQ,CAAC,MAAM,GAAI,SAAQ,CAAC,IAAIA;AAAA,IAC7D;AACA,aAASR,KAAI,GAAGS,KAAI,KAAK,QAAQT,KAAIS,IAAG,EAAET,IAAG;AAC3C,gBAAU,KAAKA,EAAC,CAAC,IAAIA;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,KAAK,KAAK,SAAS,GAAG;AACvC,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,cAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,UAAI,KAAK,WAAW,GAAG;AACrB,gBAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,aAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,aAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,WAAO,IAAI,QAAQ,MAAM,MAAM;AAAA,EACjC;AAAA,EACA,CAAC,UAAUA,IAAG;AACZ,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,WAAAU,WAAS,IAAI;AAGrE,QAAIA,YAAW;AACb,YAAMC,KAAID,WAAU,QAAQV,EAAC;AAC7B,UAAIW,KAAI,EAAG,OAAMD,WAAUC,KAAI,CAAC;AAChC,UAAIA,KAAID,WAAU,SAAS,EAAG,OAAMA,WAAUC,KAAI,CAAC;AACnD;AAAA,IACF;AAEA,UAAM,KAAK,QAAQX,EAAC;AACpB,QAAI,OAAO,GAAI;AACf,QAAIQ,KAAI,IAAI,KAAK;AACjB,OAAG;AACD,YAAM,KAAK,UAAUA,EAAC;AACtB,MAAAA,KAAIA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI;AAC9B,UAAI,UAAUA,EAAC,MAAMR,GAAG;AACxB,MAAAQ,KAAI,UAAUA,EAAC;AACf,UAAIA,OAAM,IAAI;AACZ,cAAM,IAAI,MAAM,WAAWR,EAAC,IAAI,KAAK,KAAK,MAAM;AAChD,YAAI,MAAM,GAAI,OAAM;AACpB;AAAA,MACF;AAAA,IACF,SAASQ,OAAM;AAAA,EACjB;AAAA,EACA,KAAKL,IAAGC,IAAGJ,KAAI,GAAG;AAChB,SAAKG,KAAI,CAACA,IAAGA,OAAMA,QAAOC,KAAI,CAACA,IAAGA,OAAMA,IAAI,QAAO;AACnD,UAAM,KAAKJ;AACX,QAAI;AACJ,YAAQ,IAAI,KAAK,MAAMA,IAAGG,IAAGC,EAAC,MAAM,KAAK,MAAMJ,MAAK,MAAM,GAAI,CAAAA,KAAI;AAClE,WAAO;AAAA,EACT;AAAA,EACA,MAAMA,IAAGG,IAAGC,IAAG;AACb,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,OAAM,IAAI;AAClE,QAAI,QAAQJ,EAAC,MAAM,MAAM,CAAC,OAAO,OAAQ,SAAQA,KAAI,MAAM,OAAO,UAAU;AAC5E,QAAI,IAAIA;AACR,QAAI,KAAK,IAAIG,KAAI,OAAOH,KAAI,CAAC,GAAG,CAAC,IAAI,IAAII,KAAI,OAAOJ,KAAI,IAAI,CAAC,GAAG,CAAC;AACjE,UAAM,KAAK,QAAQA,EAAC;AACpB,QAAIQ,KAAI;AACR,OAAG;AACD,UAAII,KAAI,UAAUJ,EAAC;AACnB,YAAM,KAAK,IAAIL,KAAI,OAAOS,KAAI,CAAC,GAAG,CAAC,IAAI,IAAIR,KAAI,OAAOQ,KAAI,IAAI,CAAC,GAAG,CAAC;AACnE,UAAI,KAAK,GAAI,MAAK,IAAI,IAAIA;AAC1B,MAAAJ,KAAIA,KAAI,MAAM,IAAIA,KAAI,IAAIA,KAAI;AAC9B,UAAI,UAAUA,EAAC,MAAMR,GAAG;AACxB,MAAAQ,KAAI,UAAUA,EAAC;AACf,UAAIA,OAAM,IAAI;AACZ,QAAAA,KAAI,MAAM,WAAWR,EAAC,IAAI,KAAK,KAAK,MAAM;AAC1C,YAAIQ,OAAMI,IAAG;AACX,cAAI,IAAIT,KAAI,OAAOK,KAAI,CAAC,GAAG,CAAC,IAAI,IAAIJ,KAAI,OAAOI,KAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAI,QAAOA;AAAA,QAC7E;AACA;AAAA,MACF;AAAA,IACF,SAASA,OAAM;AACf,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,WAAW,UAAS,IAAI;AACvC,aAASR,KAAI,GAAGS,KAAI,UAAU,QAAQT,KAAIS,IAAG,EAAET,IAAG;AAChD,YAAMO,KAAI,UAAUP,EAAC;AACrB,UAAIO,KAAIP,GAAG;AACX,YAAM,KAAK,UAAUA,EAAC,IAAI;AAC1B,YAAM,KAAK,UAAUO,EAAC,IAAI;AAC1B,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AAAA,IAC3C;AACA,SAAK,WAAW,OAAO;AACvB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAASF,IAAG;AACvB,QAAIA,OAAM,WAAc,CAAC,WAAW,OAAO,QAAQ,WAAW,YAAa,CAAAA,KAAI,SAAS,UAAU;AAClG,IAAAA,KAAIA,MAAK,SAAY,IAAI,CAACA;AAC1B,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,OAAM,IAAI;AACjB,aAASL,KAAI,GAAGS,KAAI,OAAO,QAAQT,KAAIS,IAAGT,MAAK,GAAG;AAChD,YAAMG,KAAI,OAAOH,EAAC,GAAGI,KAAI,OAAOJ,KAAI,CAAC;AACrC,cAAQ,OAAOG,KAAIE,IAAGD,EAAC;AACvB,cAAQ,IAAID,IAAGC,IAAGC,IAAG,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,MAAM,OAAM,IAAI;AACvB,UAAM,IAAI,KAAK,CAAC,IAAI,GAAGI,KAAI,KAAK;AAChC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACvC,aAAST,KAAI,GAAGA,KAAIS,IAAG,EAAET,IAAG;AAC1B,YAAMa,KAAI,IAAI,KAAKb,EAAC;AACpB,cAAQ,OAAO,OAAOa,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IACzC;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,UAAM,UAAU,IAAI;AACpB,SAAK,WAAW,OAAO;AACvB,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAeb,IAAG,SAAS;AACzB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,UAAS,IAAI;AAC5B,UAAM,KAAK,UAAUA,MAAK,CAAC,IAAI;AAC/B,UAAM,KAAK,UAAUA,KAAI,CAAC,IAAI;AAC9B,UAAMc,MAAK,UAAUd,KAAI,CAAC,IAAI;AAC9B,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAOc,GAAE,GAAG,OAAOA,MAAK,CAAC,CAAC;AACzC,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,mBAAmB;AAClB,UAAM,EAAC,UAAS,IAAI;AACpB,aAASd,KAAI,GAAGS,KAAI,UAAU,SAAS,GAAGT,KAAIS,IAAG,EAAET,IAAG;AACpD,YAAM,KAAK,gBAAgBA,EAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,gBAAgBA,IAAG;AACjB,UAAM,UAAU,IAAI;AACpB,SAAK,eAAeA,IAAG,OAAO;AAC9B,WAAO,QAAQ,MAAM;AAAA,EACvB;AACF;AAEA,SAAS,UAAU,QAAQ,IAAI,IAAI,MAAM;AACvC,QAAMS,KAAI,OAAO;AACjB,QAAM,QAAQ,IAAI,aAAaA,KAAI,CAAC;AACpC,WAAST,KAAI,GAAGA,KAAIS,IAAG,EAAET,IAAG;AAC1B,UAAM,IAAI,OAAOA,EAAC;AAClB,UAAMA,KAAI,CAAC,IAAI,GAAG,KAAK,MAAM,GAAGA,IAAG,MAAM;AACzC,UAAMA,KAAI,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,GAAGA,IAAG,MAAM;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,UAAU,aAAa,QAAQ,IAAI,IAAI,MAAM;AAC3C,MAAIA,KAAI;AACR,aAAW,KAAK,QAAQ;AACtB,UAAM,GAAG,KAAK,MAAM,GAAGA,IAAG,MAAM;AAChC,UAAM,GAAG,KAAK,MAAM,GAAGA,IAAG,MAAM;AAChC,MAAEA;AAAA,EACJ;AACF;;;;;;;;;;;;;;;;;;;;;;ACrPO,IAAMe,KAAsB,EAC/BC,SAAS,CAAC,GAAG,CAAA,GACbC,SAAS,CAAC,GAAG,CAAA,GACbC,QAAQ,CAAC,SAAS,SAAS,UAAU,QAAA,GACrCC,aAAAA,OACAC,eAAe,GACfC,eAAe,WACfC,aAAAA,MACAC,eAAe,GACfC,eAAe,WACfC,cAAAA,MACAC,WAAW,GACXC,YAAY,WACZC,MAAM,MAAA;AAbH,ICCMC,KAA8B,SAACC,IAAAA;AAG3C,SAA6B,CAACA,GAAKC,GAAGD,GAAKE,CAAAA;AAAE;ADJvC,ICMMC,KAAwBC;ADN9B,ICQMC,KAA0C;ADRhD,ICSMC,IAAsC;ADT5C,IEWMC,IAAoB,SAAHC,IAAAA;AAQJ,MAPtBC,KAAMD,GAANC,QAAMC,KAAAF,GACNG,iBAAAA,KAAAA,WAAeD,KAAGX,KAA2BW,IAAAE,KAAAJ,GAC7CK,QAAAA,KAAAA,WAAMD,KAAGT,KAAaS;AAMtB,SAAOH,GAAOK,IAAI,SAAAd,IAAAA;AACd,QAAAe,KAAeJ,GAAgBX,EAAAA,GAAxBC,KAACc,GAAA,CAAA,GAAEb,KAACa,GAAA,CAAA;AAEX,WAAO,CAACd,KAAIY,GAAOG,MAAMd,KAAIW,GAAOI,GAAAA;EACxC,CAAA;AACJ;AFzBO,IE2BMC,KAAc,SAAHC,IAAAA;AAYlB,MAXFV,KAAMU,GAANV,QACAW,KAAKD,GAALC,OACAC,KAAMF,GAANE,QAAMC,KAAAH,GACNN,QAAAA,KAAAA,WAAMS,KAAGnB,KAAamB,IACtBC,KAAKJ,GAALI,OAQMC,KAAWC,SAASC,KAAKjB,EAAAA,GACzBkB,KAAUJ,KACVC,GAASG,QAAQ,CACb,GACA,GACAd,GAAOG,OAAOI,KAAQP,GAAOe,OAC7Bf,GAAOI,MAAMI,KAASR,GAAOgB,MAAAA,CAAAA,IAAAA;AAIvC,SAAO,EAAEpB,QAAAA,IAAQe,UAAAA,IAAUG,SAAAA,GAAAA;AAC/B;AFnDO,IG6BMG,KAAiB,SAAHtB,IAAAA;AAAA,MACvBC,KAAMD,GAANC,QAAMC,KAAAF,GACNG,iBAAAA,KAAAA,WAAeD,KAAGX,KAA2BW,IAC7CU,KAAKZ,GAALY,OACAC,KAAMb,GAANa,QAAMT,KAAAJ,GACNK,QAAAA,KAAAA,WAAMD,KAAGT,KAAaS,IACtBW,IAAKf,GAALe;AAAK,aAYLQ,aAAAA,SACI,WAAA;AAAA,WACIb,GAAY,EACRT,QAAQF,EAAwB,EAAEE,QAAAA,IAAQI,QAAAA,IAAQF,iBAAAA,GAAAA,CAAAA,GAClDS,OAAAA,IACAC,QAAAA,IACAR,QAAAA,IACAU,OAAAA,EAAAA,CAAAA;EACF,GACN,CAACZ,IAAiBF,IAAQW,IAAOC,IAAQR,IAAQU,CAAAA,CAAAA;AACpD;AHzDE,IG2DMS,IAAa,SAAHb,IAAAA;AAYjB,MAXFc,KAAId,GAAJc,MACAb,KAAKD,GAALC,OACAC,KAAMF,GAANE,QACAnC,KAAOiC,GAAPjC,SACAC,KAAOgC,GAAPhC,SAQM+C,SAASH,aAAAA,SAAQ,WAAA;AAAA,WAAMI,OAAAA,EAAcC,OAAOlD,EAAAA,EAASmD,MAAM,CAAC,GAAGjB,EAAAA,CAAAA;EAAO,GAAE,CAAClC,IAASkC,EAAAA,CAAAA,GAClFkB,SAASP,aAAAA,SACX,WAAA;AAAA,WAAMI,OAAAA,EAAcC,OAAOjD,EAAAA,EAASkD,MAAM,CAAC,GAAGhB,EAAAA,CAAAA;EAAQ,GACtD,CAAClC,IAASkC,EAAAA,CAAAA,GAGRZ,QAASsB,aAAAA,SACX,WAAA;AAAA,WACIE,GAAKnB,IAAI,SAAAyB,IAAAA;AAAC,aAAK,EACXtC,GAAGiC,GAAOK,GAAEtC,CAAAA,GACZC,GAAGoC,GAAOC,GAAErC,CAAAA,GACZ+B,MAAMM,GAAAA;IACT,CAAA;EAAE,GACP,CAACN,IAAMC,IAAQI,EAAAA,CAAAA;AAGnB,aAAOP,aAAAA,SAAQ,WAAA;AACX,QAAMP,KAAWC,SAASC,KAAKjB,EAAOK,IAAI,SAAA0B,IAAAA;AAAC,aAAI,CAACA,GAAEvC,GAAGuC,GAAEtC,CAAAA;IAAE,CAAA,CAAA,GACnDyB,KAAUH,GAASG,QAAQ,CAAC,GAAG,GAAGP,IAAOC,EAAAA,CAAAA;AAE/C,WAAO,EACHZ,QAAAA,GACAe,UAAAA,IACAG,SAAAA,GAAAA;EAEP,GAAE,CAAClB,GAAQW,IAAOC,EAAAA,CAAAA;AACvB;AHlGO,IGuGMoB,IAAyB,SAAHC,IAAAA;AAAA,MAC/BjC,KAAMiC,GAANjC,QACAe,KAAQkB,GAARlB,UACAG,KAAOe,GAAPf;AAAO,aAEPI,aAAAA,SACI,WAAA;AAAA,WAAO,EACHtB,QAAAA,IACAe,UAAAA,IACAG,SAAAA,GAAAA;EACF,GACF,CAAClB,IAAQe,IAAUG,EAAAA,CAAAA;AACtB;AHnHE,IGqHMgB,IAAgB,SAAHC,IAAAA;AA8CpB,MA7CFC,KAAUD,GAAVC,YACAC,KAAKF,GAALE,OAAKC,KAAAH,GACLjC,iBAAAA,IAAAA,WAAeoC,KAAGhD,KAA2BgD,IAC7CvB,IAAQoB,GAARpB,UACYwB,KAAcJ,GAA1BK,YAAUC,IAAAN,GACV/B,QAAAA,IAAAA,WAAMqC,IAAG/C,KAAa+C,GAAAC,IAAAP,GACtBQ,iBAAAA,KAAAA,WAAeD,IAAGE,IAAAA,IAAQF,GAAAG,KAAAV,GAC1BW,eAAAA,KAAAA,WAAaD,MAAOA,IACpBE,KAAYZ,GAAZY,cACAC,KAAWb,GAAXa,aACAC,KAAYd,GAAZc,cACAC,KAAWf,GAAXe,aACAC,KAAShB,GAATgB,WACAC,KAAOjB,GAAPiB,SACAC,KAAalB,GAAbkB,eACAC,KAAYnB,GAAZmB,cACAC,KAAWpB,GAAXoB,aACAC,KAAUrB,GAAVqB,YAAUC,KAAAtB,GACVuB,sBAAAA,KAAAA,WAAoBD,MAAQA,IAC5BE,KAAOxB,GAAPwB,SAAOC,KAAAzB,GACP0B,iBAAAA,KAAAA,WAAeD,KAAGhE,KAAsBgE,IAAAE,KAAA3B,GACxC4B,eAAAA,KAAAA,WAAaD,KAAGjE,IAAoBiE,IA0BpCE,SAA8BC,aAAAA,UAAgC,IAAA,GAAvDC,KAAOF,GAAA,CAAA,GAAExB,IAAUwB,GAAA,CAAA,GAIpBG,QAAWC,aAAAA,QAA8B,IAAA;AAE/CC,mBAAAA,WAAU,WAAA;AACNF,MAASD,UAAUA;EACvB,GAAG,CAACC,GAAUD,EAAAA,CAAAA;AAEd,MAAMI,QAAWC,aAAAA,aACb,SAACC,IAAAA;AACG,QAAA,CAAKpC,GAAW8B,WAA4B,MAAjB7B,GAAMoC,OAAc,QAAO;AAEtD,QAAAC,KAAeC,GAAkBvC,GAAW8B,SAASM,EAAAA,GAA9ChF,KAACkF,GAAA,CAAA,GAAEjF,KAACiF,GAAA,CAAA,GAEPE,KAAuB7D,EAAS8D,KAAKrF,IAAGC,EAAAA,GACxCF,KAAAA,WAAOqF,KAAsBvC,GAAMuC,EAAAA,IAAS;AAEhD,QAAIrF,MAAQoD,OAAoBC,IAAAA,GAAU;AACtC,UAAAtC,KAAuBJ,EAAgBX,EAAAA,GAAhCuF,KAAKxE,GAAA,CAAA,GAAEyE,KAAKzE,GAAA,CAAA;AACf0E,SAAYxF,IAAGC,IAAGqF,KAAQ1E,EAAOG,MAAMwE,KAAQ3E,EAAOI,GAAAA,IAAOmC,OAC7DiC,KAAQ,MACRrF,KAAO;IAEf;AAEA,WAAc,SAAVqF,MAA2B,SAATrF,KAAsB,OAErC,CAACqF,IAAOrF,EAAAA;EACnB,GACA,CAAC6C,IAAYrB,GAAUsB,IAAOnC,GAAiBE,GAAQuC,EAAAA,CAAAA,GAG3DsC,IAA6DC,EAAAA,GAArDC,KAAaF,EAAbE,eAAeC,KAAoBH,EAApBG,sBAAsBC,KAAWJ,EAAXI,aACvCC,QAAchE,aAAAA,SAAQ,WAAA;AACxB,QAAKqC,GAEL,QAAwB,aAApBE,KAEO,SAACtE,IAAYiF,IAAAA;AAChBY,MAAAA,GAAqBzB,GAAQpE,EAAAA,GAAOiF,IAAOT,EAAAA;IAAAA,IAK5C,SAACxE,IAAAA;AACJ,UAAAgG,KAAerF,EAAgBX,EAAAA,GAAxBC,KAAC+F,GAAA,CAAA,GAAE9F,KAAC8F,GAAA,CAAA;AACXJ,MAAAA,GAAcxB,GAAQpE,EAAAA,GAAO,CAACC,KAAIY,EAAOG,MAAMd,KAAIW,EAAOI,GAAAA,GAAMuD,EAAAA;IAAAA;EAExE,GAAG,CACCoB,IACAC,IACAzB,IACAE,IACAE,IACA7D,GACAE,CAAAA,CAAAA,GAKEoF,SAAmBjB,aAAAA,aACrB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAKvB,QAHAhC,EAAWiD,EAAAA,GACG,QAAdlD,MAAAA,GAAiBkD,KAAQA,GAAM,CAAA,IAAK,IAAA,GAEhCA,IAAO;AACP,UAAMlG,KAAOkG,GAAM,CAAA;AAAA,cAEnBH,KAAAA,EAAc/F,IAAMiF,EAAAA,GACR,QAAZzB,MAAAA,GAAe0C,GAAM,CAAA,GAAIjB,EAAAA;IAC7B;EACJ,GACA,CAACF,GAAU9B,GAAYD,IAAgB+C,GAAavC,EAAAA,CAAAA,GAIlD2C,SAAkBnB,aAAAA,aACpB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAIvB,QAFAhC,EAAWiD,EAAAA,GAEPA,IAAO;AACP,UAAOb,KAAea,GAAK,CAAA,GAAblG,KAAQkG,GAAK,CAAA;AAK3B,UAHAlD,QAAAA,MAAAA,GAAiBhD,EAAAA,GAAAA,QACjB+F,KAAAA,EAAc/F,IAAMiF,EAAAA,GAEhBL,EAASD,SAAS;AAClB,YAAAyB,KAAsCxB,EAASD,SAAxC0B,KAAaD,GAAA,CAAA,GAAEE,KAAYF,GAAA,CAAA;AAC9Bf,QAAAA,OAAUgB,KAAAA,QAEV3C,MAAAA,GAAe4C,IAAcrB,EAAAA,IAAAA,QAG7BxB,MAAAA,GAAczD,IAAMiF,EAAAA;MAE5B,MAAA,SACIzB,MAAAA,GAAexD,IAAMiF,EAAAA;IAE7B,MACIjC,SAAAA,MAAAA,GAAiB,IAAA,GACN,QAAX8C,MAAAA,GAAAA,GAEIlB,EAASD,YAEG,QAAZjB,MAAAA,GAAekB,EAASD,QAAQ,CAAA,GAAIM,EAAAA;EAG/C,GACD,CACIF,GACA9B,GACAD,IACA4B,GACApB,IACAC,IACAC,IACAqC,GACAD,EAAAA,CAAAA,GAMFS,SAAmBvB,aAAAA,aACrB,SAACC,IAAAA;AACGhC,MAAW,IAAA,GACXD,QAAAA,MAAAA,GAAiB,IAAA,GAEjB8C,GAAAA,GAEIpC,MAAgBkB,EAASD,WACzBjB,GAAakB,EAASD,QAAQ,CAAA,GAAIM,EAAAA;EAE1C,GACA,CAAChC,GAAYD,IAAgB4B,GAAUkB,IAAapC,EAAAA,CAAAA,GAGlD8C,SAAkBxB,aAAAA,aACpB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAEvBhC,MAAWiD,EAAAA,GAEPA,OAAkB,QAAXvC,MAAAA,GAAcuC,GAAM,CAAA,GAAIjB,EAAAA;EACtC,GACD,CAACF,GAAU9B,GAAYU,EAAAA,CAAAA,GAGrB8C,SAAgBzB,aAAAA,aAClB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAEvBhC,MAAWiD,EAAAA,GAEPA,OAAgB,QAATtC,MAAAA,GAAYsC,GAAM,CAAA,GAAIjB,EAAAA;EACpC,GACD,CAACF,GAAU9B,GAAYW,EAAAA,CAAAA,GAGrB8C,SAAc1B,aAAAA,aAChB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAEvBhC,MAAWiD,EAAAA,GAEPA,OAAc,QAAPrC,MAAAA,GAAUqC,GAAM,CAAA,GAAIjB,EAAAA;EAClC,GACD,CAACF,GAAU9B,GAAYY,EAAAA,CAAAA,GAGrB8C,SAAoB3B,aAAAA,aACtB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAEvBhC,MAAWiD,EAAAA,GAEPA,OAAoB,QAAbpC,MAAAA,GAAgBoC,GAAM,CAAA,GAAIjB,EAAAA;EACxC,GACD,CAACF,GAAU9B,GAAYa,EAAAA,CAAAA,GAGrB8C,SAAmB5B,aAAAA,aACrB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAEnBd,IAAAA,OACAlB,EAAWiD,EAAAA,GACG,QAAdlD,MAAAA,GAAiBkD,KAAQA,GAAM,CAAA,IAAK,IAAA,IAGpCA,OAAmB,QAAZnC,MAAAA,GAAemC,GAAM,CAAA,GAAIjB,EAAAA;EACxC,GACA,CAACF,GAAU9B,GAAYD,IAAgBmB,IAAsBJ,EAAAA,CAAAA,GAG3D8C,UAAkB7B,aAAAA,aACpB,SAACC,IAAAA;AACG,QAAMiB,KAAQnB,EAASE,EAAAA;AAEnBd,IAAAA,OACAlB,EAAWiD,EAAAA,GACG,QAAdlD,MAAAA,GAAiBkD,KAAQA,GAAM,CAAA,IAAK,IAAA,IAGpCA,OAAkB,QAAXlC,MAAAA,GAAckC,GAAM,CAAA,GAAIjB,EAAAA;EACvC,GACA,CAACF,GAAU9B,GAAYD,IAAgBmB,IAAsBH,EAAAA,CAAAA,GAG3D8C,SAAiB9B,aAAAA,aACnB,SAACC,IAAAA;AACOd,IAAAA,OACAlB,EAAW,IAAA,GACXD,QAAAA,MAAAA,GAAiB,IAAA,IAGjBiB,MAAcW,EAASD,WACvBV,GAAWW,EAASD,QAAQ,CAAA,GAAIM,EAAAA;EAExC,GACA,CAACd,IAAsBlB,GAAYD,IAAgBiB,IAAYW,CAAAA,CAAAA;AAGnE,SAAO,EACHD,SAAAA,IACAsB,kBAAkB1C,KAAgB0C,KAAAA,QAClCE,iBAAiB5C,KAAgB4C,KAAAA,QACjCI,kBAAkBhD,KAAgBgD,KAAAA,QAClCC,iBAAiBjD,KAAgBiD,KAAAA,QACjCC,eAAelD,KAAgBkD,KAAAA,QAC/BC,aAAanD,KAAgBmD,KAAAA,QAC7BC,mBAAmBpD,KAAgBoD,KAAAA,QACnCC,kBAAkBrD,KAAgBqD,KAAAA,QAClCC,iBAAiBtD,KAAgBsD,MAAAA,QACjCC,gBAAgBvD,KAAgBuD,KAAAA,OAAiBC;AAEzD;AHvZO,IGyfP,IAAA,CAAA,OAAA;AHzfO,IISDC,IAAe,SAAHC,IAAAA;AAmBO,MAlBrBC,KAAID,GAAJC,MACAC,KAAKF,GAALE,OACAC,KAAMH,GAANG,QACQC,KAAaJ,GAArBK,QAAMC,KAAAN,GACNO,QAAAA,KAAAA,WAAMD,KAAGE,GAAoBD,SAAMD,IAAAG,IAAAT,GACnCU,SAAAA,KAAAA,WAAOD,IAAGD,GAAoBE,UAAOD,GAAAE,IAAAX,GACrCY,SAAAA,IAAAA,WAAOD,IAAGH,GAAoBI,UAAOD,GAAAE,IAAAb,GACrCc,aAAAA,IAAAA,WAAWD,IAAGL,GAAoBM,cAAWD,GAAAE,KAAAf,GAC7CgB,eAAAA,KAAAA,WAAaD,KAAGP,GAAoBQ,gBAAaD,IAAAE,KAAAjB,GACjDkB,eAAAA,KAAAA,WAAaD,KAAGT,GAAoBU,gBAAaD,IAAAE,KAAAnB,GACjDoB,aAAAA,KAAAA,WAAWD,KAAGX,GAAoBY,cAAWD,IAAAE,KAAArB,GAC7CsB,eAAAA,KAAAA,WAAaD,KAAGb,GAAoBc,gBAAaD,IAAAE,KAAAvB,GACjDwB,eAAAA,KAAAA,WAAaD,KAAGf,GAAoBgB,gBAAaD,IAAAE,KAAAzB,GACjD0B,cAAAA,KAAAA,WAAYD,KAAGjB,GAAoBY,cAAWK,IAAAE,KAAA3B,GAC9C4B,WAAAA,KAAAA,WAASD,KAAGnB,GAAoBoB,YAASD,IAAAE,KAAA7B,GACzC8B,YAAAA,KAAAA,WAAUD,KAAGrB,GAAoBsB,aAAUD,IAAAE,KAAA/B,GAC3CgC,MAAAA,KAAAA,WAAID,KAAGvB,GAAoBwB,OAAID,IAC/BE,KAAYjC,GAAZiC,cAEAC,KAAqEC,GACjEjC,IACAC,IACAC,EAAAA,GAHIgC,KAAUF,GAAVE,YAAYC,KAAWH,GAAXG,aAAahC,KAAM6B,GAAN7B,QAAQiC,IAAUJ,GAAVI,YAAYC,IAAWL,GAAXK,aAMrDC,IAAsCC,EAAW,EAC7CxC,MAAAA,IACAC,OAAOoC,GACPnC,QAAQoC,GACR7B,SAAAA,IACAE,SAAAA,EAAAA,CAAAA,GALI8B,IAAMF,EAANE,QAAQC,KAAQH,EAARG,UAAUC,KAAOJ,EAAPI,SAQpBC,KAA+C,EACjDC,OAAO,MACPC,OAAO,MACPL,QAAQ,MACRM,QAAQ,KAAA;AAGRlC,OAAeP,GAAO0C,SAAS,OAAA,MAC/BJ,GAAUC,YACNI,mBAAAA,KAAA,QAAA,EAEIC,QAAQjC,IACRkC,aAAapC,IACbqC,MAAK,QACLC,GAAGX,GAASY,OAAAA,EAAAA,GAJR,OAAA,IASZnC,MAAeb,GAAO0C,SAAS,OAAA,MAC/BJ,GAAUE,YACNG,mBAAAA,KAAA,QAAA,EAEII,GAAGV,GAAQW,OAAAA,GACXF,MAAK,QACLF,QAAQ3B,IACR4B,aAAa9B,GAAAA,GAJT,OAAA,IASZI,MAAgBnB,GAAO0C,SAAS,QAAA,MAChCJ,GAAUH,aACNQ,mBAAAA,KAAA,QAAA,EAEIC,QAAO,QACPE,MAAMvB,IACNwB,GAAGX,GAASa,aAAAA,QAAwB5B,KAAY,CAAA,EAAA,GAH5C,QAAA,IAQZrB,GAAO0C,SAAS,QAAA,MAChBJ,GAAUG,aACNE,mBAAAA,KAAA,QAAA,EAEIG,MAAK,QACLF,QAAQ3B,IACR4B,aAAa9B,IACbgC,GAAGV,GAAQa,aAAAA,EAAAA,GAJP,QAAA;AAShB,MAAMC,IAAeC,EAAuB,EACxCjB,QAAAA,GACAC,UAAAA,IACAC,SAAAA,GAAAA,CAAAA;AAGJ,aACIM,mBAAAA,KAACU,IAAU,EACP1D,OAAOkC,IACPjC,QAAQkC,IACRhC,QAAQA,IACR2B,MAAMA,IACN6B,KAAK5B,IAAa6B,UAEjBvD,GAAOwD,IAAI,SAACC,IAAOC,IAAAA;AAChB,WAAA,WAAIpB,GAAUmB,EAAAA,IACHnB,GAAUmB,EAAAA,IAGA,cAAA,OAAVA,SACAd,mBAAAA,KAACgB,aAAAA,UAAQ,EAAAJ,cAAUK,aAAAA,eAAcH,IAAON,CAAAA,EAAAA,GAAzBO,EAAAA,IAGnB;EAAA,CAAA,EAAA,CAAA;AAIvB;AJ5HO,II8HMG,QAAUC,aAAAA,YACnB,SAAAC,IAMIT,IAAAA;AAAuB,MAJnBU,KAAKD,GAALC,OACGC,KAAKC,EAAAH,IAAAI,CAAAA;AAAA,aAKZxB,mBAAAA,KAACyB,IAAS,EAACC,eAAAA,OAAsBC,SAAAA,OAAgBN,OAAOA,IAAMT,cAC1DZ,mBAAAA,KAACnD,GAAY+E,EAAAA,CAAAA,GAAKN,IAAK,EAAEvC,cAAc4B,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAC/B,CAAA;AJzIb,IIyIa,IAAA,CAAA,gBAAA,iBAAA,YAAA,gBAAA;AJzIb,IKOMkB,QAAoBV,aAAAA,YAC7B,SAAArE,IAEI6D,IAAAA;AAAuB,MADrBmB,KAAYhF,GAAZgF,cAAcC,KAAajF,GAAbiF,eAAeC,KAAQlF,GAARkF,UAAUC,KAAcnF,GAAdmF,gBAAmBX,KAAKC,EAAAzE,IAAA0E,CAAAA;AAAA,aAGjExB,mBAAAA,KAACkC,IAAiB,EACdJ,cAAcA,IACdC,eAAeA,IACfC,UAAUA,IACVC,gBAAgBA,IAAerB,UAE9B,SAAAQ,IAAAA;AAAA,QAAGpE,KAAKoE,GAALpE,OAAOC,KAAMmE,GAANnE;AAAM,eACb+C,mBAAAA,KAACkB,GAAOU,EAAAA,CAAAA,GAAKN,IAAK,EAAEtE,OAAOA,IAAOC,QAAQA,IAAQ0D,KAAKA,GAAAA,CAAAA,CAAAA;EAAO,EAAA,CAAA;AAElD,CAAA;ALrBrB,IMmCMwB,KAAO,SAAHrF,IAAAA;AAuBM,MAtBnBsF,KAAKtF,GAALsF,OACApF,KAAKF,GAALE,OACAC,KAAMH,GAANG,QAAMoF,KAAAvF,GACNK,QAAAA,KAAAA,WAAMkF,KAAGC,KAAaD,IACtBE,KAAezF,GAAfyF,iBACAC,IAAU1F,GAAV0F,YACAC,KAAY3F,GAAZ2F,cACAC,IAAW5F,GAAX4F,aACAC,IAAY7F,GAAZ6F,cACAC,KAAW9F,GAAX8F,aACAC,IAAS/F,GAAT+F,WACAC,IAAOhG,GAAPgG,SACAC,IAAajG,GAAbiG,eACAC,KAAYlG,GAAZkG,cACAC,KAAWnG,GAAXmG,aACAC,KAAUpG,GAAVoG,YAAUC,KAAArG,GACVsG,sBAAAA,KAAAA,WAAoBD,MAAQA,IAAAE,KAAAvG,GAC5BwG,iBAAAA,KAAAA,WAAeD,KAAGE,IAAAA,IAAQF,IAC1BG,KAAO1G,GAAP0G,SAAOC,KAAA3G,GACP4G,iBAAAA,KAAAA,WAAeD,KAAGE,KAAsBF,IAAAG,KAAA9G,GACxC+G,eAAAA,KAAAA,WAAaD,KAAGE,IAAoBF,IACpCG,KAAKjH,GAALiH,OAEMC,SAAaC,aAAAA,QAA8B,IAAA,GAEjDC,KAA8BC,GAAqB,EAC/C3E,QAAQ4C,IACRG,iBAAAA,IACAvF,OAAAA,IACAC,QAAAA,IACAE,QAAAA,IACA4G,OAAAA,GAAAA,CAAAA,GANItE,KAAQyE,GAARzE,UAAUC,KAAOwE,GAAPxE,SASlB0E,KAYIC,EAAoC,EACpCL,YAAAA,IACA5B,OAAAA,IACA3C,UAAAA,IACAtC,QAAAA,IACAmG,iBAAAA,IACAd,YAAAA,GACAC,cAAAA,IACAC,aAAAA,GACAC,cAAAA,GACAC,aAAAA,IACAC,WAAAA,GACAC,SAAAA,GACAC,eAAAA,GACAC,cAAAA,IACAC,aAAAA,IACAC,YAAAA,IACAE,sBAAAA,IACAI,SAAAA,IACAE,iBAAAA,IACAG,eAAAA,GAAAA,CAAAA,GA/BAS,KAAOF,GAAPE,SACAC,IAAgBH,GAAhBG,kBACAC,IAAeJ,GAAfI,iBACAC,IAAgBL,GAAhBK,kBACAC,IAAeN,GAAfM,iBACAC,KAAaP,GAAbO,eACAC,KAAWR,GAAXQ,aACAC,KAAiBT,GAAjBS,mBACAC,IAAgBV,GAAhBU,kBACAC,KAAeX,GAAfW,iBACAC,KAAcZ,GAAdY,gBAwBEC,SAAcC,aAAAA,SAAQ,WAAA;AACxB,QAAInB,MAASrE,GAAS,QAAOA,GAAQW,OAAAA;EAEzC,GAAG,CAAC0D,IAAOrE,EAAAA,CAAAA;AAEX,aACIyF,mBAAAA,MAAA,KAAA,EAAGxE,KAAKqD,IAAYoB,WAAS,eAAA,CAAgBjI,GAAOkI,OAAAA,MAAAA,CAASlI,GAAOmI,MAAO,KAAA1E,UAAAA,CACtEmD,MAASrE,UACNyF,mBAAAA,MAAAI,mBAAAA,UAAA,EAAA3E,UAAAA,KACIZ,mBAAAA,KAAA,QAAA,EAAMI,GAAG6E,IAAahF,QAAO,OAAMC,aAAa,GAAGsF,SAAS,KAAA,CAAA,GAC3DlC,KAAkBC,IAAAA,SACfvD,mBAAAA,KAAA,QAAA,EACIC,QAAO,OACPC,aAAa,MACbC,MAAK,QACLC,GAAGX,GAASa,aAAAA,QAAwBgD,EAAAA,EAAAA,CAAAA,GAI3CgB,UACGtE,mBAAAA,KAAA,QAAA,EAAMG,MAAK,QAAOqF,SAAS,MAAMpF,GAAGV,GAAQ+F,WAAWnB,GAAQ,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,OAK3EtE,mBAAAA,KAAA,QAAA,EACI,YAAS,oBACThD,OAAOG,GAAOkI,OAAOrI,KAAQG,GAAOuI,OACpCzI,QAAQE,GAAOmI,MAAMrI,KAASE,GAAOwI,QACrCxF,MAAK,OACLqF,SAAS,GACTI,OAAO,EAAEC,QAAQ,OAAA,GACjBpD,cAAc8B,GACd7B,aAAa8B,GACb7B,cAAc8B,GACd7B,aAAa8B,GACb7B,WAAW8B,IACX3B,cAAc8B,GACd7B,aAAa8B,IACb7B,YAAY8B,IACZlC,SAAS8B,IACT7B,eAAe8B,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAI/B;ANtJO,IOAMiB,IAAwB,SACjCC,IACArG,IAAAA;AAEAqG,EAAAA,GAAIC,KAAAA,GAEJD,GAAIE,cAAc,MAClBF,GAAIG,UAAAA,GACJxG,GAAQW,OAAO0F,EAAAA,GACfA,GAAII,cAAc,OAClBJ,GAAIK,YAAY,GAChBL,GAAI9F,OAAAA,GAEJ8F,GAAIM,QAAAA;AACR;APdO,IOiCMC,KAA4B,SACrCC,IACAC,IACAC,IAAAA;AAEAF,EAAAA,GAAIG,KAAAA,GAEJH,GAAII,cAAc,MAClBJ,GAAIK,UAAAA,GACJJ,GAAQK,WAAWJ,IAAOF,EAAAA,GAC1BA,GAAIO,YAAY,QAChBP,GAAIQ,KAAAA,GAEJR,GAAIS,QAAAA;AACR;;;;;;;;;;;;;;;;;;;;;AC7CO,IAAA;AAAA,ICOMC,KAyBT,EACAC,QAAQ,EACJC,MAAM,QAAA,GAEVC,QAAQ,EACJD,MAAM,UACNE,KAAK,GACLC,KAAK,OAAA,GAETC,OAAO,UACPC,QAAQ,EAAEC,QAAQ,OAAA,GAClBC,WAAW,GACXC,QAAQ,CACJ,QACA,WACA,QACA,SACA,aACA,SACA,UACA,UACA,QACA,SAAA,GAEJC,cAAAA,MACAC,WAAW,GACXC,YAAY,EAAEC,MAAM,eAAA,GACpBC,kBAAkB,GAClBC,kBAAkB,EAAEC,OAAO,aAAA,GAC3BC,YAAAA,OACAC,mBAAmB,GACnBC,aAAa,KACbC,aAAAA,MACAC,aAAAA,MACAC,SAAS,CAAA,GACTC,eAAAA,MACAC,aDnDwBC,cAAAA,MAjBW,SAAHC,IAAAA;AAEH,MAD7BC,KAAKD,GAALC;AAEA,aACIC,oBAAAA,KAACC,GAAY,EACTC,QACIC,oBAAAA,MAAA,QAAA,EAAAC,UAAA,CAAM,WACCJ,oBAAAA,KAAA,UAAA,EAAAI,UAASL,GAAMM,KAAKC,WAAAA,CAAAA,GAAoB,QAAK,SAChDN,oBAAAA,KAAA,UAAA,EAAAI,UAASL,GAAMM,KAAKE,WAAAA,CAAAA,CAAAA,EAAAA,CAAAA,GAG5BC,YAAAA,MACAC,OAAOV,GAAMW,YAAAA,CAAAA;AAGzB,CAAA,GCsDIC,kBChDwBd,cAAAA,MApBW,SAAHC,IAAAA;AAGH,MAF7Bc,KAAKd,GAALc,OACAC,KAAIf,GAAJe,MAEMzB,KAAQ0B,EAAAA,GACRC,KAAqB,QAATF,KAAe,MAAM;AAEvC,aACIb,oBAAAA,KAACgB,GAAY,EACTC,MAAML,GAAMM,OAAOC,IAAI,SAAApB,IAAAA;AAAK,WAAI,KAC5BC,oBAAAA,KAACoB,GAAI,EAAYX,OAAOV,GAAMW,aAAaW,OAAOjC,GAAMQ,QAAQ0B,KAAAA,GAAtD,MAAA,GACVvB,GAAMwB,cACNvB,oBAAAA,KAAA,QAAA,EAAkBqB,OAAOjC,GAAMQ,QAAQ4B,gBAAepB,UACjDL,GAAMM,KAAQU,KAAS,WAAA,EAAA,GADlB,OAAA,CAAA;EAGb,CAAA,EAAA,CAAA;AAGb,CAAA,GDmDIU,WAAAA,OACAC,eAAAA,KAAe;ADvEZ,IC0EMC,KAGZC,EAAAA,CAAAA,GACMzD,IAAkB,EACrB0D,MAAM,CAAA,GACNC,MAAM,CAAA,GACNC,kBAAAA,OACAC,YAAY,mBACZC,eAAe,UACfC,SAAS,MACTC,WAAW,MACXC,YAAYC,GACZC,UAAUD,GACVE,SAAAA,OACAC,cAAAA,OACAC,aAAAA,OACAC,iBAAAA,MACAC,eAAe,eACfC,sBAAAA,OACAC,kBAAkB,CAAA,GAClBC,SAAAA,MACAC,cAAc,UACdC,MAAM,OACNC,aAAAA,MAAa,CAAA;ADlGV,ICqGMC,KAGZtB,EAAAA,CAAAA,GACMzD,IAAkB,EACrBgF,YAA8B,eAAA,OAAXC,UAAiDC,SAA3BA,KAAID,OAAOE,oBAAgBD,KAAS,GAC7EnB,SAAS,MACTC,WAAW,MACXC,YAAYC,GACZC,UAAUD,EAAAA,CAAAA;AEpFP,SAASkB,GAAiB9E,IAAAA;AAC7B,aAAO+E,cAAAA,SAAQ,WAAA;AACX,WAAOC,aAAAA,EAIFC,QAAQ,SAAAC,IAAAA;AAAC,aAAY,SAARA,GAAEC,KAAsB,SAARD,GAAEE;IAAU,CAAA,EACzCD,EAAE,SAAAD,IAAAA;AAAC,aAAIA,GAAEC;IAAC,CAAA,EACVC,EAAE,SAAAF,IAAAA;AAAC,aAAIA,GAAEE;IAAC,CAAA,EACVpF,MAAMqF,GAAcrF,EAAAA,CAAAA;EAC7B,GAAG,CAACA,EAAAA,CAAAA;AACR;AAEO,SAASsF,GAAgBjE,IAAAA;AAQd,MAPdrB,KAAKqB,GAALrB,OACAH,KAAMwB,GAANxB,QACAgB,KAAiBQ,GAAjBR;AAMA,aAAOkE,cAAAA,SAAQ,WAAA;AACX,WAAOQ,aAAAA,EAIFN,QAAQ,SAAAC,IAAAA;AAAC,aAAY,SAARA,GAAEC,KAAsB,SAARD,GAAEE;IAAU,CAAA,EACzCD,EAAE,SAAAD,IAAAA;AAAC,aAAIA,GAAEC;IAAC,CAAA,EACVK,GAAG,SAAAN,IAAAA;AAAC,aAAIA,GAAEE;IAAC,CAAA,EACXpF,MAAMqF,GAAcrF,EAAAA,CAAAA,EACpByF,GAAG5F,GAAOgB,EAAAA,CAAAA;EAClB,GAAE,CAACb,IAAOH,IAAQgB,EAAAA,CAAAA;AACvB;AAAA,IAuDa6E,KAAY,SAAHC,IAAAA;AAYhB,MAXFC,KAAWD,GAAXC,aACA7B,KAAY4B,GAAZ5B,cACAtB,KAAMkD,GAANlD,QACAoD,KAAKF,GAALE,OACAC,KAAMH,GAANG;AAQA,aAAOf,cAAAA,SAAQ,WAAA;AACX,QAAqB,QAAjBhB,IAAsB;AACtB,UAAMrB,KAAM,oBAAIqD;AAOhB,aANAtD,GAAOuD,QAAQ,SAAA1E,IAAAA;AACU,iBAAjBA,GAAMM,KAAKuD,KAA+B,SAAjB7D,GAAMM,KAAKwD,MACnC1C,GAAIuD,IAAI3E,GAAM6D,CAAAA,IACdzC,GAAIwD,IAAI5E,GAAM6D,CAAAA,EAAGgB,KAAK7E,EAAAA,IADJoB,GAAI0D,IAAI9E,GAAM6D,GAAG,CAAC7D,EAAAA,CAAAA;MAE7C,CAAA,GAEO+E,MAAM7F,KAAKkC,GAAI4D,QAAAA,CAAAA,EACjBC,KAAK,SAACC,IAAGC,IAAAA;AAAC,eAAKD,GAAE,CAAA,IAAKC,GAAE,CAAA;MAAG,CAAA,EAC3B/D,IAAI,SAAAgE,IAAmBC,IAAGC,IAAAA;AAAW,YAI9BC,IAIAC,IARD3B,KAACuB,GAAA,CAAA,GAAEK,KAAWL,GAAA,CAAA,GACXM,IAAYJ,GAAOD,KAAI,CAAA,GACvBM,IAAYL,GAAOD,KAAI,CAAA;AAU7B,eANKE,KADAG,IACK7B,MAAKA,KAAI6B,EAAU,CAAA,KAAM,IADd7B,IAKhB2B,KADAG,IACa9B,KAAI0B,MAAMI,EAAU,CAAA,IAAK9B,MAAK,IADnBU,KAAQgB,IAG9B,EACHpF,IAAE,WAAWmE,KAAW,MAAIT,IAC5B0B,IAAAA,IACA1B,GAAAA,IACAM,IAAI,GACJL,GAAG,GACHS,OAAOiB,IACPhB,QAAAA,IACArD,QAAQsE,GAAYG,QAAAA,EAAAA;MAE5B,CAAA;IACR;AAAO,QAAqB,QAAjBnD,IAAsB;AAC7B,UAAMrB,KAAM,oBAAIqD;AAOhB,aANAtD,GAAOuD,QAAQ,SAAA1E,IAAAA;AACU,iBAAjBA,GAAMM,KAAKuD,KAA+B,SAAjB7D,GAAMM,KAAKwD,MACnC1C,GAAIuD,IAAI3E,GAAM8D,CAAAA,IACd1C,GAAIwD,IAAI5E,GAAM8D,CAAAA,EAAGe,KAAK7E,EAAAA,IADJoB,GAAI0D,IAAI9E,GAAM8D,GAAG,CAAC9D,EAAAA,CAAAA;MAE7C,CAAA,GAEO+E,MAAM7F,KAAKkC,GAAI4D,QAAAA,CAAAA,EACjBC,KAAK,SAACC,IAAGC,IAAAA;AAAC,eAAKD,GAAE,CAAA,IAAKC,GAAE,CAAA;MAAG,CAAA,EAC3B/D,IAAI,SAAAyE,IAAmBR,IAAGC,IAAAA;AAAW,YAI9BnB,IAIA2B,IARDhC,KAAC+B,GAAA,CAAA,GAAEJ,KAAWI,GAAA,CAAA,GACXH,KAAYJ,GAAOD,KAAI,CAAA,GACvBM,IAAYL,GAAOD,KAAI,CAAA;AAU7B,eANKlB,KADAuB,KACK5B,MAAKA,KAAI4B,GAAU,CAAA,KAAM,IADd5B,IAKhBgC,KADAH,IACc7B,KAAIK,MAAMwB,EAAU,CAAA,IAAK7B,MAAK,IADnBU,KAASL,IAGhC,EACHhE,IAAI2D,IACJyB,IAAI,GACJ1B,GAAG,GACHM,IAAAA,IACAL,GAAAA,IACAS,OAAAA,IACAC,QAAQsB,IACR3E,QAAQsE,GAAYG,QAAAA,EAAAA;MAE5B,CAAA;IACR;AAEA,WAAO,CAAA;EACX,GAAG,CAACtB,IAAa7B,IAAc+B,IAAQrD,IAAQoD,EAAAA,CAAAA;AACnD;AA3IA,IA6IawB,KAAwB;AA7IrC,IA+IaC,KAAU,SAAHC,IAAAA;AAoDf,MAnDD3F,KAAI2F,GAAJ3F,MAAI4F,KAAAD,GACJ5H,QAAQ8H,KAAAA,WAAUD,KAAG9H,GAAmBC,SAAM6H,IAC9CE,KAAOH,GAAPG,SAAOC,KAAAJ,GACP1H,QAAQ+H,KAAAA,WAAUD,KAAGjI,GAAmBG,SAAM8H,IAC9CE,IAAON,GAAPM,SACAhC,IAAK0B,GAAL1B,OACAC,IAAMyB,GAANzB,QAAMgC,IAAAP,GACNtH,QAAAA,KAAAA,WAAM6H,IAAGpI,GAAmBO,SAAM6H,GAAAC,KAAAR,GAClCvH,OAAAA,IAAAA,WAAK+H,KAAGrI,GAAmBM,QAAK+H,IAAAC,KAAAT,GAChC1G,mBAAAA,KAAAA,WAAiBmH,KAAGtI,GAAmBmB,oBAAiBmH,IAAAC,KAAAV,GACxDhH,YAAAA,KAAAA,WAAU0H,KAAGvI,GAAmBa,aAAU0H,IAAAC,KAAAX,GAC1C7G,kBAAAA,KAAAA,WAAgBwH,KAAGxI,GAAmBgB,mBAAgBwH,IAAAC,KAAAZ,GAGtDxD,cAAAA,KAAAA,WAAYoE,KAAGjF,GAAgBa,eAAYoE,IAAAC,KAAAb,GAI3CnD,kBAAAA,KAAAA,WAAgBgE,KAAGlF,GAAgBkB,mBAAgBgE,IAkC5CxC,SAAeyC,cAAAA,cAASC,gBAAAA,SAASjB,EAAAA,CAAAA,EAAtB,CAAA,GACZkB,KAAUC,GAAkBd,EAAAA,GAC5Be,KAAUD,GAAkBX,CAAAA,GAC5Ba,KAAWC,GAAqB1I,IAAQ,IAAA,GACxCU,KAAQ0B,EAAAA,GACRuG,KAAgBC,GAAkBtI,IAAYI,EAAAA,GAC9CmI,IAAsBD,GAAkBnI,IAAkBC,EAAAA,GAChEoI,SAAkCV,cAAAA,UAASjE,QAAAA,KAAAA,KAAoB,CAAA,CAAA,GAAxD4E,KAASD,GAAA,CAAA,GAAEE,KAAYF,GAAA,CAAA,GAE9BG,SAIInE,cAAAA,SACA,WAAA;AAAA,WACIoE,IACIvH,GAAKwH,OAAO,SAAAC,IAAAA;AAAI,aAAA,OAAIL,GAAUM,QAAQD,GAAK5H,EAAAA;IAAoC,CAAA,GAC/EgG,IACAG,IACA/B,GACAC,CAAAA;EACH,GACL,CAAClE,IAAMoH,IAAWvB,IAAYG,IAAY/B,GAAOC,CAAAA,CAAAA,GAZjDnG,KAAMuJ,GAANvJ,QACAE,KAAMqJ,GAANrJ,QACQ0J,KAASL,GAAjBM,QAaJC,QAA+B1E,cAAAA,SAAQ,WAAA;AACnC,QAAM2E,KAAgB9H,GAAKc,IAAI,SAAAiH,IAAAA;AAAU,aAAK,EAC1ClI,IAAIkI,GAAWlI,IACfmI,OAAK,KAAKD,GAAWlI,IACrBO,OAAO0G,GAASiB,EAAAA,EAAAA;IACnB,CAAA,GAEKH,KAASE,GACVhH,IAAI,SAAAmH,IAAAA;AAAK,aAAA1G,EACHoG,CAAAA,GAAAA,GAAUO,KAAK,SAAAH,IAAAA;AAAU,eAAIA,GAAWlI,OAAOoI,GAAMpI;MAAAA,CAAAA,GAAG,EAC3DO,OAAO6H,GAAM7H,MAAAA,CAAAA;IAAK,CAAA,EAErBoH,OAAO,SAAAC,IAAAA;AAAI,aAAIU,QAAQV,GAAK5H,EAAAA;IAAAA,CAAAA;AASjC,WAAO,EAAEuI,YAPUN,GACdhH,IAAI,SAAA2G,IAAAA;AAAI,aAAAlG,EAAAA,CAAAA,GACFkG,IAAI,EACPY,QAAAA,CAAST,GAAOM,KAAK,SAAAH,IAAAA;AAAU,eAAIA,GAAWlI,OAAO4H,GAAK5H;MAAE,CAAA,EAAA,CAAA;IAAC,CAAA,EAEhEyF,QAAAA,GAEgBsC,QAAAA,GAAAA;EACxB,GAAE,CAAC5H,IAAM2H,IAAWb,EAAAA,CAAAA,GAtBbsB,IAAUP,EAAVO,YAAYR,KAAMC,EAAND,QAwBdU,QAAeC,cAAAA,aAAY,SAAC1I,IAAAA;AAC9BwH,IAAAA,GAAa,SAAAmB,IAAAA;AAAK,aACdA,GAAMd,QAAQ7H,EAAAA,IAAAA,KAAW2I,GAAMhB,OAAO,SAAAC,IAAAA;AAAI,eAAIA,OAAS5H;MAAE,CAAA,IAAA,CAAA,EAAC4I,OAAOD,IAAK,CAAE3I,EAAAA,CAAAA;IAAG,CAAA;EAElF,GAAE,CAAA,CAAA,GAEGgB,IAzPV,SAAkB6H,IAAAA;AAYf,QAXCd,KAAMc,GAANd,QACAZ,KAAa0B,GAAb1B,eACAE,KAAmBwB,GAAnBxB,qBACAP,KAAO+B,GAAP/B,SACAE,KAAO6B,GAAP7B;AAQA,eAAO1D,cAAAA,SAAQ,WAAA;AACX,aAAOyE,GAAOe,OAAO,SAACC,IAAKb,IAAYc,IAAAA;AACnC,eAAAJ,CAAAA,EAAAA,OACOG,IACAb,GAAW/H,KACTwH,OAAO,SAAAS,IAAAA;AAAK,iBAAyB,SAArBA,GAAMa,SAASvF,KAAmC,SAArB0E,GAAMa,SAAStF;QAAU,CAAA,EACtE1C,IAAI,SAACmH,IAAOc,IAAAA;AACT,cAAMrJ,KAGF,EACAG,IAAOkI,GAAWlI,KAAAA,MAAMkJ,IACxBA,eAAAA,IACAC,UAAUJ,GAAIK,SAASF,IACvBF,aAAAA,IACA3H,UAAU6G,GAAWlI,IACrBQ,aAAa0H,GAAW3H,OACxBmD,GAAG0E,GAAMa,SAASvF,GAClBC,GAAGyE,GAAMa,SAAStF,GAClBxD,MAAIuB,EACG0G,CAAAA,GAAAA,GAAMjI,MAAI,EACbC,YAAY0G,GAAQsB,GAAMjI,KAAKuD,CAAAA,GAC/BrD,YAAY2G,GAAQoB,GAAMjI,KAAKwD,CAAAA,EAAAA,CAAAA,EAAAA;AAWvC,iBARA9D,GAAMU,QAAQ4G,GAAc,EACxBY,QAAQG,IACRrI,OAAOA,GAAAA,CAAAA,GAEXA,GAAMwJ,cAAchC,GAChBxH,EAAAA,GAGGA;QACV,CAAA,CAAA;MAEZ,GAAE,CAAA,CAAA;IACP,GAAG,CAACkI,IAAQZ,IAAeE,IAAqBP,IAASE,EAAAA,CAAAA;EAC7D,EAsMqC,EAC7Be,QAAAA,IACAZ,eAAAA,IACAE,qBAAAA,GACAP,SAAAA,IACAE,SAAAA,GAAAA,CAAAA,GAGE7B,KAASlB,GAAkB,EAC7BE,aAAAA,IACA7B,cAAAA,IACAtB,QAAAA,GACAoD,OAAAA,GACAC,QAAAA,EAAAA,CAAAA;AAUJ,SAAO,EACHkE,YAAAA,GACAE,cAAAA,GACAa,eAVkBjG,GAAiB9E,CAAAA,GAWnCgL,eAVkB1F,GAAiC,EACnDtF,OAAAA,GACAH,QAAQA,IACRgB,mBAAAA,GAAAA,CAAAA,GAQA6H,UAAAA,IACAc,QAAAA,IACA7J,QAAQA,IACRE,QAAQA,IACR+G,QAAAA,IACAnE,QAAAA,EAAAA;AAER;AA9RA,IC1DMwI,KAAW,SAAH5J,IAAAA;AAYR,MAXFmC,KAAanC,GAAbmC,eACA1C,KAAWO,GAAXP,aACAkB,KAAKX,GAALW,OACAqB,KAAIhC,GAAJgC,MACA6H,KAAI7J,GAAJ6J,MAQAC,KAA0CC,GAAAA,GAAlC/G,KAAO8G,GAAP9G,SAAiBgH,KAAYF,GAApBG,QAEXC,KAAeC,GAAgBN,EAAAA,GAC/BO,IAAgBC,UAAU,EAC5B1J,OAAAA,IACAsJ,QAAQD,IACRM,WAAAA,CAAYtH,GAAAA,CAAAA;AAGhB,aACI9C,oBAAAA,KAACqK,SAASV,MAAI,EACVhG,GAAGqG,IACHlI,MAAMA,MAAcoI,EAAczJ,OAClC6J,aAAa/K,IACbgL,aAAa,GACblJ,OAAO,EACHmJ,cAAcvI,GAAAA,EAAAA,CAAAA;AAI9B;ADyBA,ICGawI,UAAQ5K,cAAAA,MA1BI,SAAHkJ,IAAAA;AAUhB,MATFU,KAAaV,GAAbU,eACAlK,KAAWwJ,GAAXxJ,aACA0C,KAAa8G,GAAb9G,eAQMyI,KAPA3B,GAANd,OAO8BrH,MAAM,CAAA,EAAG+E,QAAAA;AAEvC,aACI3F,oBAAAA,KAAA,KAAA,EAAAI,UACKsK,GAAevJ,IAAI,SAAAiH,IAAAA;AAAU,eAC1BpI,oBAAAA,KAAC0J,IAAQ9H,EAAA,EAEL+H,MAAMF,GAAcrB,GAAW/H,KAAKc,IAAI,SAAAwC,IAAAA;AAAC,aAAIA,GAAEwF;IAAAA,CAAAA,CAAAA,EAAAA,GAAYvH,EAAA,EACrDrC,aAAAA,IAAa0C,eAAAA,GAAAA,GAAkBmG,EAAAA,CAAAA,GAAU,KAFvCA,GAAWlI,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAOvC,CAAA;ADDA,IEzCayK,UAAY9K,cAAAA,MAjBW,SAAHC,IAAAA;AAU3B,MATF0J,KAAa1J,GAAb0J,eACAtI,KAAMpB,GAANoB,QACAT,KAAKX,GAALW,OACAmK,KAAS9K,GAAT8K,WAOMjB,SAAOnG,cAAAA,SAAQ,WAAA;AAAA,WAAMgG,GAActI,EAAAA;EAAO,GAAE,CAACsI,IAAetI,EAAAA,CAAAA,GAC5D8I,KAAeC,GAAgBN,EAAAA;AAErC,aAAO3J,oBAAAA,KAACqK,SAASV,MAAI,EAAChG,GAAGqG,IAAclI,MAAK,QAAOyI,aAAaK,IAAWC,QAAQpK,GAAAA,CAAAA;AACvF,CAAA;AF2CA,IGhCaqK,SAAQjL,cAAAA,MA3BW,SAAHC,IAAAA;AAQvB,MAPFmI,KAAMnI,GAANmI,QACAuB,KAAa1J,GAAb0J,eACA5K,KAASkB,GAATlB;AAMA,aACIoB,oBAAAA,KAAA+K,oBAAAA,UAAA,EAAA3K,UACK6H,GACIrH,MAAM,CAAA,EACN+E,QAAAA,EACAxE,IAAI,SAAA4H,IAAAA;AAAA,QAAG7I,KAAE6I,GAAF7I,IAAIG,KAAI0I,GAAJ1I,MAAMI,KAAKsI,GAALtI;AAAK,eACnBT,oBAAAA,KAAC2K,KAAS,EAENzJ,QAAQb,GAAKc,IAAI,SAAAwC,IAAAA;AAAC,aAAIA,GAAEwF;IAAAA,CAAAA,GACxBK,eAAeA,IACf/I,OAAOA,IACPmK,WAAWhM,GAAAA,GAJNsB,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAS7B,CAAA;AHkCA,IIyGa8K,SAAanL,cAAAA,MApKW,SAAHC,IAAAA;AAoC5B,MAnCFc,KAAKd,GAALc,OACAyE,KAAMvF,GAANuF,QACAxE,KAAIf,GAAJe,MACAoK,KAAKnL,GAALmL,OACArL,KAAOE,GAAPF,SACAsL,KAASpL,GAAToL,WACAC,KAAUrL,GAAVqL,YACAC,IAAYtL,GAAZsL,cACAC,IAAWvL,GAAXuL,aACAC,IAAYxL,GAAZwL,cACAC,IAAWzL,GAAXyL,aACAC,IAAS1L,GAAT0L,WACAC,KAAO3L,GAAP2L,SACAC,KAAa5L,GAAb4L,eACAC,IAAY7L,GAAZ6L,cACAC,KAAW9L,GAAX8L,aACAC,KAAU/L,GAAV+L,YAoBAC,KAA8CC,EAAAA,GAAtCC,KAAoBF,GAApBE,sBAAsBC,KAAWH,GAAXG,aAExBC,SAAmBtD,cAAAA,aACrB,SAACuD,IAAAA;AACGH,IAAAA,OAAqBI,cAAAA,eAAcxM,IAAS,EAAEgB,OAAAA,IAAOC,MAAAA,GAAAA,CAAAA,GAASsL,IAAO,OAAA,GACrEhB,GAAWvK,EAAAA,GAAAA,QACXwK,KAAAA,EAAexK,IAAOuL,EAAAA;EAC1B,GACA,CAACH,IAAsBpM,IAASgB,IAAOC,IAAMsK,IAAYC,CAAAA,CAAAA,GAGvDiB,SAAkBzD,cAAAA,aACpB,SAACuD,IAAAA;AACGH,IAAAA,OAAqBI,cAAAA,eAAcxM,IAAS,EAAEgB,OAAAA,IAAOC,MAAAA,GAAAA,CAAAA,GAASsL,IAAO,OAAA,GAAA,QACrEd,KAAAA,EAAczK,IAAOuL,EAAAA;EACzB,GACA,CAACH,IAAsBpM,IAASgB,IAAOC,IAAMwK,CAAAA,CAAAA,GAG3CiB,SAAmB1D,cAAAA,aACrB,SAACuD,IAAAA;AACGF,IAAAA,GAAAA,GACAd,GAAW,IAAA,GAAA,QACXG,KAAAA,EAAe1K,IAAOuL,EAAAA;EACzB,GACD,CAACF,IAAad,IAAYG,GAAc1K,EAAAA,CAAAA,GAGtC2L,SAAkB3D,cAAAA,aACpB,SAACuD,IAAAA;AAAAA,YACGZ,KAAAA,EAAc3K,IAAOuL,EAAAA;EACzB,GACA,CAACvL,IAAO2K,CAAAA,CAAAA,GAGNiB,SAAgB5D,cAAAA,aAClB,SAACuD,IAAAA;AAAAA,YACGX,KAAAA,EAAY5K,IAAOuL,EAAAA;EACvB,GACA,CAACvL,IAAO4K,CAAAA,CAAAA,GAGNiB,SAAc7D,cAAAA,aAChB,SAACuD,IAAAA;AAAAA,YACGV,MAAAA,GAAU7K,IAAOuL,EAAAA;EACrB,GACA,CAACvL,IAAO6K,EAAAA,CAAAA,GAGNiB,SAAoB9D,cAAAA,aACtB,SAACuD,IAAAA;AAAAA,YACGT,MAAAA,GAAgB9K,IAAOuL,EAAAA;EAC3B,GACA,CAACvL,IAAO8K,EAAAA,CAAAA,GAGNiB,SAAoB/D,cAAAA,aACtB,SAACuD,IAAAA;AACGH,IAAAA,OAAqBI,cAAAA,eAAcxM,IAAS,EAAEgB,OAAAA,IAAOC,MAAAA,GAAAA,CAAAA,GAASsL,IAAO,OAAA,GACrEhB,GAAWvK,EAAAA,GAAAA,QACX+K,KAAAA,EAAe/K,IAAOuL,EAAAA;EAC1B,GACA,CAACtL,IAAM8K,GAAcR,IAAYa,IAAsBpL,IAAOhB,EAAAA,CAAAA,GAG5DgN,SAAmBhE,cAAAA,aACrB,SAACuD,IAAAA;AAGG,QAAMU,KAAaV,GAAMW,QAAQ,CAAA,GAC3BC,KAAkBC,SAASC,iBAC7BJ,GAAWK,SACXL,GAAWM,OAAAA,GAGTC,KAAiC,QAAfL,KAAAA,SAAAA,GAAiBM,aAAa,UAAA;AACtD,QAAID,IAAiB;AAEjB,UAAMxM,KAAQyE,GAAOkD,KAAK,SAAA3H,IAAAA;AAAK,eAAIA,GAAMV,OAAOkN;MAAAA,CAAAA;AAC5CxM,MAAAA,OACAoL,OAAqBI,cAAAA,eAAcxM,IAAS,EAAEgB,OAAAA,IAAOC,MAAAA,GAAAA,CAAAA,GAASsL,IAAO,OAAA,GACrEhB,GAAWvK,EAAAA;IAEnB;AAAA,YAIAgL,MAAAA,GAAchL,IAAOuL,EAAAA;EACzB,GACA,CAACtL,IAAM+K,IAAaT,IAAYa,IAAsBpL,IAAOyE,IAAQzF,EAAAA,CAAAA,GAGnE0N,SAAmB1E,cAAAA,aACrB,SAACuD,IAAAA;AACGF,IAAAA,GAAAA,GACAd,GAAW,IAAA,GAAA,QACXU,MAAAA,GAAajL,IAAOuL,EAAAA;EACvB,GACD,CAACF,IAAad,IAAYU,IAAYjL,EAAAA,CAAAA;AAG1C,aACIZ,oBAAAA,KAAA,QAAA,EACI4D,GAAGhD,GAAM0E,IACTzB,GAAGjD,GAAMsD,IACTI,OAAO1D,GAAM0D,OACbC,QAAQ3D,GAAM2D,QACdsG,QAAO,OACPN,aAAaU,KAAQ,IAAI,GACzBsC,eAAe,MACfzL,MAAK,OACLwI,aAAaY,MAAaD,KAAQ,OAAO,GACzCG,cAAcc,IACdb,aAAagB,IACbf,cAAcgB,IACdf,aAAagB,IACbf,WAAWgB,IACXf,SAASgB,IACTf,eAAegB,IACff,cAAcgB,IACdf,aAAagB,IACbf,YAAYyB,IACZ,YAAU1M,GAAMV,GAAAA,CAAAA;AAG5B,CAAA;AJvGA,IKIasN,SAAS3N,cAAAA,MA/DW,SAAHC,IAAAA;AAkCxB,MAjCFuF,KAAMvF,GAANuF,QACAxE,KAAIf,GAAJe,MACAoK,KAAKnL,GAALmL,OACArL,KAAOE,GAAPF,SACA6N,KAAO3N,GAAP2N,SACAtC,KAAUrL,GAAVqL,YACAC,KAAYtL,GAAZsL,cACAC,KAAWvL,GAAXuL,aACAC,KAAYxL,GAAZwL,cACAC,IAAWzL,GAAXyL,aACAC,IAAS1L,GAAT0L,WACAC,IAAO3L,GAAP2L,SACAC,IAAa5L,GAAb4L,eACAC,IAAY7L,GAAZ6L,cACAC,KAAW9L,GAAX8L,aACAC,KAAU/L,GAAV+L;AAmBA,aACI7L,oBAAAA,KAAA+K,oBAAAA,UAAA,EAAA3K,UACKiF,GAAOlE,IAAI,SAAAP,IAAAA;AAAK,eACbZ,oBAAAA,KAACgL,IAAU,EAEPpK,OAAOA,IACPyE,QAAQA,IACRxE,MAAMA,IACNoK,OAAOA,IACPrL,SAASA,IACTuL,YAAYA,IACZD,WAAuB,SAAZuC,MAAoBA,GAAQvN,OAAOU,GAAMV,IACpDkL,cAAcA,IACdC,aAAaA,IACbC,cAAcA,IACdC,aAAaA,GACbC,WAAWA,GACXC,SAASA,GACTC,eAAeA,GACfC,cAAcA,GACdC,aAAaA,IACbC,YAAYA,GAAAA,GAjBPjL,GAAMV,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAsB/B,CAAA;ALFA,IMmDawN,SAAS7N,cAAAA,MA3GI,SAAHC,IAAAA;AAkCjB,MAjCFoB,KAAMpB,GAANoB,QACAyM,KAAM7N,GAAN6N,QACAC,KAAI9N,GAAJ8N,MACAC,KAAW/N,GAAX+N,aACAC,KAAWhO,GAAXgO,aACAzF,KAAKvI,GAALuI,OACA0F,KAAYjO,GAAZiO,cACA9K,KAAWnD,GAAXmD,aACA+K,IAAelO,GAAfkO,iBACApO,IAAOE,GAAPF,SACAqO,IAAMnO,GAANmO,QACAC,KAASpO,GAAToO,WACAC,KAAcrO,GAAdqO,gBACAC,IAAetO,GAAfsO,iBACAC,KAAUvO,GAAVuO,YACAC,KAAYxO,GAAZwO,cAmBMC,KAAWC,GAAkBnG,EAAAA,GAEnCyD,KAAuCC,EAAAA,GAA/B0C,KAAa3C,GAAb2C,eAAexC,KAAWH,GAAXG,aAGjByC,KAAexN,GAChBN,MAAM,CAAA,EACNoE,KAAK,SAACC,IAAGC,IAAAA;AAAC,WAAKD,GAAEmE,gBAAgBlE,GAAEkE;EAAa,CAAA,EAChDpE,KAAK,SAACC,IAAGC,IAAAA;AAAC,WAAKA,GAAEgE,cAAcjE,GAAEiE;EAAW,CAAA,EAC5C/H,IAAI,SAAApB,IAAAA;AACD,WAAO,EACHG,IAAIH,GAAMG,IACV0D,GAAG7D,GAAM6D,GACTC,GAAG9D,GAAM8D,GACTyE,OAAOvI,GAAMM,MACbyB,MAAM/B,GAAMU,OACZoK,QAAQ9K,GAAMwJ,aACdlB,OAAOyF,KAAcS,GAASxO,EAAAA,IAAS,MACvCmO,WAAWA,KAAYA,GAAUnO,EAAAA,IAAAA,QACjCoO,gBAAgBA,KAAiBA,GAAepO,EAAAA,IAAAA,QAChDqO,iBAAiBA,IAAkBA,EAAgBrO,EAAAA,IAAAA,QACnDsO,YAAYA,KAAaA,GAAWtO,EAAAA,IAAAA,QACpCuO,cAAcA,KAAeA,GAAavO,EAAAA,IAAAA,QAC1C4O,SAAS1L,KACH,WAAA;AACI+K,QAAgBjO,EAAAA,GAChB0O,OACIrC,cAAAA,eAAcxM,GAAS,EAAEG,OAAAA,GAAAA,CAAAA,GACzB,CAACkO,EAAOW,OAAO7O,GAAM6D,GAAGqK,EAAOY,MAAM9O,GAAM8D,CAAAA,GAC3C,KAAA;IAER,IAAA,QAENiL,QAAQ7L,KACF,WAAA;AACI+K,QAAgB,IAAA,GAChB/B,GAAAA;IACJ,IAAA,OACA8C;EAEd,CAAA;AAEJ,aACI/O,oBAAAA,KAAA,KAAA,EAAAI,UACKsO,GAAavN,IAAI,SAAApB,IAAAA;AAAK,eACnBC,oBAAAA,KAACgP,IAAQ,EAELpL,GAAG7D,GAAM6D,GACTC,GAAG9D,GAAM8D,GACTyE,OAAOvI,GAAMuI,OACbqF,QAAQA,IACRC,MAAMA,IACNnN,OAAOV,GAAM+B,MACb+L,aAAaA,IACbtE,aAAaxJ,GAAM8K,QACnBxC,OAAOtI,GAAMsI,OACb0F,cAAcA,IACdG,WAAWnO,GAAMmO,WACjBC,gBAAgBpO,GAAMoO,gBACtBC,iBAAiBrO,GAAMqO,iBACvBC,YAAYtO,GAAMsO,YAClBC,cAAcvO,GAAMuO,cACpBrL,aAAaA,IACb0L,SAAS5O,GAAM4O,SACfG,QAAQ/O,GAAM+O,QACdG,QAAM,gBAAgBlP,GAAMG,GAAAA,GAnBvBH,GAAMG,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAwB/B,CAAA;ANjDA,IOkGagP,SAAOrP,cAAAA,MA3JI,SAAHC,IAAAA;AAsCf,MArCFoB,KAAMpB,GAANoB,QACAoD,KAAKxE,GAALwE,OACAC,KAAMzE,GAANyE,QACA0J,KAAMnO,GAANmO,QACA9C,KAAUrL,GAAVqL,YACAC,KAAYtL,GAAZsL,cACAC,KAAWvL,GAAXuL,aACAC,IAAYxL,GAAZwL,cACAC,IAAWzL,GAAXyL,aACAC,IAAS1L,GAAT0L,WACAC,IAAO3L,GAAP2L,SACAC,IAAa5L,GAAb4L,eACAC,KAAY7L,GAAZ6L,cACAC,KAAW9L,GAAX8L,aACAC,IAAU/L,GAAV+L,YACAjM,KAAOE,GAAPF,SACAqL,KAAKnL,GAALmL,OACArI,KAAoB9C,GAApB8C,sBAqBAkJ,KAAuCC,EAAAA,GAA/B0C,KAAa3C,GAAb2C,eAAexC,KAAWH,GAAXG,aAEjBC,SAAmBtD,cAAAA,aACrB,SAAC7I,IAAsBoM,IAAAA;AACnBsC,IAAAA,OACIrC,cAAAA,eAAcxM,IAAS,EAAEG,OAAAA,GAAAA,CAAAA,GACzB,CAACA,GAAM6D,IAAIqK,GAAOW,MAAM7O,GAAM8D,IAAIoK,GAAOY,GAAAA,GACzC,KAAA,GAAA,QAEJzD,MAAAA,GAAerL,IAAOoM,EAAAA;EACzB,GACD,CAACsC,IAAe7O,IAASwL,IAAc6C,EAAAA,CAAAA,GAGrC5B,SAAkBzD,cAAAA,aACpB,SAAC7I,IAAsBoM,IAAAA;AACnBsC,IAAAA,OACIrC,cAAAA,eAAcxM,IAAS,EAAEG,OAAAA,GAAAA,CAAAA,GACzB,CAACA,GAAM6D,IAAIqK,GAAOW,MAAM7O,GAAM8D,IAAIoK,GAAOY,GAAAA,GACzC,KAAA,GAAA,QAEJxD,MAAAA,GAActL,IAAOoM,EAAAA;EACzB,GACA,CAACsC,IAAe7O,IAASqO,GAAOW,MAAMX,GAAOY,KAAKxD,EAAAA,CAAAA,GAGhDiB,SAAmB1D,cAAAA,aACrB,SAAC7I,IAAsBoM,IAAAA;AACnBF,IAAAA,GAAAA,GAAAA,QACAX,KAAAA,EAAevL,IAAOoM,EAAAA;EAC1B,GACA,CAACF,IAAaX,CAAAA,CAAAA,GAGZiB,SAAkB3D,cAAAA,aACpB,SAAC7I,IAAsBoM,IAAAA;AAAAA,YACnBZ,KAAAA,EAAcxL,IAAOoM,EAAAA;EACzB,GACA,CAACZ,CAAAA,CAAAA,GAGCiB,SAAgB5D,cAAAA,aAClB,SAAC7I,IAAsBoM,IAAAA;AAAAA,YACnBX,KAAAA,EAAYzL,IAAOoM,EAAAA;EACvB,GACA,CAACX,CAAAA,CAAAA,GAGCiB,SAAc7D,cAAAA,aAChB,SAAC7I,IAAsBoM,IAAAA;AAAAA,YACnBV,KAAAA,EAAU1L,IAAOoM,EAAAA;EACrB,GACA,CAACV,CAAAA,CAAAA,GAGCiB,SAAoB9D,cAAAA,aACtB,SAAC7I,IAAsBoM,IAAAA;AAAAA,YACnBT,KAAAA,EAAgB3L,IAAOoM,EAAAA;EAC3B,GACA,CAACT,CAAAA,CAAAA,GAGCyD,SAAmBvG,cAAAA,aACrB,SAAC7I,IAAsBoM,IAAAA;AACnBsC,IAAAA,OACIrC,cAAAA,eAAcxM,IAAS,EAAEG,OAAAA,GAAAA,CAAAA,GACzB,CAACA,GAAM6D,IAAIqK,GAAOW,MAAM7O,GAAM8D,IAAIoK,GAAOY,GAAAA,GACzC,KAAA,GAAA,QAEJlD,MAAAA,GAAe5L,IAAOoM,EAAAA;EAC1B,GACA,CAAC8B,GAAOW,MAAMX,GAAOY,KAAKlD,IAAc8C,IAAe7O,EAAAA,CAAAA,GAGrDwP,SAAkBxG,cAAAA,aACpB,SAAC7I,IAAsBoM,IAAAA;AACnBsC,IAAAA,OACIrC,cAAAA,eAAcxM,IAAS,EAAEG,OAAAA,GAAAA,CAAAA,GACzB,CAACA,GAAM6D,IAAIqK,GAAOW,MAAM7O,GAAM8D,IAAIoK,GAAOY,GAAAA,GACzC,KAAA,GAAA,QAEJjD,MAAAA,GAAc7L,IAAOoM,EAAAA;EACzB,GACA,CAAC8B,GAAOW,MAAMX,GAAOY,KAAKjD,IAAa6C,IAAe7O,EAAAA,CAAAA,GAGpDyP,SAAiBzG,cAAAA,aACnB,SAAC7I,IAAsBoM,IAAAA;AACnBF,IAAAA,GAAAA,GAAAA,QACAJ,KAAAA,EAAa9L,IAAOoM,EAAAA;EACxB,GACA,CAACN,GAAYI,EAAAA,CAAAA;AAGjB,aACIjM,oBAAAA,KAACsP,IAAQ,EACLC,OAAOrO,IACPoD,OAAOA,IACPC,QAAQA,IACR4G,YAAYA,IACZC,cAAcc,IACdb,aAAagB,IACbf,cAAcgB,IACdf,aAAagB,IACbf,WAAWgB,IACXf,SAASgB,IACTf,eAAegB,IACff,cAAcwD,IACdvD,aAAawD,IACbvD,YAAYwD,IACZzM,sBAAsBA,IACtBqI,OAAOA,GAAAA,CAAAA;AAGnB,CAAA;APhGA,IOgGA,KAAA,CAAA,iBAAA,WAAA,gBAAA,SAAA,eAAA;ACzHA,SAASuE,GACLC,IAAAA;AAEA,MACIpP,KAuEAoP,GAvEApP,MAAIqP,KAuEJD,GAtEArR,QAAQ8H,KAAAA,WAAUwJ,KAAG/N,GAAgBvD,SAAMsR,IAC3CvJ,KAqEAsJ,GArEAtJ,SAAOwJ,KAqEPF,GApEAnR,QAAQ+H,KAAAA,WAAUsJ,KAAGhO,GAAgBrD,SAAMqR,IAC3CrJ,KAmEAmJ,GAnEAnJ,SAAOsJ,IAmEPH,GAlEAhR,OAAAA,IAAAA,WAAKmR,IAAGjO,GAAgBlD,QAAKmR,GACrBC,IAiERJ,GAjEAxB,QACA3J,IAgEAmL,GAhEAnL,OACAC,IA+DAkL,GA/DAlL,QAAMuL,KA+DNL,GA9DA/Q,QAAAA,KAAAA,WAAMoR,KAAGnO,GAAgBjD,SAAMoR,IAAAC,KA8D/BN,GA7DA7Q,WAAAA,KAAAA,WAASmR,KAAGpO,GAAgB/C,YAASmR,IAAAC,KA6DrCP,GA5DA5Q,QAAAA,KAAAA,WAAMmR,KAAGrO,GAAgB9C,SAAMmR,IAAAC,KA4D/BR,GA3DApQ,YAAAA,KAAAA,WAAU4Q,KAAGtO,GAAgBtC,aAAU4Q,IAAAC,KA2DvCT,GA1DAnQ,mBAAAA,KAAAA,WAAiB4Q,KAAGvO,GAAgBrC,oBAAiB4Q,IAAAC,KA0DrDV,GAzDAlQ,aAAAA,KAAAA,WAAW4Q,KAAGxO,GAAgBpC,cAAW4Q,IAAAC,IAyDzCX,GAxDAxN,eAAAA,KAAAA,WAAamO,IAAGzO,GAAgBM,gBAAamO,GAAAC,KAwD7CZ,GAvDA3Q,cAAAA,KAAAA,WAAYuR,KAAG1O,GAAgB7C,eAAYuR,IAC3CC,KAsDAb,GAtDAa,aAAWC,KAsDXd,GArDA1Q,WAAAA,KAAAA,WAASwR,KAAG5O,GAAgB5C,YAASwR,IAAAC,KAqDrCf,GApDAzQ,YAAAA,KAAAA,WAAUwR,KAAG7O,GAAgB3C,aAAUwR,IAAAC,KAoDvChB,GAnDAvQ,kBAAAA,KAAAA,WAAgBuR,KAAG9O,GAAgBzC,mBAAgBuR,IAAAC,IAmDnDjB,GAlDAtQ,kBAAAA,IAAAA,WAAgBuR,IAAG/O,GAAgBxC,mBAAgBuR,GAAAC,KAkDnDlB,GA/CA1N,kBAAAA,IAAAA,WAAgB4O,KAAGhP,GAAgBI,mBAAgB4O,IAAAC,IA+CnDnB,GA9CAzN,YAAAA,KAAAA,WAAU4O,IAAGjP,GAAgBK,aAAU4O,GACvCC,MA6CApB,GA7CAoB,mBAAiBC,MA6CjBrB,GA5CAjQ,aAAAA,MAAAA,WAAWsR,MAAGnP,GAAgBnC,cAAWsR,KACzCC,MA2CAtB,GA3CAsB,aAAWC,MA2CXvB,GA1CAhQ,aAAAA,MAAAA,WAAWuR,MAAGrP,GAAgBlC,cAAWuR,KACzCC,MAyCAxB,GAzCAwB,aACA/O,MAwCAuN,GAxCAvN,SACAC,MAuCAsN,GAvCAtN,WAAS+O,MAuCTzB,GAtCArN,YAAAA,MAAAA,WAAU8O,MAAGvP,GAAgBS,aAAU8O,KAAAC,MAsCvC1B,GArCAnN,UAAAA,MAAAA,WAAQ6O,MAAGxP,GAAgBW,WAAQ6O,KAAAC,MAqCnC3B,GApCA5N,MAAAA,MAAAA,WAAIuP,MAAGzP,GAAgBE,OAAIuP,KAAAC,MAoC3B5B,GAnCA3N,MAAAA,MAAAA,WAAIuP,MAAG1P,GAAgBG,OAAIuP,KAC3BC,MAkCA7B,GAlCA6B,SAAOC,MAkCP9B,GAjCA/P,SAAAA,MAAAA,WAAO6R,MAAG5P,GAAgBjC,UAAO6R,KAAAC,MAiCjC/B,GAhCA9P,eAAAA,MAAAA,WAAa6R,MAAG7P,GAAgBhC,gBAAa6R,KAAAC,KAgC7ChC,GA/BAlN,SAAAA,KAAAA,WAAOkP,KAAG9P,GAAgBY,UAAOkP,IAAAC,KA+BjCjC,GA9BAhO,WAAAA,KAAAA,WAASiQ,KAAG/P,GAAgBF,YAASiQ,IACrCtG,KA6BAqE,GA7BArE,cACAC,KA4BAoE,GA5BApE,aACAC,KA2BAmE,GA3BAnE,cACAC,KA0BAkE,GA1BAlE,aACAC,KAyBAiE,GAzBAjE,WACAC,KAwBAgE,GAxBAhE,SACAC,KAuBA+D,GAvBA/D,eACAC,KAsBA8D,GAtBA9D,cACAC,KAqBA6D,GArBA7D,aACAC,MAoBA4D,GApBA5D,YAAU8F,KAoBVlC,GAnBA7P,SAAAA,KAAAA,WAAO+R,KAAGhQ,GAAgB/B,UAAO+R,IAAAC,KAmBjCnC,GAlBAjN,cAAAA,KAAAA,WAAYoP,KAAGjQ,GAAgBa,eAAYoP,IAAAC,KAkB3CpC,GAjBAhN,aAAAA,KAAAA,WAAWoP,KAAGlQ,GAAgBc,cAAWoP,IAAAC,KAiBzCrC,GAhBA9O,cAAAA,KAAAA,WAAYmR,KAAGnQ,GAAgBhB,eAAYmR,IAAAC,KAgB3CtC,GAfA/M,iBAAAA,KAAAA,WAAeqP,KAAGpQ,GAAgBe,kBAAeqP,IAAAC,KAejDvC,GAdA9M,eAAAA,KAAAA,WAAaqP,KAAGrQ,GAAgBgB,gBAAaqP,IAAAC,KAc7CxC,GAbA7M,sBAAAA,KAAAA,WAAoBqP,KAAGtQ,GAAgBiB,uBAAoBqP,IAAAC,KAa3DzC,GAZAzM,MAAAA,KAAAA,WAAIkP,KAAGvQ,GAAgBqB,OAAIkP,IAC3BhE,KAWAuB,GAXAvB,WACAC,KAUAsB,GAVAtB,gBACAC,KASAqB,GATArB,iBAAe+D,KASf1C,GARAxM,aAAAA,KAAAA,WAAWkP,KAAGxQ,GAAgBsB,cAAWkP,IACzCC,KAOA3C,GAPA2C,gBACAC,KAMA5C,GANA4C,qBACAC,KAKA7C,GALA6C,sBACAC,KAIA9C,GAJA8C,iBACAC,KAGA/C,GAHA+C,mBAAiBC,KAGjBhD,GAFA5M,kBAAAA,KAAAA,WAAgB4P,KAAG9Q,GAAgBkB,mBAAgB4P,IACnDC,KACAjD,GADAiD,cAGJC,KAAqEC,GACjEtO,GACAC,GACAsL,CAAAA,GAHI5B,KAAM0E,GAAN1E,QAAQ4E,KAAUF,GAAVE,YAAYC,KAAWH,GAAXG,aAAaC,KAAUJ,GAAVI,YAAYC,KAAWL,GAAXK,aAMrDC,KAUIlN,GAAgB,EAChB1F,MAAAA,IACAjC,QAAQ8H,IACRC,SAAAA,IACA7H,QAAQ+H,IACRC,SAAAA,IACAhC,OAAOuO,IACPtO,QAAQuO,IACRpU,QAAAA,IACAD,OAAAA,GACAa,mBAAAA,IACAN,YAAAA,IACAG,kBAAAA,GACAqD,cAAAA,IACAK,kBAAAA,GAAAA,CAAAA,GAvBA4F,KAAUwK,GAAVxK,YACAE,KAAYsK,GAAZtK,cACAa,KAAayJ,GAAbzJ,eACAC,KAAawJ,GAAbxJ,eACAxB,KAAMgL,GAANhL,QACA7J,KAAM6U,GAAN7U,QACAE,KAAM2U,GAAN3U,QACA+G,KAAM4N,GAAN5N,QACAnE,KAAM+R,GAAN/R,QAkBJgS,SAAwCpM,cAAAA,UAA+B,IAAA,GAAhEqM,KAAYD,GAAA,CAAA,GAAElF,KAAekF,GAAA,CAAA,GACpC1L,SAAwCV,cAAAA,UAAmC,IAAA,GAApEsM,KAAY5L,GAAA,CAAA,GAAE6L,KAAe7L,GAAA,CAAA,GAE9B8L,KAA4C,EAC9CC,MAAM,MACNjC,SAAS,MACTkC,MAAM,MACNC,OAAO,MACPC,WAAW,MACXC,OAAO,MACPzS,QAAQ,MACRmE,QAAQ,MACRuO,MAAM,MACNlU,SAAS,KAAA;AAGTb,EAAAA,GAAOgV,SAAS,MAAA,MAAYrU,OAAeC,SAC3C6T,GAAUC,WACNvT,oBAAAA,KAAC8T,GAAI,EAEDxP,OAAOuO,IACPtO,QAAQuO,IACR1U,QAAQoB,MAAepB,KAAsB,MAC7CE,QAAQmB,MAAenB,KAAsB,MAC7CyV,SAAShD,KACTiD,SAAS/C,IAAAA,GANL,MAAA,IAWZpS,GAAOgV,SAAS,SAAA,KAAc/O,MAAMmP,QAAQ3C,GAAAA,KAAYA,IAAQhI,SAAS,MACzEgK,GAAUhC,cACNtR,oBAAAA,KAACkU,IAAgB,EAEb5C,SAASA,KACThN,OAAOuO,IACPtO,QAAQuO,IACR1U,QAAQA,IACRE,QAAQA,GAAAA,GALJ,SAAA,IAUZO,GAAOgV,SAAS,MAAA,MAChBP,GAAUE,WACNxT,oBAAAA,KAACmU,IAAI,EAED/V,QAAQA,IACRE,QAAQA,IACRgG,OAAOuO,IACPtO,QAAQuO,IACRjE,KAAK3M,KACLkS,OAAOjS,KACPkS,QAAQjS,KACRwM,MAAMtM,IAAAA,GARF,MAAA,IAaZzD,GAAOgV,SAAS,OAAA,MAChBP,GAAUK,YACN3T,oBAAAA,KAAC8K,IAAK,EAEF7C,QAAQA,IACRuB,eAAeA,IACf5K,WAAWA,GAAAA,GAHP,OAAA,IAQZC,GAAOgV,SAAS,SAAA,KAAcnU,IAAQ4J,SAAS,MAC/CgK,GAAU5T,cACNM,oBAAAA,KAACsU,cAAAA,UAAQ,EAAAlU,UACJV,IAAQyB,IAAI,SAACoT,IAAQnP,IAAAA;AAAC,eACnBpF,oBAAAA,KAACwU,IAAY5S,EAAAA,CAAAA,GAEL2S,IAAM,EACVE,gBAAgB5B,IAChB6B,iBAAiB5B,IACjBzS,MAAMkU,GAAOlU,QAAQoI,IACrBkM,aACIJ,GAAOI,cACAhM,KAAAA,OACDoG,CAAAA,GARL3J,EAAAA;EAAAA,CAAAA,EAAAA,GAHH,SAAA;AAmBtB,MAAMwP,KAAYC,GAAShT,KAAMoG,IAAQnG,GAAAA;AAErCzC,EAAAA,OACAiU,GAAUG,YACNzT,oBAAAA,KAACyK,KAAK,EAEFhB,eAAeA,IACflK,aAAaA,IACb0C,eAAeA,IACfgG,QAAQA,GAAAA,GAJJ,OAAA,IASZtI,OAAAA,UAAiB6C,OACjB8Q,GAAUjO,aACNrF,oBAAAA,KAACwN,IAAM,EAEHnI,QAAQA,IACRxE,MAAM2B,IACNyI,OAAOxI,IACP7C,SAASe,IACT8M,SAAS2F,IACTjI,YAAYkI,IACZjI,cAAcA,IACdC,aAAaA,IACbC,cAAcA,IACdC,aAAaA,IACbC,WAAWA,IACXC,SAASA,IACTC,eAAeA,IACfC,cAAcA,IACdC,aAAaA,IACbC,YAAYA,IAAAA,GAhBR,QAAA,IAqBZ/M,OACAwU,GAAUpS,aACNlB,oBAAAA,KAAC0N,IAAM,EAEHxM,QAAQA,IACRyM,QAAQ2C,IACR1C,MAAM7O,IACN8O,aAAa3O,IACb4O,aAAa/L,GACbsG,OAAOrG,IACP+L,cAAc8C,KACd5N,aAAaA,IACb+K,iBAAiBA,IACjBpO,SAASA,IACTqO,QAAQA,IACRC,WAAWkE,IACXjE,gBAAgBkE,IAChBjE,iBAAiBkE,IACjBjE,YAAYkE,IACZjE,cAAckE,GAAAA,GAhBV,QAAA,IAqBZ7S,OAAiB+C,OACI,SAAjByQ,OACAG,GAAUI,gBACN1T,oBAAAA,KAAC8U,GAAS,EAENxQ,OAAOuO,IACPtO,QAAQuO,IACRlP,GAAGuP,GAAavP,GAChBC,GAAGsP,GAAatP,GAChBxF,MAAMsE,GAAAA,GALF,WAAA,IASK,SAAjByQ,MAAyB5Q,OACzB8Q,GAAUI,gBACN1T,oBAAAA,KAAC8U,GAAS,EAENxQ,OAAOuO,IACPtO,QAAQuO,IACRlP,GAAGwP,GAAaxP,GAChBC,GAAGuP,GAAavP,GAChBxF,MAAMmE,GAAAA,GALF,WAAA,KAWhB7C,OAAiB4C,MAAAA,UAAWC,OAC5B8Q,GAAUM,WACN5T,oBAAAA,KAACkP,IAAI,EAEDhO,QAAQA,IACRoD,OAAOuO,IACPtO,QAAQuO,IACR7E,QAAQA,IACR9C,YAAY6C,IACZ5C,cAAcA,IACdC,aAAaA,IACbC,cAAcA,IACdC,aAAaA,IACbC,WAAWA,IACXC,SAASA,IACTC,eAAeA,IACfC,cAAcA,IACdC,aAAaA,IACbC,YAAYA,KACZjM,SAASA,IACTgD,sBAAsBA,IACtBqI,OAAOxJ,GAAAA,GAlBH,MAAA;AAuBhB,MAAMsT,KAAiDnT,EAAA,CAAA,GAC/C6N,IAAK,EACToD,YAAAA,IACAC,aAAAA,IACA7K,QAAAA,IACA5C,QAAAA,IACAnE,QAAAA,IACA9C,QAAAA,IACAE,QAAAA,IACAkL,eAAAA,IACAC,eAAAA,IACA0J,cAAAA,IACAnF,iBAAAA,IACAoF,cAAAA,IACAC,iBAAAA,GAAAA,CAAAA;AAGJ,aACIrT,oBAAAA,KAACgV,IAAU,EACPnT,MAAM+S,IACNtQ,OAAOyO,IACPxO,QAAQyO,IACR/E,QAAQA,IACRjL,MAAMA,IACNkL,WAAWA,IACXC,gBAAgBA,IAChBC,iBAAiBA,IACjBnL,aAAaA,IACbgS,KAAKvC,IAAatS,UAEjBvB,GAAOsC,IAAI,SAAC+T,IAAO9P,IAAAA;AAChB,WAAqB,cAAA,OAAV8P,SACAlV,oBAAAA,KAACsU,cAAAA,UAAQ,EAAAlU,UAAU8U,GAAMH,EAAAA,EAAAA,GAAV3P,EAAAA,IAGnBkO,GAAU4B,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAIjC;AAEO,IAAMC,SAAOC,cAAAA,YAChB,SAAAtV,IASImV,IAAAA;AAAuB,MAAAI,KAAAvV,GAPnBH,eAAAA,KAAAA,WAAa0V,KAAG1T,GAAgBhC,gBAAa0V,IAAAC,KAAAxV,GAC7CgD,SAAAA,KAAAA,WAAOwS,KAAG3T,GAAgBmB,UAAOwS,IAAAC,KAAAzV,GACjCiD,cAAAA,KAAAA,WAAYwS,KAAG5T,GAAgBoB,eAAYwS,IAC3CnW,KAAKU,GAALV,OACAsC,KAAa5B,GAAb4B,eACG8T,IAAUC,EAAA3V,IAAA4V,EAAAA;AAAA,aAIjB1V,oBAAAA,KAAC2V,IAAS,EACN7S,SAASA,IACTnD,eAAeA,IACfoD,cAAcA,IACdrB,eAAeA,IACftC,OAAOA,IAAMgB,cAEbJ,oBAAAA,KAACwP,IAAS5N,EAAA,EAASjC,eAAeA,GAAAA,GAAmB6V,GAAU,EAAE9C,cAAcuC,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AACvE,CAAA;AApBb,IAoBa,KAAA,CAAA,gBAAA,iBAAA,YAAA,gBAAA;AApBb,ICtYMW,SAAiBR,cAAAA,YAC1B,SAAAtV,IAQImV,IAAAA;AAAuB,MANnBY,KAAY/V,GAAZ+V,cACAC,KAAahW,GAAbgW,eACAC,KAAQjW,GAARiW,UACAC,KAAclW,GAAdkW,gBACGvG,KAAKgG,EAAA3V,IAAA4V,EAAAA;AAAA,aAIZ1V,oBAAAA,KAACiW,IAAiB,EACdJ,cAAcA,IACdC,eAAeA,IACfC,UAAUA,IACVC,gBAAgBA,IAAe5V,UAE9B,SAAA2I,IAAAA;AAAA,QAAGzE,KAAKyE,GAALzE,OAAOC,KAAMwE,GAANxE;AAAM,eACbvE,oBAAAA,KAACmV,IAAIvT,EAAA,EAAS0C,OAAOA,IAAOC,QAAQA,GAAAA,GAAYkL,IAAK,EAAEwF,KAAKA,GAAAA,CAAAA,CAAAA;EAAO,EAAA,CAAA;AAEvD,CAAA;ADkXrB,IClXqB,KAAA,CAAA,iBAAA,iBAAA,OAAA;ADkXrB,IEzWDiB,KAAkB,SAAHpW,IAAAA;AA6Cf,MA5CFwE,KAAKxE,GAALwE,OACAC,KAAMzE,GAANyE,QACQsL,KAAa/P,GAArBmO,QAAMkI,IAAArW,GACNqD,YAAAA,IAAAA,WAAUgT,IAAGjT,GAAmBC,aAAUgT,GAC1C9V,IAAIP,GAAJO,MAAI+V,IAAAtW,GACJ1B,QAAQ8H,IAAAA,WAAUkQ,IAAGlT,GAAmB9E,SAAMgY,GAC9CjQ,KAAOrG,GAAPqG,SAAOkQ,IAAAvW,GACPxB,QAAQ+H,KAAAA,WAAUgQ,IAAGnT,GAAmB5E,SAAM+X,GAC9C/P,KAAOxG,GAAPwG,SAAOgQ,KAAAxW,GACPrB,OAAAA,KAAAA,WAAK6X,KAAGpT,GAAmBzE,QAAK6X,IAAAC,KAAAzW,GAChCjB,QAAAA,KAAAA,WAAM0X,KAAGrT,GAAmBrE,SAAM0X,IAAAC,KAAA1W,GAClCpB,QAAAA,KAAAA,WAAM8X,KAAGtT,GAAmBxE,SAAM8X,IAAAC,KAAA3W,GAClClB,WAAAA,KAAAA,WAAS6X,KAAGvT,GAAmBtE,YAAS6X,IAAAC,KAAA5W,GACxCT,YAAAA,KAAAA,WAAUqX,KAAGxT,GAAmB7D,aAAUqX,IAAAC,KAAA7W,GAC1CR,mBAAAA,KAAAA,WAAiBqX,KAAGzT,GAAmB5D,oBAAiBqX,IAAAC,KAAA9W,GACxDP,aAAAA,KAAAA,WAAWqX,KAAG1T,GAAmB3D,cAAWqX,IAAAC,KAAA/W,GAC5ChB,cAAAA,KAAAA,WAAY+X,KAAG3T,GAAmBpE,eAAY+X,IAAAC,KAAAhX,GAC9Cf,WAAAA,KAAAA,WAAS+X,KAAG5T,GAAmBnE,YAAS+X,IAAAC,IAAAjX,GACxCd,YAAAA,IAAAA,WAAU+X,IAAG7T,GAAmBlE,aAAU+X,GAAAC,KAAAlX,GAC1CZ,kBAAAA,MAAAA,WAAgB8X,KAAG9T,GAAmBhE,mBAAgB8X,IAAAC,MAAAnX,GACtDX,kBAAAA,MAAAA,WAAgB8X,MAAG/T,GAAmB/D,mBAAgB8X,KAAAC,MAAApX,GAGtDN,aAAAA,MAAAA,WAAW0X,MAAGhU,GAAmB1D,cAAW0X,KAC5CnG,MAAWjR,GAAXiR,aAAWoG,MAAArX,GACXL,aAAAA,MAAAA,WAAW0X,MAAGjU,GAAmBzD,cAAW0X,KAC5ClG,MAAWnR,GAAXmR,aACA/O,MAAOpC,GAAPoC,SACAC,MAASrC,GAATqC,WAASiV,MAAAtX,GACTsC,YAAAA,MAAAA,WAAUgV,MAAGlU,GAAmBd,aAAUgV,KAAAC,MAAAvX,GAC1CwC,UAAAA,MAAAA,WAAQ+U,MAAGnU,GAAmBZ,WAAQ+U,KAAAC,MAAAxX,GACtCJ,SAAAA,MAAAA,WAAO4X,MAAGpU,GAAmBxD,UAAO4X,KAAAjC,MAAAvV,GACpCH,eAAAA,MAAAA,WAAa0V,MAAGnS,GAAmBvD,gBAAa0V,KAAAkC,MAAAzX,GAChD2B,WAAAA,MAAAA,WAAS8V,MAAGrU,GAAmBzB,YAAS8V,KACxCjM,MAAYxL,GAAZwL,cACAC,MAAWzL,GAAXyL,aACAC,MAAS1L,GAAT0L,WACAC,MAAO3L,GAAP2L,SACAC,MAAa5L,GAAb4L,eAAa8L,MAAA1X,GACbF,SAAAA,KAAAA,WAAO4X,MAAGtU,GAAmBtD,UAAO4X,KACpCxU,KAAIlD,GAAJkD,MACA0P,KAAY5S,GAAZ4S,cAIM+E,SAAWC,cAAAA,QAAiC,IAAA,GAElD/E,KAAqEC,GACjEtO,IACAC,IACAsL,EAAAA,GAHI5B,KAAM0E,GAAN1E,QAAQ4E,KAAUF,GAAVE,YAAYC,KAAWH,GAAXG,aAAaC,KAAUJ,GAAVI,YAAYC,KAAWL,GAAXK,aAK/C5T,KAAQ0B,EAAAA,GACdoS,SAAwCpM,cAAAA,UAA+B,IAAA,GAAhEqM,KAAYD,GAAA,CAAA,GAAElF,MAAekF,GAAA,CAAA,GAEpCD,KAAyElN,GAAgB,EACrF1F,MAAAA,GACAjC,QAAQ8H,GACRC,SAAAA,IACA7H,QAAQ+H,IACRC,SAAAA,IACAhC,OAAOuO,IACPtO,QAAQuO,IACRpU,QAAAA,IACAD,OAAAA,IACAa,mBAAAA,IACAN,YAAAA,GACAG,kBAAAA,IAAAA,CAAAA,GAZIqK,KAAayJ,GAAbzJ,eAAeC,KAAawJ,GAAbxJ,eAAexB,KAAMgL,GAANhL,QAAQ7J,KAAM6U,GAAN7U,QAAQE,KAAM2U,GAAN3U,QAAQ4C,KAAM+R,GAAN/R,QAexD6T,SAAuDvR,cAAAA,SACzD,WAAA;AAAA,WAAO,EACHqP,YAAAA,IACAC,aAAAA,IACA7K,QAAAA,IACA/G,QAAAA,IACA9C,QAAAA,IACAE,QAAAA,IACAM,WAAAA,IACA4K,eAAAA,IACAC,eAAAA,IACA0J,cAAAA,IACAnF,iBAAAA,IAAAA;EACF,GACF,CACI6E,IACAC,IACA7K,IACA/G,IACA9C,IACAE,IACAM,IACA4K,IACAC,IACA0J,IACAnF,GAAAA,CAAAA,GAIR2J,KAA8BC,GAAe,EACzC1W,QAAAA,IACAoD,OAAOuO,IACPtO,QAAQuO,IACR7H,OAAOxJ,IAAAA,CAAAA,GAJHoW,KAAQF,GAARE,UAAUC,KAAOH,GAAPG;AAOlBC,oBAAAA,WAAU,WAAA;AACN,QAAyB,SAArBN,GAAShK,SAAb;AAEAgK,SAAShK,QAAQnJ,QAAQyO,KAAa5P,GACtCsU,GAAShK,QAAQlJ,SAASyO,KAAc7P;AAExC,UAAM6U,KAAMP,GAAShK,QAAQwK,WAAW,IAAA;AAExCD,MAAAA,GAAIE,MAAM/U,GAAYA,CAAAA,GAEtB6U,GAAIG,YAAY/Y,GAAMgZ,YACtBJ,GAAIK,SAAS,GAAG,GAAGtF,IAAYC,EAAAA,GAC/BgF,GAAIM,UAAUrK,GAAOW,MAAMX,GAAOY,GAAAA,GAElChQ,GAAO4F,QAAQ,SAAAyQ,IAAAA;AAAS,YAAAqD;AACC,sBAAA,OAAVrD,MACPA,GAAM8C,IAAKjD,EAAAA;AAGf,YAAMyD,KAA2CD,SAA9BA,KAAGnZ,GAAMmU,KAAK9P,KAAK8G,eAAWgO,KAAI;AAwCrD,YAvCc,WAAVrD,MAA6C,YAAA,OAAlBsD,MAA8BA,KAAgB,MACzER,GAAIpZ,YAAY4Z,IAChBR,GAAIS,cAAcrZ,GAAMmU,KAAK9P,KAAKoH,QAE9BrL,OACAkZ,EAAwBV,IAAK,EACzB1T,OAAOuO,IACPtO,QAAQuO,IACRoF,OAAO9Z,IACPyC,MAAM,KACN8X,QAAQ5H,IAAAA,CAAAA,GAIZtR,OACAiZ,EAAwBV,IAAK,EACzB1T,OAAOuO,IACPtO,QAAQuO,IACRoF,OAAO5Z,IACPuC,MAAM,KACN8X,QAAQ1H,IAAAA,CAAAA,IAKN,WAAViE,MACA0D,EAAmBZ,IAAK,EACpB5Z,QAAQA,IACRE,QAAQA,IACRgG,OAAOuO,IACPtO,QAAQuO,IACRjE,KAAK3M,KACLkS,OAAOjS,KACPkS,QAAQjS,KACRwM,MAAMtM,KACNlD,OAAAA,GAAAA,CAAAA,GAIM,YAAV8V,MAAAA,SAAqB7V,IAAqB;AAC1C2Y,UAAAA,GAAIa,KAAAA,GACJb,GAAIc,cAAcvZ,IAElBkK,GAAcsP,QAAQf,EAAAA;AACtB,mBAAS5S,KAAI6C,GAAOqB,SAAS,GAAGlE,MAAK,GAAGA,KACpC4S,CAAAA,GAAIG,YAAYlQ,GAAO7C,EAAAA,EAAG3E,OAC1BuX,GAAIgB,UAAAA,GACJvP,GAAcxB,GAAO7C,EAAAA,EAAG/E,KAAKc,IAAI,SAAAwC,IAAAA;AAAC,mBAAIA,GAAEwF;UAAQ,CAAA,CAAA,GAChD6O,GAAIlW,KAAAA;AAGRkW,UAAAA,GAAIiB,QAAAA;QACR;AAmCA,YAjCc,YAAV/D,OACA1L,GAAcuP,QAAQf,EAAAA,GACtB/P,GAAOxD,QAAQ,SAAA2D,IAAAA;AACX4P,UAAAA,GAAIS,cAAcrQ,GAAW3H,OAC7BuX,GAAIpZ,YAAYA,IAChBoZ,GAAIgB,UAAAA,GACJxP,GAAcpB,GAAW/H,KAAKc,IAAI,SAAAwC,IAAAA;AAAC,mBAAIA,GAAEwF;UAAQ,CAAA,CAAA,GACjD6O,GAAInN,OAAAA;QACR,CAAA,IAGU,aAAVqK,MAAAA,SAAsBpW,MAAyBC,KAAY,KAC3DmC,GAAOuD,QAAQ,SAAA1E,IAAAA;AACXiY,UAAAA,GAAIG,YAAYpY,GAAMU,OACtBuX,GAAIgB,UAAAA,GACJhB,GAAIkB,IAAInZ,GAAM6D,GAAG7D,GAAM8D,GAAG9E,KAAY,GAAG,GAAG,IAAIoa,KAAKC,EAAAA,GACrDpB,GAAIlW,KAAAA,GAEA5C,MAAmB,MACnB8Y,GAAIS,cAAc1Y,GAAMwJ,aACxByO,GAAIpZ,YAAYM,KAChB8Y,GAAInN,OAAAA;QAEZ,CAAA,GAGU,WAAVqK,MAAAA,SAAoBzT,OAAAA,WAAsBqW,OAC1CuB,EAAsBrB,IAAKF,EAAAA,GACvB3E,MACAmG,GAA0BtB,IAAKF,IAAS3E,GAAa9J,QAAAA,IAI/C,cAAV6L,IAAqB;AACrB,cAAMzM,KAAaR,GACd9G,IAAI,SAAAoY,IAAAA;AAAK,mBAAK,EACXrZ,IAAIqZ,GAAMrZ,IACVmI,OAAOkR,GAAMrZ,IACbO,OAAO8Y,GAAM9Y,MAAAA;UAChB,CAAA,EACAkF,QAAAA;AAELjG,UAAAA,IAAQ+E,QAAQ,SAAA8P,IAAAA;AACZiF,cAAqBxB,IAAGpW,EAAAA,CAAAA,GACjB2S,IAAM,EACTlU,MAAMkU,GAAOlU,QAAQoI,IACrBgM,gBAAgB5B,IAChB6B,iBAAiB5B,IACjB1T,OAAAA,GAAAA,CAAAA,CAAAA;UAER,CAAA;QACJ;MACJ,CAAA;IA7H+B;EA8HnC,GAAG,CACCqY,IACA5E,IACAE,IACAD,IACAE,IACA/E,GAAOW,MACPX,GAAOY,KACP1L,GACAtE,IACAO,IACAoK,IACAvB,IACA7J,IACAE,IACAkB,KACAuR,KACAtR,KACAwR,KACA/O,KACAC,KACAC,KACAE,KACA5C,KACAwB,IACApC,IACAC,IACAG,KACAiU,IACA4B,IACAtT,KACApC,IACAoK,IACAlK,IACAX,IACAkZ,EAAAA,CAAAA;AAGJ,MAAM2B,SAAyB7Q,cAAAA,aAC3B,SAACuD,IAAAA;AACG,QAAA,CAAKsL,GAAShK,QAAS,QAAO;AAE9B,QAAAiM,KAAeC,GAAkBlC,GAAShK,SAAStB,EAAAA,GAA5CvI,KAAC8V,GAAA,CAAA,GAAE7V,KAAC6V,GAAA,CAAA;AACX,QAAA,CAAKE,GAAe3L,GAAOW,MAAMX,GAAOY,KAAKgE,IAAYC,IAAalP,IAAGC,EAAAA,EAAI,QAAO;AAEpF,QAAMgW,KAAahC,GAAStP,KAAK3E,KAAIqK,GAAOW,MAAM/K,KAAIoK,GAAOY,GAAAA;AAC7D,WAAO3N,GAAO2Y,EAAAA;EAClB,GACA,CAACpC,IAAUxJ,IAAQ4E,IAAYC,IAAa+E,IAAU3W,EAAAA,CAAAA,GAG1D4K,KAA8CC,EAAAA,GAAtCC,KAAoBF,GAApBE,sBAAsBC,KAAWH,GAAXG,aAExB6N,SAAmBlR,cAAAA,aACrB,SAACuD,IAAAA;AACG,QAAMpM,KAAQ0Z,GAAuBtN,EAAAA;AACrC6B,IAAAA,IAAgBjO,EAAAA,GAEZA,KACAiM,OAAqBI,cAAAA,eAAcxM,IAAS,EAAEG,OAAAA,GAAAA,CAAAA,GAAUoM,EAAAA,IAExDF,GAAAA;EAER,GACA,CAACwN,IAAwBzL,KAAiBhC,IAAsBC,IAAarM,EAAAA,CAAAA,GAG3E0M,SAAmB1D,cAAAA,aACrB,SAACuD,IAAAA;AACGF,OAAAA,GACA+B,IAAgB,IAAA,GACZmF,OAA0B,QAAZ7H,OAAAA,IAAe6H,IAAchH,EAAAA;EAClD,GACD,CAACF,IAAa+B,KAAiB1C,KAAc6H,EAAAA,CAAAA,GAG3C5G,SAAkB3D,cAAAA,aACpB,SAACuD,IAAAA;AACG,QAAIZ,KAAa;AACb,UAAMxL,KAAQ0Z,GAAuBtN,EAAAA;AACjCpM,MAAAA,MAAOwL,IAAYxL,IAAOoM,EAAAA;IAClC;EACJ,GACA,CAACsN,IAAwBlO,GAAAA,CAAAA,GAGvBiB,SAAgB5D,cAAAA,aAClB,SAACuD,IAAAA;AACG,QAAIX,KAAW;AACX,UAAMzL,KAAQ0Z,GAAuBtN,EAAAA;AACjCpM,MAAAA,MAAOyL,IAAUzL,IAAOoM,EAAAA;IAChC;EACJ,GACA,CAACsN,IAAwBjO,GAAAA,CAAAA,GAGvBiB,SAAc7D,cAAAA,aAChB,SAACuD,IAAAA;AACG,QAAIV,KAAS;AACT,UAAM1L,KAAQ0Z,GAAuBtN,EAAAA;AACjCpM,MAAAA,MAAO0L,IAAQ1L,IAAOoM,EAAAA;IAC9B;EACJ,GACA,CAACsN,IAAwBhO,GAAAA,CAAAA,GAGvBiB,SAAoB9D,cAAAA,aACtB,SAACuD,IAAAA;AACG,QAAIT,KAAe;AACf,UAAM3L,KAAQ0Z,GAAuBtN,EAAAA;AACjCpM,MAAAA,MAAO2L,IAAc3L,IAAOoM,EAAAA;IACpC;EACJ,GACA,CAACsN,IAAwB/N,GAAAA,CAAAA;AAG7B,aACI1L,oBAAAA,KAAA,UAAA,EACIiV,KAAK8E,GAA6BtC,IAAU/E,EAAAA,GAC5CpO,OAAOyO,KAAa5P,GACpBoB,QAAQyO,KAAc7P,GACtB9B,OAAO,EACHiD,OAAOyO,IACPxO,QAAQyO,IACRgH,QAAQra,MAAgB,SAAS,SAAA,GAErCyL,cAAczL,MAAgBma,KAAAA,QAC9BzO,aAAa1L,MAAgBma,KAAAA,QAC7BxO,cAAc3L,MAAgB2M,KAAAA,QAC9Bf,aAAa5L,MAAgB4M,KAAAA,QAC7Bf,WAAW7L,MAAgB6M,KAAAA,QAC3Bf,SAAS9L,MAAgB8M,KAAAA,QACzBf,eAAe/L,MAAgB+M,KAAAA,QAC/B1J,MAAMA,GAAAA,CAAAA;AAGlB;AFTO,IEWMiX,SAAa7E,cAAAA,YACtB,SAAArM,IAEIkM,IAAAA;AAA2B,MADzBtV,KAAaoJ,GAAbpJ,eAAe+B,KAAaqH,GAAbrH,eAAetC,KAAK2J,GAAL3J,OAAUqQ,KAAKgG,EAAA1M,IAAA2M,EAAAA;AAAA,aAG/C1V,oBAAAA,KAAC2V,IAAS,EAAOhW,eAAAA,IAAe+B,eAAAA,IAAetC,OAAAA,IAAS0D,SAAAA,OAAe1C,cACnEJ,oBAAAA,KAACkW,IAAetU,EAAAA,CAAAA,GAAa6N,IAAK,EAAEiD,cAAcuC,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAC1C,CAAA;AFlBb,IEkBa,KAAA,CAAA,gBAAA,iBAAA,YAAA,gBAAA;AFlBb,IGtYMiF,SAAuB9E,cAAAA,YAChC,SAAAtV,IAQImV,IAAAA;AAA2B,MANvBY,KAAY/V,GAAZ+V;AACa/V,EAAAA,GAAbgW;AAAAA,MACAC,KAAQjW,GAARiW,UACAC,KAAclW,GAAdkW,gBACGvG,KAAKgG,EAAA3V,IAAA4V,EAAAA;AAAA,aAIZ1V,oBAAAA,KAACiW,IAAiB,EACdJ,cAAcA,IACdC,eAAeD,IACfE,UAAUA,IACVC,gBAAgBA,IAAe5V,UAE9B,SAAA2I,IAAAA;AAAA,QAAGzE,KAAKyE,GAALzE,OAAOC,KAAMwE,GAANxE;AAAM,eACbvE,oBAAAA,KAACia,IAAUrY,EAAA,EAAS0C,OAAOA,IAAOC,QAAQA,GAAAA,GAAYkL,IAAK,EAAEwF,KAAKA,GAAAA,CAAAA,CAAAA;EAAO,EAAA,CAAA;AAE7D,CAAA;ACyErB,SAASkF,GACZ9Z,IAAAA;AAEA,SAAA,WAAQA,GAAuBkB;AACnC;AACO,SAAS6Y,GACZ/Z,IAAAA;AAEA,SAAA,WAAQA,GAA2Ba;AACvC;", "names": ["n", "i", "v", "x", "y", "z", "x2", "y2", "z2", "t", "e", "e", "i", "n", "u3", "u", "bc", "ca", "ab", "u", "abt", "bct", "cat", "_8", "_16", "fin", "fin2", "ab", "bc", "_8", "_8b", "_16", "_48", "fin", "n", "i", "x", "y", "i2", "r", "j", "k", "e", "q", "t", "a", "b", "epsilon", "x", "y", "r", "w", "x", "y", "i", "j", "n", "x", "y", "t2", "x1", "y1", "x2", "y2", "ab", "a", "t", "v", "S", "e", "V", "P", "k", "i", "a", "b", "x", "y", "r", "_", "j", "e", "n", "collinear", "l", "t", "h", "t2", "defaultVoronoiProps", "xDomain", "yDomain", "layers", "enableLinks", "linkLineWidth", "linkLineColor", "enableCells", "cellLineWidth", "cellLineColor", "enablePoints", "pointSize", "pointColor", "role", "defaultNodePositionAccessor", "node", "x", "y", "defaultMargin", "coreDefaultMargin", "defaultTooltipPosition", "defaultTooltipAnchor", "computeMeshPoints", "_ref", "points", "_ref$getNodePosition", "getNodePosition", "_ref$margin", "margin", "map", "_getNodePosition", "left", "top", "computeMesh", "_ref2", "width", "height", "_ref2$margin", "debug", "delaunay", "Delaunay", "from", "voronoi", "right", "bottom", "useVoronoiMesh", "useMemo", "useVoronoi", "data", "xScale", "scaleLinear", "domain", "range", "yScale", "d", "p", "useVoronoiLayerContext", "_ref3", "useMeshEvents", "_ref4", "elementRef", "nodes", "_ref4$getNodePosition", "setCurrentNode", "setCurrent", "_ref4$margin", "_ref4$detectionRadius", "detectionRadius", "Infinity", "_ref4$isInteractive", "isInteractive", "onMouseEnter", "onMouseMove", "onMouseLeave", "onMouseDown", "onMouseUp", "onClick", "onDoubleClick", "onTouchStart", "onTouchMove", "onTouchEnd", "_ref4$enableTouchCros", "enableTouchCrosshair", "tooltip", "_ref4$tooltipPosition", "tooltipPosition", "_ref4$tooltipAnchor", "tooltipAnchor", "_useState", "useState", "current", "previous", "useRef", "useEffect", "findNode", "useCallback", "event", "length", "_getRelativeCursor", "getRelativeCursor", "index", "find", "nodeX", "nodeY", "getDistance", "_useTooltip", "useTooltip", "showTooltipAt", "showTooltipFromEvent", "hideTooltip", "showTooltip", "_getNodePosition2", "handleMouseEnter", "match", "handleMouseMove", "_previous$current", "previousIndex", "previousNode", "handleMouseLeave", "handleMouseDown", "handleMouseUp", "handleClick", "handleDoubleClick", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "undefined", "InnerVoronoi", "_ref", "data", "width", "height", "<PERSON><PERSON><PERSON><PERSON>", "margin", "_ref$layers", "layers", "defaultVoronoiProps", "_ref$xDomain", "xDomain", "_ref$yDomain", "yDomain", "_ref$enableLinks", "enableLinks", "_ref$linkLineWidth", "linkLineWidth", "_ref$linkLineColor", "linkLineColor", "_ref$enableCells", "enableCells", "_ref$cellLineWidth", "cellLineWidth", "_ref$cellLineColor", "cellLineColor", "_ref$enablePoints", "enablePoints", "_ref$pointSize", "pointSize", "_ref$pointColor", "pointColor", "_ref$role", "role", "forwardedRef", "_useDimensions", "useDimensions", "outerWidth", "outerHeight", "innerWidth", "innerHeight", "_useVoronoi", "useVoronoi", "points", "delaunay", "voronoi", "layerById", "links", "cells", "bounds", "includes", "_jsx", "stroke", "strokeWidth", "fill", "d", "render", "renderPoints", "renderBounds", "layerContext", "useVoronoiLayerContext", "SvgWrapper", "ref", "children", "map", "layer", "i", "Fragment", "createElement", "Voronoi", "forwardRef", "_ref2", "theme", "props", "_objectWithoutPropertiesLoose", "_excluded", "Container", "isInteractive", "animate", "_extends", "ResponsiveVoronoi", "defaultWidth", "defaultHeight", "onResize", "debounceResize", "ResponsiveWrapper", "<PERSON><PERSON>", "nodes", "_ref$margin", "defaultMargin", "getNodePosition", "setCurrent", "onMouseEnter", "onMouseMove", "onMouseLeave", "onMouseDown", "onMouseUp", "onClick", "onDoubleClick", "onTouchStart", "onTouchMove", "onTouchEnd", "_ref$enableTouchCross", "enableTouchCrosshair", "_ref$detectionRadius", "detectionRadius", "Infinity", "tooltip", "_ref$tooltipPosition", "tooltipPosition", "defaultTooltipPosition", "_ref$tooltipAnchor", "tooltipAnchor", "defaultTooltipAnchor", "debug", "elementRef", "useRef", "_useVoronoiMesh", "useVoronoiMesh", "_useMeshEvents", "useMeshEvents", "current", "handleMouseEnter", "handleMouseMove", "handleMouseLeave", "handleMouseDown", "handleMouseUp", "handleClick", "handleDoubleClick", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "voronoiPath", "useMemo", "_jsxs", "transform", "left", "top", "_Fragment", "opacity", "renderCell", "right", "bottom", "style", "cursor", "renderVoronoiToCanvas", "ctx", "save", "globalAlpha", "beginPath", "strokeStyle", "lineWidth", "restore", "renderVoronoiCellToCanvas", "ctx", "voronoi", "index", "save", "globalAlpha", "beginPath", "renderCell", "fillStyle", "fill", "restore", "commonDefaultProps", "xScale", "type", "yScale", "min", "max", "curve", "colors", "scheme", "lineWidth", "layers", "enablePoints", "pointSize", "pointColor", "from", "pointBorderWidth", "pointBorderColor", "theme", "enableArea", "areaBaselineValue", "areaOpacity", "enableGridX", "enableGridY", "legends", "isInteractive", "tooltip", "memo", "_ref", "point", "_jsx", "BasicTooltip", "id", "_jsxs", "children", "data", "xFormatted", "yFormatted", "enableChip", "color", "seriesColor", "sliceTooltip", "slice", "axis", "useTheme", "otherAxis", "TableTooltip", "rows", "points", "map", "Chip", "style", "chip", "seriesId", "tableCellValue", "debug<PERSON><PERSON>", "renderWrapper", "svgDefaultProps", "_extends", "defs", "fill", "enablePointLabel", "pointLabel", "areaBlendMode", "axisTop", "axisRight", "axisBottom", "defaultAxisProps", "axisLeft", "<PERSON><PERSON><PERSON>", "enableSlices", "debugSlices", "enableCrosshair", "crosshairType", "enableTouchCrosshair", "initialHiddenIds", "animate", "motionConfig", "role", "isFocusable", "canvasDefaultProps", "pixelRatio", "window", "_window$devicePixelRa", "devicePixelRatio", "useLineGenerator", "useMemo", "line", "defined", "d", "x", "y", "curveFromProp", "useAreaGenerator", "area", "y1", "y0", "useSlices", "_ref3", "componentId", "width", "height", "Map", "for<PERSON>ach", "has", "get", "push", "set", "Array", "entries", "sort", "a", "b", "_ref4", "i", "slices", "x0", "sliceWidth", "slicePoints", "prevSlice", "nextSlice", "reverse", "_ref5", "sliceHeight", "LINE_UNIQUE_ID_PREFIX", "useLine", "_ref6", "_ref6$xScale", "xScaleSpec", "xFormat", "_ref6$yScale", "yScaleSpec", "yFormat", "_ref6$colors", "_ref6$curve", "_ref6$areaBaselineVal", "_ref6$pointColor", "_ref6$pointBorderColo", "_ref6$enableSlices", "_ref6$initialHiddenId", "useState", "uniqueId", "formatX", "useValueFormatter", "formatY", "getColor", "useOrdinalColorScale", "getPointColor", "useInheritedColor", "getPointBorderColor", "_useState2", "hiddenIds", "setHiddenIds", "_useMemo", "computeXYScalesForSeries", "filter", "item", "indexOf", "rawSeries", "series", "_useMemo2", "dataWithColor", "seriesItem", "label", "datum", "find", "Boolean", "legendData", "hidden", "toggleSeries", "useCallback", "state", "concat", "_ref2", "reduce", "acc", "seriesIndex", "position", "indexInSeries", "absIndex", "length", "borderColor", "lineGenerator", "areaGenerator", "AreaPath", "path", "_useMotionConfig", "useMotionConfig", "springConfig", "config", "animatedPath", "useAnimatedPath", "animatedProps", "useSpring", "immediate", "animated", "fillOpacity", "strokeWidth", "mixBlendMode", "Areas", "reversedSeries", "LinesItem", "thickness", "stroke", "Lines", "_Fragment", "SlicesItem", "debug", "isCurrent", "setCurrent", "onMouseEnter", "onMouseMove", "onMouseLeave", "onMouseDown", "onMouseUp", "onClick", "onDoubleClick", "onTouchStart", "onTouchMove", "onTouchEnd", "_useTooltip", "useTooltip", "showTooltipFromEvent", "hideTooltip", "handleMouseEnter", "event", "createElement", "handleMouseMove", "handleMouseLeave", "handleMouseDown", "handleMouseUp", "handleClick", "handleDoubleClick", "handeOnTouchStart", "handeOnTouchMove", "touchPoint", "touches", "touchingElement", "document", "elementFromPoint", "clientX", "clientY", "touchingSliceId", "getAttribute", "handleOnTouchEnd", "strokeOpacity", "Slices", "current", "Points", "symbol", "size", "borderWidth", "enableLabel", "labelYOffset", "setCurrentPoint", "margin", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaDescribedBy", "ariaHidden", "ariaDisabled", "get<PERSON><PERSON><PERSON>", "getLabelGenerator", "showTooltipAt", "mappedPoints", "onFocus", "left", "top", "onBlur", "undefined", "DotsItem", "testId", "<PERSON><PERSON>", "handleTouchStart", "handleTouchMove", "handleTouchEnd", "BaseMesh", "nodes", "InnerLine", "props", "_props$xScale", "_props$yScale", "_props$curve", "<PERSON><PERSON><PERSON><PERSON>", "_props$colors", "_props$lineWidth", "_props$layers", "_props$enableArea", "_props$areaBaselineVa", "_props$areaOpacity", "_props$areaBlendMode", "_props$enablePoints", "pointSymbol", "_props$pointSize", "_props$pointColor", "_props$pointBorderWid", "_props$pointBorderCol", "_props$enablePointLab", "_props$pointLabel", "pointLabelYOffset", "_props$enableGridX", "gridXValues", "_props$enableGridY", "gridYValues", "_props$axisBottom", "_props$axisLeft", "_props$defs", "_props$fill", "markers", "_props$legends", "_props$isInteractive", "_props$useMesh", "_props$debugMesh", "_props$tooltip", "_props$enableSlices", "_props$debugSlices", "_props$sliceTooltip", "_props$enableCrosshai", "_props$crosshairType", "_props$enableTouchCro", "_props$role", "_props$isFocusable", "pointAriaLabel", "pointAriaLabelledBy", "pointAriaDescribedBy", "pointAriaHidden", "pointAriaDisabled", "_props$initialHiddenI", "forwardedRef", "_useDimensions", "useDimensions", "innerWidth", "innerHeight", "outerWidth", "outerHeight", "_useLine", "_useState", "currentPoint", "currentSlice", "setCurrentSlice", "layerById", "grid", "axes", "areas", "crosshair", "lines", "mesh", "includes", "Grid", "xValues", "yV<PERSON><PERSON>", "isArray", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "Axes", "right", "bottom", "Fragment", "legend", "BoxLegendSvg", "containerWidth", "containerHeight", "toggleSerie", "boundDefs", "bindDefs", "<PERSON><PERSON><PERSON>", "customLayerProps", "SvgWrapper", "ref", "layer", "Line", "forwardRef", "_ref$isInteractive", "_ref$animate", "_ref$motionConfig", "otherProps", "_objectWithoutPropertiesLoose", "_excluded", "Container", "ResponsiveLine", "defaultWidth", "defaultHeight", "onResize", "debounceResize", "ResponsiveWrapper", "InnerLineCanvas", "_ref$pixelRatio", "_ref$xScale", "_ref$yScale", "_ref$curve", "_ref$layers", "_ref$colors", "_ref$lineWidth", "_ref$enableArea", "_ref$areaBaselineValu", "_ref$areaOpacity", "_ref$enablePoints", "_ref$pointSize", "_ref$pointColor", "_ref$pointBorderWidth", "_ref$pointBorderColor", "_ref$enableGridX", "_ref$enableGridY", "_ref$axisBottom", "_ref$axisLeft", "_ref$legends", "_ref$debugMesh", "_ref$tooltip", "canvasEl", "useRef", "_useVoronoiMesh", "useVoronoiMesh", "delaunay", "voronoi", "useEffect", "ctx", "getContext", "scale", "fillStyle", "background", "fillRect", "translate", "_theme$grid$line$stro", "gridLineWidth", "strokeStyle", "renderGridLinesToCanvas", "values", "renderAxesToCanvas", "save", "globalAlpha", "context", "beginPath", "restore", "arc", "Math", "PI", "renderVoronoiToCanvas", "renderVoronoiCellToCanvas", "serie", "renderLegendToCanvas", "getPointFromMouseEvent", "_getRelativeCursor", "getRelativeCursor", "isCursorInRect", "pointIndex", "handleMouseHover", "mergeRefs", "cursor", "LineCanvas", "ResponsiveLineCanvas", "isPoint", "isSliceData"]}