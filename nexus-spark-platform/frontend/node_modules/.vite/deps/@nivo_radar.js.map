{"version": 3, "sources": ["../../@nivo/radar/node_modules/@react-spring/web/dist/react-spring_web.modern.mjs", "../../@nivo/radar/src/RadarLayer.tsx", "../../@nivo/radar/src/RadarGridLabels.tsx", "../../@nivo/radar/src/RadarGridLevels.tsx", "../../@nivo/radar/src/RadarGrid.tsx", "../../@nivo/radar/src/RadarSlice.tsx", "../../@nivo/radar/src/RadarSlices.tsx", "../../@nivo/radar/src/RadarDots.tsx", "../../@nivo/radar/src/defaults.ts", "../../@nivo/radar/src/RadarGridLabel.tsx", "../../@nivo/radar/src/RadarSliceTooltip.tsx", "../../@nivo/radar/src/Radar.tsx", "../../@nivo/radar/src/hooks.ts", "../../@nivo/radar/src/ResponsiveRadar.tsx"], "sourcesContent": ["// src/index.ts\nimport { Globals } from \"@react-spring/core\";\nimport { unstable_batchedUpdates } from \"react-dom\";\nimport { createStringInterpolator, colors } from \"@react-spring/shared\";\nimport { createHost } from \"@react-spring/animated\";\n\n// src/applyAnimatedValues.ts\nvar isCustomPropRE = /^--/;\nfunction dangerousStyleValue(name, value) {\n  if (value == null || typeof value === \"boolean\" || value === \"\") return \"\";\n  if (typeof value === \"number\" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))\n    return value + \"px\";\n  return (\"\" + value).trim();\n}\nvar attributeCache = {};\nfunction applyAnimatedValues(instance, props) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false;\n  }\n  const isFilterElement = instance.nodeName === \"filter\" || instance.parentNode && instance.parentNode.nodeName === \"filter\";\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props;\n  const values = Object.values(attributes);\n  const names = Object.keys(attributes).map(\n    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(\n      /([A-Z])/g,\n      // Attributes are written in dash case\n      (n) => \"-\" + n.toLowerCase()\n    ))\n  );\n  if (children !== void 0) {\n    instance.textContent = children;\n  }\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name]);\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value);\n      } else {\n        instance.style[name] = value;\n      }\n    }\n  }\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i]);\n  });\n  if (className !== void 0) {\n    instance.className = className;\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop;\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft;\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute(\"viewBox\", viewBox);\n  }\n}\nvar isUnitlessNumber = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nvar prefixKey = (prefix, key) => prefix + key.charAt(0).toUpperCase() + key.substring(1);\nvar prefixes = [\"Webkit\", \"Ms\", \"Moz\", \"O\"];\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach((prefix) => acc[prefixKey(prefix, prop)] = acc[prop]);\n  return acc;\n}, isUnitlessNumber);\n\n// src/AnimatedStyle.ts\nimport { AnimatedObject } from \"@react-spring/animated\";\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver\n} from \"@react-spring/shared\";\nvar domTransforms = /^(matrix|translate|scale|rotate|skew)/;\nvar pxTransforms = /^(translate)/;\nvar degTransforms = /^(rotate|skew)/;\nvar addUnit = (value, unit) => is.num(value) && value !== 0 ? value + unit : value;\nvar isValueIdentity = (value, id) => is.arr(value) ? value.every((v) => isValueIdentity(v, id)) : is.num(value) ? value === id : parseFloat(value) === id;\nvar AnimatedStyle = class extends AnimatedObject {\n  constructor({ x, y, z, ...style }) {\n    const inputs = [];\n    const transforms = [];\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0]);\n      transforms.push((xyz) => [\n        `translate3d(${xyz.map((v) => addUnit(v, \"px\")).join(\",\")})`,\n        // prettier-ignore\n        isValueIdentity(xyz, 0)\n      ]);\n    }\n    eachProp(style, (value, key) => {\n      if (key === \"transform\") {\n        inputs.push([value || \"\"]);\n        transforms.push((transform) => [transform, transform === \"\"]);\n      } else if (domTransforms.test(key)) {\n        delete style[key];\n        if (is.und(value)) return;\n        const unit = pxTransforms.test(key) ? \"px\" : degTransforms.test(key) ? \"deg\" : \"\";\n        inputs.push(toArray(value));\n        transforms.push(\n          key === \"rotate3d\" ? ([x2, y2, z2, deg]) => [\n            `rotate3d(${x2},${y2},${z2},${addUnit(deg, unit)})`,\n            isValueIdentity(deg, 0)\n          ] : (input) => [\n            `${key}(${input.map((v) => addUnit(v, unit)).join(\",\")})`,\n            isValueIdentity(input, key.startsWith(\"scale\") ? 1 : 0)\n          ]\n        );\n      }\n    });\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms);\n    }\n    super(style);\n  }\n};\nvar FluidTransform = class extends FluidValue {\n  constructor(inputs, transforms) {\n    super();\n    this.inputs = inputs;\n    this.transforms = transforms;\n    this._value = null;\n  }\n  get() {\n    return this._value || (this._value = this._get());\n  }\n  _get() {\n    let transform = \"\";\n    let identity = true;\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0]);\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      );\n      transform += \" \" + t;\n      identity = identity && id;\n    });\n    return identity ? \"none\" : transform;\n  }\n  // Start observing our inputs once we have an observer.\n  observerAdded(count) {\n    if (count == 1)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      );\n  }\n  // Stop observing our inputs once we have no observers.\n  observerRemoved(count) {\n    if (count == 0)\n      each(\n        this.inputs,\n        (input) => each(\n          input,\n          (value) => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      );\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._value = null;\n    }\n    callFluidObservers(this, event);\n  }\n};\n\n// src/primitives.ts\nvar primitives = [\n  \"a\",\n  \"abbr\",\n  \"address\",\n  \"area\",\n  \"article\",\n  \"aside\",\n  \"audio\",\n  \"b\",\n  \"base\",\n  \"bdi\",\n  \"bdo\",\n  \"big\",\n  \"blockquote\",\n  \"body\",\n  \"br\",\n  \"button\",\n  \"canvas\",\n  \"caption\",\n  \"cite\",\n  \"code\",\n  \"col\",\n  \"colgroup\",\n  \"data\",\n  \"datalist\",\n  \"dd\",\n  \"del\",\n  \"details\",\n  \"dfn\",\n  \"dialog\",\n  \"div\",\n  \"dl\",\n  \"dt\",\n  \"em\",\n  \"embed\",\n  \"fieldset\",\n  \"figcaption\",\n  \"figure\",\n  \"footer\",\n  \"form\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"h4\",\n  \"h5\",\n  \"h6\",\n  \"head\",\n  \"header\",\n  \"hgroup\",\n  \"hr\",\n  \"html\",\n  \"i\",\n  \"iframe\",\n  \"img\",\n  \"input\",\n  \"ins\",\n  \"kbd\",\n  \"keygen\",\n  \"label\",\n  \"legend\",\n  \"li\",\n  \"link\",\n  \"main\",\n  \"map\",\n  \"mark\",\n  \"menu\",\n  \"menuitem\",\n  \"meta\",\n  \"meter\",\n  \"nav\",\n  \"noscript\",\n  \"object\",\n  \"ol\",\n  \"optgroup\",\n  \"option\",\n  \"output\",\n  \"p\",\n  \"param\",\n  \"picture\",\n  \"pre\",\n  \"progress\",\n  \"q\",\n  \"rp\",\n  \"rt\",\n  \"ruby\",\n  \"s\",\n  \"samp\",\n  \"script\",\n  \"section\",\n  \"select\",\n  \"small\",\n  \"source\",\n  \"span\",\n  \"strong\",\n  \"style\",\n  \"sub\",\n  \"summary\",\n  \"sup\",\n  \"table\",\n  \"tbody\",\n  \"td\",\n  \"textarea\",\n  \"tfoot\",\n  \"th\",\n  \"thead\",\n  \"time\",\n  \"title\",\n  \"tr\",\n  \"track\",\n  \"u\",\n  \"ul\",\n  \"var\",\n  \"video\",\n  \"wbr\",\n  // SVG\n  \"circle\",\n  \"clipPath\",\n  \"defs\",\n  \"ellipse\",\n  \"foreignObject\",\n  \"g\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"mask\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"stop\",\n  \"svg\",\n  \"text\",\n  \"tspan\"\n];\n\n// src/index.ts\nexport * from \"@react-spring/core\";\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors\n});\nvar host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: (style) => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props\n});\nvar animated = host.animated;\nexport {\n  animated as a,\n  animated\n};\n", "import { useMemo } from 'react'\nimport { useSpring, animated } from '@react-spring/web'\nimport { lineRadial, CurveFactory } from 'd3-shape'\nimport { ScaleLinear } from 'd3-scale'\nimport { useMotionConfig, useAnimatedPath } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { useInheritedColor } from '@nivo/colors'\nimport { RadarCommonProps, RadarSvgProps } from './types'\n\ninterface RadarLayerProps<D extends Record<string, unknown>> {\n    data: readonly D[]\n    item: string\n    colorByKey: Record<string | number, string>\n    fillByKey: Record<string, string | null>\n    radiusScale: ScaleLinear<number, number>\n    rotation: number\n    angleStep: number\n    curveFactory: CurveFactory\n    borderWidth: RadarCommonProps<D>['borderWidth']\n    borderColor: RadarCommonProps<D>['borderColor']\n    fillOpacity: RadarCommonProps<D>['fillOpacity']\n    blendMode: RadarCommonProps<D>['blendMode']\n    onClick?: RadarSvgProps<D>['onClick']\n}\n\nexport const RadarLayer = <D extends Record<string, unknown>>({\n    data,\n    item: key,\n    colorByKey,\n    fillByKey,\n    radiusScale,\n    rotation,\n    angleStep,\n    curveFactory,\n    borderWidth,\n    borderColor,\n    fillOpacity,\n    blendMode,\n}: RadarLayerProps<D>) => {\n    const theme = useTheme()\n    const getBorderColor = useInheritedColor(borderColor, theme)\n\n    const lineGenerator = useMemo(() => {\n        return lineRadial<number>()\n            .radius(d => radiusScale(d))\n            .angle((_, i) => rotation + i * angleStep)\n            .curve(curveFactory)\n    }, [radiusScale, rotation, angleStep, curveFactory])\n\n    const { animate, config: springConfig } = useMotionConfig()\n    const animatedPath = useAnimatedPath(lineGenerator(data.map(d => d[key] as number)) as string)\n    const animatedProps = useSpring<{ fill: string; stroke: string }>({\n        fill: colorByKey[key],\n        stroke: getBorderColor({ key, color: colorByKey[key] }),\n        config: springConfig,\n        immediate: !animate,\n    })\n    const fill = fillByKey[key] ?? animatedProps.fill\n\n    return (\n        <animated.path\n            key={key}\n            d={animatedPath}\n            fill={fill}\n            fillOpacity={fillOpacity}\n            stroke={animatedProps.stroke}\n            strokeWidth={borderWidth}\n            style={{ mixBlendMode: blendMode }}\n        />\n    )\n}\n", "import { createElement } from 'react'\nimport { useSprings } from '@react-spring/web'\nimport { useMotionConfig, positionFromAngle, radiansToDegrees } from '@nivo/core'\nimport { GridLabelComponent } from './types'\n\nconst textAnchorFromAngle = (_angle: number) => {\n    const angle = radiansToDegrees(_angle) + 90\n\n    if (angle <= 10 || angle >= 350 || (angle >= 170 && angle <= 190)) return 'middle' as const\n    if (angle > 180) return 'end' as const\n    return 'start' as const\n}\n\ninterface RadarGridLabelsProps {\n    radius: number\n    angles: number[]\n    indices: string[]\n    label: GridLabelComponent\n    labelOffset: number\n}\n\nexport const RadarGridLabels = ({\n    radius,\n    angles,\n    indices,\n    label: labelComponent,\n    labelOffset,\n}: RadarGridLabelsProps) => {\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const labels = indices.map((index, i) => {\n        const position = positionFromAngle(angles[i], radius + labelOffset)\n        const textAnchor = textAnchorFromAngle(angles[i])\n\n        return {\n            id: index,\n            angle: radiansToDegrees(angles[i]),\n            anchor: textAnchor,\n            ...position,\n        }\n    })\n\n    const springs = useSprings(\n        labels.length,\n        labels.map(label => ({\n            transform: `translate(${label.x}, ${label.y})`,\n            config: springConfig,\n            immediate: !animate,\n        }))\n    )\n\n    return (\n        <>\n            {springs.map((animatedProps, index) => {\n                const label = labels[index]\n\n                return createElement(labelComponent, {\n                    key: label.id,\n                    id: label.id,\n                    anchor: label.anchor,\n                    angle: label.angle,\n                    x: label.x,\n                    y: label.y,\n                    animated: animatedProps,\n                })\n            })}\n        </>\n    )\n}\n", "import { memo, SVGProps, useMemo } from 'react'\nimport { lineRadial, curveLinearClosed } from 'd3-shape'\nimport { animated, useSpring, to } from '@react-spring/web'\nimport { useAnimatedPath, useMotionConfig } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { RadarCommonProps } from './types'\n\ninterface RadarGridLevelCircularProps {\n    radius: number\n}\n\nconst RadarGridLevelCircular = memo(({ radius }: RadarGridLevelCircularProps) => {\n    const theme = useTheme()\n    const { animate, config: springConfig } = useMotionConfig()\n\n    const animatedProps = useSpring({\n        radius,\n        config: springConfig,\n        immediate: !animate,\n    })\n\n    return (\n        <animated.circle\n            fill=\"none\"\n            r={to(animatedProps.radius, value => Math.max(value, 0))}\n            {...(theme.grid.line as Omit<SVGProps<SVGCircleElement>, 'ref'>)}\n        />\n    )\n})\n\ninterface RadarGridLevelLinearProps {\n    radius: number\n    rotation: number\n    angleStep: number\n    dataLength: number\n}\n\nconst RadarGridLevelLinear = ({\n    radius,\n    rotation,\n    angleStep,\n    dataLength,\n}: RadarGridLevelLinearProps) => {\n    const theme = useTheme()\n\n    const radarLineGenerator = useMemo(\n        () =>\n            lineRadial<number>()\n                .angle(i => rotation + i * angleStep)\n                .radius(radius)\n                .curve(curveLinearClosed),\n        [rotation, angleStep, radius]\n    )\n\n    const points = Array.from({ length: dataLength }, (_, i) => i)\n    const animatedPath = useAnimatedPath(radarLineGenerator(points) as string)\n\n    return (\n        <animated.path\n            fill=\"none\"\n            d={animatedPath}\n            {...(theme.grid.line as Omit<SVGProps<SVGPathElement>, 'ref'>)}\n        />\n    )\n}\n\ninterface RadarGridLevelsProps<D extends Record<string, unknown>> {\n    shape: RadarCommonProps<D>['gridShape']\n    radius: number\n    rotation: number\n    angleStep: number\n    dataLength: number\n}\n\nexport const RadarGridLevels = <D extends Record<string, unknown>>({\n    shape,\n    ...props\n}: RadarGridLevelsProps<D>) => {\n    return shape === 'circular' ? (\n        <RadarGridLevelCircular radius={props.radius} />\n    ) : (\n        <RadarGridLevelLinear {...props} />\n    )\n}\n", "import { SVGProps, useMemo } from 'react'\nimport { positionFromAngle } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { RadarGridLabels } from './RadarGridLabels'\nimport { RadarGridLevels } from './RadarGridLevels'\nimport { GridLabelComponent, RadarCommonProps } from './types'\n\ninterface RadarGridProps<D extends Record<string, unknown>> {\n    indices: string[]\n    shape: RadarCommonProps<D>['gridShape']\n    radius: number\n    levels: number\n    rotation: number\n    angleStep: number\n    label: GridLabelComponent\n    labelOffset: number\n}\n\nexport const RadarGrid = <D extends Record<string, unknown>>({\n    indices,\n    levels,\n    shape,\n    radius,\n    rotation,\n    angleStep,\n    label,\n    labelOffset,\n}: RadarGridProps<D>) => {\n    const theme = useTheme()\n    const { radii, angles } = useMemo(() => {\n        return {\n            radii: Array.from({ length: levels })\n                .map((_, i) => (radius / levels) * (i + 1))\n                .reverse(),\n            angles: Array.from({ length: indices.length }).map(\n                (_, i) => rotation + i * angleStep - Math.PI / 2\n            ),\n        }\n    }, [indices, levels, radius, rotation, angleStep])\n\n    return (\n        <>\n            {angles.map((angle, i) => {\n                const position = positionFromAngle(angle, radius)\n                return (\n                    <line\n                        key={`axis.${i}`}\n                        x1={0}\n                        y1={0}\n                        x2={position.x}\n                        y2={position.y}\n                        {...(theme.grid.line as SVGProps<SVGLineElement>)}\n                    />\n                )\n            })}\n            {radii.map((radius, i) => (\n                <RadarGridLevels<D>\n                    key={`level.${i}`}\n                    shape={shape}\n                    radius={radius}\n                    rotation={rotation}\n                    angleStep={angleStep}\n                    dataLength={indices.length}\n                />\n            ))}\n            <RadarGridLabels\n                radius={radius}\n                angles={angles}\n                indices={indices}\n                labelOffset={labelOffset}\n                label={label}\n            />\n        </>\n    )\n}\n", "import { useMemo, useState, useCallback, createElement, MouseEvent } from 'react'\nimport { Arc } from 'd3-shape'\nimport { positionFromAngle } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { useTooltip } from '@nivo/tooltip'\nimport { RadarCommonProps, RadarDataProps, RadarSliceTooltipDatum, RadarSvgProps } from './types'\n\ninterface RadarSliceProps<D extends Record<string, unknown>> {\n    datum: D\n    keys: RadarDataProps<D>['keys']\n    index: string | number\n    formatValue: (value: number, context: string) => string\n    colorByKey: Record<string, string>\n    startAngle: number\n    endAngle: number\n    radius: number\n    arcGenerator: Arc<void, { startAngle: number; endAngle: number }>\n    tooltip: RadarCommonProps<D>['sliceTooltip']\n    onClick?: RadarSvgProps<D>['onClick']\n}\n\nexport const RadarSlice = <D extends Record<string, unknown>>({\n    datum,\n    keys,\n    index,\n    formatValue,\n    colorByKey,\n    radius,\n    startAngle,\n    endAngle,\n    arcGenerator,\n    tooltip,\n    onClick,\n}: RadarSliceProps<D>) => {\n    const [isHover, setIsHover] = useState(false)\n    const theme = useTheme()\n    const { showTooltipFromEvent, hideTooltip } = useTooltip()\n\n    const handleClick = useCallback(\n        (event: MouseEvent<SVGPathElement>) => onClick?.(datum, event),\n        [onClick, datum]\n    )\n\n    const tooltipData = useMemo(() => {\n        const data: RadarSliceTooltipDatum[] = keys.map(key => ({\n            color: colorByKey[key],\n            id: key,\n            value: datum[key] as number,\n            formattedValue: formatValue(datum[key] as number, key),\n        }))\n        data.sort((a, b) => a.value - b.value)\n        data.reverse()\n\n        return data\n    }, [datum, keys, formatValue, colorByKey])\n\n    const showItemTooltip = useCallback(\n        (event: MouseEvent<SVGPathElement>) => {\n            setIsHover(true)\n            showTooltipFromEvent(\n                createElement(tooltip, {\n                    index,\n                    data: tooltipData,\n                }),\n                event\n            )\n        },\n        [showTooltipFromEvent, tooltip, index, tooltipData]\n    )\n\n    const hideItemTooltip = useCallback(() => {\n        setIsHover(false)\n        hideTooltip()\n    }, [hideTooltip, setIsHover])\n\n    const { path, tipX, tipY } = useMemo(() => {\n        const position = positionFromAngle(\n            startAngle + (endAngle - startAngle) * 0.5 - Math.PI / 2,\n            radius\n        )\n\n        return {\n            path: arcGenerator({ startAngle, endAngle }) as string,\n            tipX: position.x,\n            tipY: position.y,\n        }\n    }, [startAngle, endAngle, radius, arcGenerator])\n\n    return (\n        <>\n            {isHover && <line x1={0} y1={0} x2={tipX} y2={tipY} style={theme.crosshair.line} />}\n            <path\n                d={path}\n                fill=\"#F00\"\n                fillOpacity={0}\n                onMouseEnter={showItemTooltip}\n                onMouseMove={showItemTooltip}\n                onMouseLeave={hideItemTooltip}\n                onClick={handleClick}\n            />\n        </>\n    )\n}\n", "import { arc as d3Arc } from 'd3-shape'\nimport { RadarSlice } from './RadarSlice'\nimport { RadarColorMapping, RadarCommonProps, RadarDataProps, RadarSvgProps } from './types'\n\ninterface RadarSlicesProps<D extends Record<string, unknown>> {\n    data: RadarDataProps<D>['data']\n    keys: RadarDataProps<D>['keys']\n    getIndex: (d: D) => string | number\n    formatValue: (value: number, context: string) => string\n    colorByKey: RadarColorMapping\n    radius: number\n    rotation: number\n    angleStep: number\n    tooltip: RadarCommonProps<D>['sliceTooltip']\n    onClick?: RadarSvgProps<D>['onClick']\n}\n\nexport const RadarSlices = <D extends Record<string, unknown>>({\n    data,\n    keys,\n    getIndex,\n    formatValue,\n    colorByKey,\n    radius,\n    rotation,\n    angleStep,\n    tooltip,\n    onClick,\n}: RadarSlicesProps<D>) => {\n    const arc = d3Arc<{ startAngle: number; endAngle: number }>().outerRadius(radius).innerRadius(0)\n\n    const halfAngleStep = angleStep * 0.5\n    let rootStartAngle = rotation - halfAngleStep\n\n    return (\n        <>\n            {data.map(d => {\n                const index = getIndex(d)\n                const startAngle = rootStartAngle\n                const endAngle = startAngle + angleStep\n\n                rootStartAngle += angleStep\n\n                return (\n                    <RadarSlice\n                        key={index}\n                        datum={d}\n                        keys={keys}\n                        index={index}\n                        formatValue={formatValue}\n                        colorByKey={colorByKey}\n                        startAngle={startAngle}\n                        endAngle={endAngle}\n                        radius={radius}\n                        arcGenerator={arc}\n                        tooltip={tooltip}\n                        onClick={onClick}\n                    />\n                )\n            })}\n        </>\n    )\n}\n", "import { useMemo } from 'react'\nimport { ScaleLinear } from 'd3-scale'\nimport { position<PERSON>romAngle, DotsItem, usePropertyAccessor } from '@nivo/core'\nimport { useTheme } from '@nivo/theming'\nimport { getInheritedColorGenerator } from '@nivo/colors'\nimport { RadarCommonProps, RadarDataProps, PointProps, PointData, RadarColorMapping } from './types'\n\ninterface RadarDotsProps<D extends Record<string, unknown>> {\n    data: RadarDataProps<D>['data']\n    keys: RadarDataProps<D>['keys']\n    radiusScale: ScaleLinear<number, number>\n    getIndex: (d: D) => string\n    colorByKey: RadarColorMapping\n    rotation: number\n    angleStep: number\n    symbol?: RadarCommonProps<D>['dotSymbol']\n    size: number\n    color: RadarCommonProps<D>['dotColor']\n    borderWidth: number\n    borderColor: RadarCommonProps<D>['dotBorderColor']\n    enableLabel: boolean\n    label: RadarCommonProps<D>['dotLabel']\n    formatValue: (value: number, context: string) => string\n    labelYOffset: number\n}\n\nexport const RadarDots = <D extends Record<string, unknown>>({\n    data,\n    keys,\n    getIndex,\n    colorByKey,\n    radiusScale,\n    rotation,\n    angleStep,\n    symbol,\n    size = 6,\n    color = { from: 'color' },\n    borderWidth = 0,\n    borderColor = { from: 'color' },\n    enableLabel = false,\n    label = 'value',\n    formatValue,\n    labelYOffset,\n}: RadarDotsProps<D>) => {\n    const theme = useTheme()\n    const fillColor = getInheritedColorGenerator(color, theme)\n    const strokeColor = getInheritedColorGenerator(borderColor, theme)\n    const getLabel = usePropertyAccessor<PointData, string | number>(label)\n\n    const points: PointProps[] = useMemo(\n        () =>\n            data.reduce((acc, datum, i) => {\n                const index = getIndex(datum)\n                keys.forEach(key => {\n                    const value = datum[key] as number\n\n                    const pointData: PointData = {\n                        index,\n                        key,\n                        value,\n                        formattedValue: formatValue(value, key),\n                        color: colorByKey[key],\n                    }\n\n                    acc.push({\n                        key: `${key}.${index}`,\n                        label: enableLabel ? getLabel(pointData) : undefined,\n                        style: {\n                            fill: fillColor(pointData),\n                            stroke: strokeColor(pointData),\n                            ...positionFromAngle(\n                                rotation + angleStep * i - Math.PI / 2,\n                                radiusScale(datum[key] as number)\n                            ),\n                        },\n                        data: pointData,\n                    })\n                })\n\n                return acc\n            }, [] as PointProps[]),\n        [\n            data,\n            keys,\n            getIndex,\n            colorByKey,\n            enableLabel,\n            getLabel,\n            formatValue,\n            fillColor,\n            strokeColor,\n            rotation,\n            angleStep,\n            radiusScale,\n        ]\n    )\n\n    return (\n        <>\n            {points.map(point => (\n                <DotsItem\n                    key={point.key}\n                    x={point.style.x}\n                    y={point.style.y}\n                    symbol={symbol}\n                    size={size}\n                    color={point.style.fill}\n                    borderWidth={borderWidth}\n                    borderColor={point.style.stroke}\n                    label={point.label}\n                    labelYOffset={labelYOffset}\n                    datum={point.data}\n                />\n            ))}\n        </>\n    )\n}\n", "import { RadarGridLabel } from './RadarGridLabel'\nimport { RadarSliceTooltip } from './RadarSliceTooltip'\nimport { RadarLayerId } from './types'\n\nexport const svgDefaultProps = {\n    layers: ['grid', 'layers', 'slices', 'dots', 'legends'] as RadarLayerId[],\n\n    maxValue: 'auto' as const,\n\n    rotation: 0,\n\n    curve: 'linearClosed' as const,\n\n    borderWidth: 2,\n    borderColor: { from: 'color' },\n\n    gridLevels: 5,\n    gridShape: 'circular' as const,\n    gridLabelOffset: 16,\n    gridLabel: RadarGridLabel,\n\n    enableDots: true,\n    dotSize: 6,\n    dotColor: { from: 'color' },\n    dotBorderWidth: 0,\n    dotBorderColor: { from: 'color' },\n    enableDotLabel: false,\n    dotLabel: 'formattedValue',\n    dotLabelYOffset: -12,\n\n    colors: { scheme: 'nivo' as const },\n    fillOpacity: 0.25,\n    blendMode: 'normal' as const,\n\n    isInteractive: true,\n    sliceTooltip: RadarSliceTooltip,\n\n    legends: [],\n    role: 'img',\n\n    animate: true,\n    motionConfig: 'gentle' as const,\n\n    defs: [],\n    fill: [],\n}\n", "import { animated } from '@react-spring/web'\nimport { useTheme } from '@nivo/theming'\nimport { Text } from '@nivo/text'\nimport { GridLabelProps } from './types'\n\nexport const RadarGridLabel = ({ id, anchor, animated: animatedProps }: GridLabelProps) => {\n    const theme = useTheme()\n\n    return (\n        <animated.g transform={animatedProps.transform}>\n            <Text style={theme.axis.ticks.text} dominantBaseline=\"central\" textAnchor={anchor}>\n                {id}\n            </Text>\n        </animated.g>\n    )\n}\n", "import { useMemo } from 'react'\nimport { TableTooltip, Chip } from '@nivo/tooltip'\nimport { RadarSliceTooltipProps } from './types'\n\nexport const RadarSliceTooltip = ({ index, data }: RadarSliceTooltipProps) => {\n    const rows = useMemo(\n        () =>\n            data.map(datum => [\n                <Chip key={datum.id} color={datum.color} />,\n                datum.id,\n                datum.formattedValue,\n            ]),\n        [data]\n    )\n\n    return <TableTooltip title={<strong>{index}</strong>} rows={rows} />\n}\n", "import { ReactNode, Fragment, createElement, forwardRef, Ref, ReactElement } from 'react'\nimport { Container, useDimensions, SvgWrapper, WithChartRef } from '@nivo/core'\nimport { BoxLegendSvg } from '@nivo/legends'\nimport { RadarLayer } from './RadarLayer'\nimport { RadarGrid } from './RadarGrid'\nimport { RadarSlices } from './RadarSlices'\nimport { RadarDots } from './RadarDots'\nimport { svgDefaultProps } from './defaults'\nimport { RadarLayerId, RadarSvgProps } from './types'\nimport { useRadar } from './hooks'\n\ntype InnerRadarProps<D extends Record<string, unknown>> = Omit<\n    RadarSvgProps<D>,\n    'animate' | 'motionConfig' | 'renderWrapper' | 'theme'\n> & {\n    forwardedRef: Ref<SVGSVGElement>\n}\n\nconst InnerRadar = <D extends Record<string, unknown>>({\n    data,\n    keys,\n    indexBy,\n    layers = svgDefaultProps.layers,\n    rotation: rotationDegrees = svgDefaultProps.rotation,\n    maxValue = svgDefaultProps.maxValue,\n    valueFormat,\n    curve = svgDefaultProps.curve,\n    margin: partialMargin,\n    width,\n    height,\n    borderWidth = svgDefaultProps.borderWidth,\n    borderColor = svgDefaultProps.borderColor,\n    gridLevels = svgDefaultProps.gridLevels,\n    gridShape = svgDefaultProps.gridShape,\n    gridLabel = svgDefaultProps.gridLabel,\n    gridLabelOffset = svgDefaultProps.gridLabelOffset,\n    enableDots = svgDefaultProps.enableDots,\n    dotSymbol,\n    dotSize = svgDefaultProps.dotSize,\n    dotColor = svgDefaultProps.dotColor,\n    dotBorderWidth = svgDefaultProps.dotBorderWidth,\n    dotBorderColor = svgDefaultProps.dotBorderColor,\n    enableDotLabel = svgDefaultProps.enableDotLabel,\n    dotLabel = svgDefaultProps.dotLabel,\n    dotLabelYOffset = svgDefaultProps.dotLabelYOffset,\n    colors = svgDefaultProps.colors,\n    fillOpacity = svgDefaultProps.fillOpacity,\n    blendMode = svgDefaultProps.blendMode,\n    isInteractive = svgDefaultProps.isInteractive,\n    sliceTooltip = svgDefaultProps.sliceTooltip,\n    legends = svgDefaultProps.legends,\n    role,\n    ariaLabel,\n    ariaLabelledBy,\n    ariaDescribedBy,\n    defs = svgDefaultProps.defs,\n    fill = svgDefaultProps.fill,\n    onClick,\n    forwardedRef,\n}: InnerRadarProps<D>) => {\n    const { margin, innerWidth, innerHeight, outerWidth, outerHeight } = useDimensions(\n        width,\n        height,\n        partialMargin\n    )\n\n    const {\n        getIndex,\n        indices,\n        formatValue,\n        colorByKey,\n        fillByKey,\n        boundDefs,\n        rotation,\n        radius,\n        radiusScale,\n        centerX,\n        centerY,\n        angleStep,\n        curveFactory,\n        boundLegends,\n        customLayerProps,\n    } = useRadar<D>({\n        data,\n        keys,\n        indexBy,\n        rotationDegrees,\n        maxValue,\n        valueFormat,\n        curve,\n        width: innerWidth,\n        height: innerHeight,\n        colors,\n        legends,\n        defs,\n        fill,\n    })\n\n    const layerById: Record<RadarLayerId, ReactNode> = {\n        grid: null,\n        layers: null,\n        slices: null,\n        dots: null,\n        legends: null,\n    }\n\n    if (layers.includes('grid')) {\n        layerById.grid = (\n            <g key=\"grid\" transform={`translate(${centerX}, ${centerY})`}>\n                <RadarGrid<D>\n                    levels={gridLevels}\n                    shape={gridShape}\n                    radius={radius}\n                    rotation={rotation}\n                    angleStep={angleStep}\n                    indices={indices}\n                    label={gridLabel}\n                    labelOffset={gridLabelOffset}\n                />\n            </g>\n        )\n    }\n\n    if (layers.includes('layers')) {\n        layerById.layers = (\n            <g key=\"layers\" transform={`translate(${centerX}, ${centerY})`}>\n                {keys.map(key => (\n                    <RadarLayer<D>\n                        key={key}\n                        data={data}\n                        item={key}\n                        colorByKey={colorByKey}\n                        fillByKey={fillByKey}\n                        radiusScale={radiusScale}\n                        rotation={rotation}\n                        angleStep={angleStep}\n                        curveFactory={curveFactory}\n                        borderWidth={borderWidth}\n                        borderColor={borderColor}\n                        fillOpacity={fillOpacity}\n                        blendMode={blendMode}\n                    />\n                ))}\n            </g>\n        )\n    }\n\n    if (layers.includes('slices') && isInteractive) {\n        layerById.slices = (\n            <g key=\"slices\" transform={`translate(${centerX}, ${centerY})`}>\n                <RadarSlices<D>\n                    data={data}\n                    keys={keys}\n                    getIndex={getIndex}\n                    formatValue={formatValue}\n                    colorByKey={colorByKey}\n                    radius={radius}\n                    rotation={rotation}\n                    angleStep={angleStep}\n                    tooltip={sliceTooltip}\n                    onClick={onClick}\n                />\n            </g>\n        )\n    }\n\n    if (layers.includes('dots') && enableDots) {\n        layerById.dots = (\n            <g key=\"dots\" transform={`translate(${centerX}, ${centerY})`}>\n                <RadarDots<D>\n                    data={data}\n                    keys={keys}\n                    getIndex={getIndex}\n                    radiusScale={radiusScale}\n                    rotation={rotation}\n                    angleStep={angleStep}\n                    symbol={dotSymbol}\n                    size={dotSize}\n                    colorByKey={colorByKey}\n                    color={dotColor}\n                    borderWidth={dotBorderWidth}\n                    borderColor={dotBorderColor}\n                    enableLabel={enableDotLabel}\n                    label={dotLabel}\n                    formatValue={formatValue}\n                    labelYOffset={dotLabelYOffset}\n                />\n            </g>\n        )\n    }\n\n    if (layers.includes('legends')) {\n        layerById.legends = (\n            <Fragment key=\"legends\">\n                {boundLegends.map((legend, i) => (\n                    <BoxLegendSvg\n                        key={i}\n                        {...legend}\n                        containerWidth={width}\n                        containerHeight={height}\n                    />\n                ))}\n            </Fragment>\n        )\n    }\n\n    return (\n        <SvgWrapper\n            defs={boundDefs}\n            width={outerWidth}\n            height={outerHeight}\n            margin={margin}\n            role={role}\n            ariaLabel={ariaLabel}\n            ariaLabelledBy={ariaLabelledBy}\n            ariaDescribedBy={ariaDescribedBy}\n            ref={forwardedRef}\n        >\n            {layers.map((layer, i) => {\n                if (typeof layer === 'function') {\n                    return <Fragment key={i}>{createElement(layer, customLayerProps)}</Fragment>\n                }\n\n                return layerById?.[layer] ?? null\n            })}\n        </SvgWrapper>\n    )\n}\n\nexport const Radar = forwardRef(\n    <D extends Record<string, unknown>>(\n        {\n            isInteractive = svgDefaultProps.isInteractive,\n            animate = svgDefaultProps.animate,\n            motionConfig = svgDefaultProps.motionConfig,\n            theme,\n            renderWrapper,\n            ...props\n        }: RadarSvgProps<D>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <Container\n            animate={animate}\n            isInteractive={isInteractive}\n            motionConfig={motionConfig}\n            renderWrapper={renderWrapper}\n            theme={theme}\n        >\n            <InnerRadar<D> isInteractive={isInteractive} {...props} forwardedRef={ref} />\n        </Container>\n    )\n) as <D extends Record<string, unknown>>(\n    props: WithChartRef<RadarSvgProps<D>, SVGSVGElement>\n) => ReactElement\n", "import { useMemo } from 'react'\nimport { scaleLinear } from 'd3-scale'\nimport {\n    // @ts-expect-error no types\n    bindDefs,\n    useCurveInterpolation,\n    usePropertyAccessor,\n    useValueFormatter,\n} from '@nivo/core'\nimport { degreesToRadians } from '@nivo/core'\nimport { useOrdinalColorScale } from '@nivo/colors'\nimport { svgDefaultProps } from './defaults'\nimport {\n    RadarColorMapping,\n    RadarCommonProps,\n    RadarDataProps,\n    RadarCustomLayerProps,\n    RadarSvgProps,\n    BoundLegendProps,\n} from './types'\n\nexport const useRadar = <D extends Record<string, unknown>>({\n    data,\n    keys,\n    indexBy,\n    rotationDegrees,\n    maxValue,\n    valueFormat,\n    curve,\n    width,\n    height,\n    colors = svgDefaultProps.colors,\n    legends,\n    defs,\n    fill,\n}: {\n    data: RadarDataProps<D>['data']\n    keys: RadarDataProps<D>['keys']\n    indexBy: RadarDataProps<D>['indexBy']\n    rotationDegrees: RadarCommonProps<D>['rotation']\n    maxValue: RadarCommonProps<D>['maxValue']\n    valueFormat?: RadarCommonProps<D>['valueFormat']\n    curve: RadarCommonProps<D>['curve']\n    width: number\n    height: number\n    colors: RadarCommonProps<D>['colors']\n    legends: RadarCommonProps<D>['legends']\n    defs: RadarSvgProps<D>['defs']\n    fill: RadarSvgProps<D>['fill']\n}) => {\n    const getIndex = usePropertyAccessor<D, string>(indexBy)\n    const indices = useMemo(() => data.map(getIndex), [data, getIndex])\n    const formatValue = useValueFormatter<number, string>(valueFormat)\n    const rotation = degreesToRadians(rotationDegrees)\n\n    const getColor = useOrdinalColorScale<{ key: string; index: number }>(colors, 'key')\n    const colorByKey: RadarColorMapping = useMemo(\n        () =>\n            keys.reduce<RadarColorMapping>((mapping, key, index) => {\n                mapping[key] = getColor({ key, index })\n                return mapping\n            }, {}),\n        [keys, getColor]\n    )\n\n    const { boundDefs, fillByKey } = useMemo(() => {\n        // expand keys into structure expected by bindDefs\n        const keyData = keys.map(k => ({ key: k, color: colorByKey[k], data, fill: null }))\n        const boundDefs = bindDefs(defs, keyData, fill)\n        const fillByKey = keyData.reduce<Record<string, string | null>>((mapping, keyDatum) => {\n            const { key: keyName, fill } = keyDatum\n            mapping[keyName] = fill\n            return mapping\n        }, {})\n\n        return { boundDefs, fillByKey }\n    }, [keys, data, defs, fill, colorByKey])\n\n    const { radius, radiusScale, centerX, centerY, angleStep } = useMemo(() => {\n        const allValues: number[] = data.reduce(\n            (acc: number[], d) => [...acc, ...keys.map(key => d[key] as number)],\n            [] as number[]\n        )\n        const computedMaxValue = maxValue !== 'auto' ? maxValue : Math.max(...allValues)\n\n        const radius = Math.min(width, height) / 2\n        const radiusScale = scaleLinear<number, number>()\n            .range([0, radius])\n            .domain([0, computedMaxValue])\n\n        return {\n            radius,\n            radiusScale,\n            centerX: width / 2,\n            centerY: height / 2,\n            angleStep: (Math.PI * 2) / data.length,\n        }\n    }, [keys, data, maxValue, width, height])\n\n    const curveFactory = useCurveInterpolation(curve)\n\n    const customLayerProps: RadarCustomLayerProps<D> = useMemo(\n        () => ({\n            data,\n            keys,\n            indices,\n            colorByKey,\n            centerX,\n            centerY,\n            radiusScale,\n            angleStep,\n        }),\n        [data, keys, indices, colorByKey, centerX, centerY, radiusScale, angleStep]\n    )\n\n    const legendData = useMemo(\n        () => keys.map(key => ({ id: key, label: key, color: colorByKey[key] })),\n        [keys, colorByKey]\n    )\n\n    const boundLegends: BoundLegendProps[] = useMemo(\n        () =>\n            legends.map(({ data: customData, ...legend }) => {\n                const boundData = customData?.map(cd => {\n                    const findData = legendData.find(ld => ld.id === cd.id) || {}\n                    return { ...findData, ...cd }\n                })\n                return { ...legend, data: boundData || legendData }\n            }),\n        [legends, legendData]\n    )\n\n    return {\n        getIndex,\n        indices,\n        formatValue,\n        colorByKey,\n        fillByKey,\n        boundDefs,\n        rotation,\n        radius,\n        radiusScale,\n        centerX,\n        centerY,\n        angleStep,\n        curveFactory,\n        legendData,\n        boundLegends,\n        customLayerProps,\n    }\n}\n", "import { forwardRef, Ref, ReactElement } from 'react'\nimport { ResponsiveWrapper, ResponsiveProps, WithChartRef } from '@nivo/core'\nimport { RadarSvgProps } from './types'\nimport { Radar } from './Radar'\n\nexport const ResponsiveRadar = forwardRef(\n    <D extends Record<string, unknown>>(\n        {\n            defaultWidth,\n            defaultHeight,\n            onResize,\n            debounceResize,\n            ...props\n        }: ResponsiveProps<RadarSvgProps<D>>,\n        ref: Ref<SVGSVGElement>\n    ) => (\n        <ResponsiveWrapper\n            defaultWidth={defaultWidth}\n            defaultHeight={defaultHeight}\n            onResize={onResize}\n            debounceResize={debounceResize}\n        >\n            {({ width, height }) => <Radar<D> {...props} width={width} height={height} ref={ref} />}\n        </ResponsiveWrapper>\n    )\n) as <D extends Record<string, unknown>>(\n    props: WithChartRef<ResponsiveProps<RadarSvgProps<D>>, SVGSVGElement>\n) => ReactElement\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,uBAAwC;AAKxC,IAAI,iBAAiB;AACrB,SAAS,oBAAoB,MAAM,OAAO;AACxC,MAAI,SAAS,QAAQ,OAAO,UAAU,aAAa,UAAU,GAAI,QAAO;AACxE,MAAI,OAAO,UAAU,YAAY,UAAU,KAAK,CAAC,eAAe,KAAK,IAAI,KAAK,EAAE,iBAAiB,eAAe,IAAI,KAAK,iBAAiB,IAAI;AAC5I,WAAO,QAAQ;AACjB,UAAQ,KAAK,OAAO,KAAK;AAC3B;AACA,IAAI,iBAAiB,CAAC;AACtB,SAAS,oBAAoB,UAAU,OAAO;AAC5C,MAAI,CAAC,SAAS,YAAY,CAAC,SAAS,cAAc;AAChD,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,SAAS,aAAa,YAAY,SAAS,cAAc,SAAS,WAAW,aAAa;AAClH,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,IACpC,CAAC,SAAS,mBAAmB,SAAS,aAAa,IAAI,IAAI,OAAO,eAAe,IAAI,MAAM,eAAe,IAAI,IAAI,KAAK;AAAA,MACrH;AAAA;AAAA,MAEA,CAACA,OAAM,MAAMA,GAAE,YAAY;AAAA,IAC7B;AAAA,EACF;AACA,MAAI,aAAa,QAAQ;AACvB,aAAS,cAAc;AAAA,EACzB;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,eAAe,IAAI,GAAG;AAC9B,YAAM,QAAQ,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,iBAAS,MAAM,YAAY,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,iBAAS,MAAM,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,MAAMC,OAAM;AACzB,aAAS,aAAa,MAAM,OAAOA,EAAC,CAAC;AAAA,EACvC,CAAC;AACD,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,eAAe,QAAQ;AACzB,aAAS,aAAa;AAAA,EACxB;AACA,MAAI,YAAY,QAAQ;AACtB,aAAS,aAAa,WAAW,OAAO;AAAA,EAC1C;AACF;AACA,IAAI,mBAAmB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AACA,IAAI,YAAY,CAAC,QAAQ,QAAQ,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AACvF,IAAI,WAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAC1C,mBAAmB,OAAO,KAAK,gBAAgB,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,WAAS,QAAQ,CAAC,WAAW,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AACrE,SAAO;AACT,GAAG,gBAAgB;AAgBnB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,UAAU,CAAC,OAAO,SAAS,GAAG,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,OAAO;AAC7E,IAAI,kBAAkB,CAAC,OAAO,OAAO,GAAG,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC,MAAM,gBAAgB,GAAG,EAAE,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,UAAU,KAAK,WAAW,KAAK,MAAM;AACvJ,IAAI,gBAAgB,cAAc,eAAe;AAAA,EAC/C,YAAY,EAAE,GAAG,GAAG,GAAAC,IAAG,GAAG,MAAM,GAAG;AACjC,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,CAAC;AACpB,QAAI,KAAK,KAAKA,IAAG;AACf,aAAO,KAAK,CAAC,KAAK,GAAG,KAAK,GAAGA,MAAK,CAAC,CAAC;AACpC,iBAAW,KAAK,CAAC,QAAQ;AAAA,QACvB,eAAe,IAAI,IAAI,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA;AAAA,QAEzD,gBAAgB,KAAK,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AACA,aAAS,OAAO,CAAC,OAAO,QAAQ;AAC9B,UAAI,QAAQ,aAAa;AACvB,eAAO,KAAK,CAAC,SAAS,EAAE,CAAC;AACzB,mBAAW,KAAK,CAAC,cAAc,CAAC,WAAW,cAAc,EAAE,CAAC;AAAA,MAC9D,WAAW,cAAc,KAAK,GAAG,GAAG;AAClC,eAAO,MAAM,GAAG;AAChB,YAAI,GAAG,IAAI,KAAK,EAAG;AACnB,cAAM,OAAO,aAAa,KAAK,GAAG,IAAI,OAAO,cAAc,KAAK,GAAG,IAAI,QAAQ;AAC/E,eAAO,KAAK,QAAQ,KAAK,CAAC;AAC1B,mBAAW;AAAA,UACT,QAAQ,aAAa,CAAC,CAAC,IAAI,IAAIC,KAAI,GAAG,MAAM;AAAA,YAC1C,YAAY,EAAE,IAAI,EAAE,IAAIA,GAAE,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,YAChD,gBAAgB,KAAK,CAAC;AAAA,UACxB,IAAI,CAAC,UAAU;AAAA,YACb,GAAG,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,YACtD,gBAAgB,OAAO,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY,IAAI,eAAe,QAAQ,UAAU;AAAA,IACzD;AACA,UAAM,KAAK;AAAA,EACb;AACF;AACA,IAAI,iBAAiB,cAAc,WAAW;AAAA,EAC5C,YAAY,QAAQ,YAAY;AAC9B,UAAM;AACN,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM;AACJ,WAAO,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA,EACA,OAAO;AACL,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,SAAK,KAAK,QAAQ,CAAC,OAAOF,OAAM;AAC9B,YAAM,OAAO,cAAc,MAAM,CAAC,CAAC;AACnC,YAAM,CAACG,IAAG,EAAE,IAAI,KAAK,WAAWH,EAAC;AAAA,QAC/B,GAAG,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,aAAa;AAAA,MAC/C;AACA,mBAAa,MAAMG;AACnB,iBAAW,YAAY;AAAA,IACzB,CAAC;AACD,WAAO,WAAW,SAAS;AAAA,EAC7B;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,iBAAiB,OAAO,IAAI;AAAA,QACjE;AAAA,MACF;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,QAAI,SAAS;AACX;AAAA,QACE,KAAK;AAAA,QACL,CAAC,UAAU;AAAA,UACT;AAAA,UACA,CAAC,UAAU,cAAc,KAAK,KAAK,oBAAoB,OAAO,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,EACJ;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,uBAAmB,MAAM,KAAK;AAAA,EAChC;AACF;AAGA,IAAI,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIA,gBAAQ,OAAO;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA;AACF,CAAC;AACD,IAAI,OAAO,WAAW,YAAY;AAAA,EAChC;AAAA,EACA,qBAAqB,CAAC,UAAU,IAAI,cAAc,KAAK;AAAA;AAAA,EAEvD,mBAAmB,CAAC,EAAE,WAAW,YAAY,GAAG,MAAM,MAAM;AAC9D,CAAC;AACD,IAAI,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;ACjWb,IAAMC,IAAa,SAAHC,IAAAA;AAaG,MAAAC,IAZtBC,KAAIF,GAAJE,MACMC,KAAGH,GAATI,MACAC,KAAUL,GAAVK,YACAC,KAASN,GAATM,WACAC,IAAWP,GAAXO,aACAC,IAAQR,GAARQ,UACAC,IAAST,GAATS,WACAC,IAAYV,GAAZU,cACAC,IAAWX,GAAXW,aACAC,IAAWZ,GAAXY,aACAC,IAAWb,GAAXa,aACAC,IAASd,GAATc,WAEMC,IAAQC,EAAAA,GACRC,IAAiBC,GAAkBN,GAAaG,CAAAA,GAEhDI,SAAgBC,aAAAA,SAAQ,WAAA;AAC1B,WAAOC,mBAAAA,EACFC,OAAO,SAAAC,IAAAA;AAAC,aAAIhB,EAAYgB,EAAAA;IAAE,CAAA,EAC1BC,MAAM,SAACC,IAAGC,IAAAA;AAAC,aAAKlB,IAAWkB,KAAIjB;IAAS,CAAA,EACxCkB,MAAMjB,CAAAA;EACd,GAAE,CAACH,GAAaC,GAAUC,GAAWC,CAAAA,CAAAA,GAEtCkB,IAA0CC,GAAAA,GAAlCC,IAAOF,EAAPE,SAAiBC,IAAYH,EAApBI,QACXC,IAAeC,GAAgBf,GAAcjB,GAAKiC,IAAI,SAAAZ,IAAAA;AAAC,WAAIA,GAAEpB,EAAAA;EAAc,CAAA,CAAA,CAAA,GAC3EiC,IAAgBC,UAA4C,EAC9DC,MAAMjC,GAAWF,EAAAA,GACjBoC,QAAQtB,EAAe,EAAEd,KAAAA,IAAKqC,OAAOnC,GAAWF,EAAAA,EAAAA,CAAAA,GAChD6B,QAAQD,GACRU,WAAAA,CAAYX,EAAAA,CAAAA,GAEVQ,IAAqBrC,SAAjBA,KAAGK,GAAUH,EAAAA,KAAIF,KAAImC,EAAcE;AAE7C,aACII,mBAAAA,KAACC,SAASC,MAAI,EAEVrB,GAAGU,GACHK,MAAMA,GACNzB,aAAaA,GACb0B,QAAQH,EAAcG,QACtBM,aAAalC,GACbmC,OAAO,EAAEC,cAAcjC,EAAAA,EAAAA,GANlBX,EAAAA;AASjB;AA7CO,ICJM6C,KAAkB,SAAHhD,IAAAA;AAMA,MALxBsB,KAAMtB,GAANsB,QACA2B,KAAMjD,GAANiD,QACAC,KAAOlD,GAAPkD,SACOC,KAAcnD,GAArBoD,OACAC,KAAWrD,GAAXqD,aAEAzB,IAA0CC,GAAAA,GAAlCC,IAAOF,EAAPE,SAAiBC,IAAYH,EAApBI,QAEXsB,IAASJ,GAAQf,IAAI,SAACoB,IAAO7B,IAAAA;AAC/B,QA1BqB8B,IACnBhC,IAyBIiC,IAAWC,GAAkBT,GAAOvB,EAAAA,GAAIJ,KAAS+B,EAAAA,GACjDM,MA3BeH,KA2BkBP,GAAOvB,EAAAA,IA1B5CF,KAAQoC,GAAiBJ,EAAAA,IAAU,OAE5B,MAAMhC,MAAS,OAAQA,MAAS,OAAOA,MAAS,MAAa,WACtEA,KAAQ,MAAY,QACjB;AAwBH,WAAAqC,EAAA,EACIC,IAAIP,IACJ/B,OAAOoC,GAAiBX,GAAOvB,EAAAA,CAAAA,GAC/BqC,QAAQJ,GAAAA,GACLF,CAAAA;EAEX,CAAA,GAEMO,IAAUC,WACZX,EAAOY,QACPZ,EAAOnB,IAAI,SAAAiB,IAAAA;AAAK,WAAK,EACjBe,WAAS,eAAef,GAAMgB,IAAAA,OAAMhB,GAAMiB,IAAI,KAC9CrC,QAAQD,GACRU,WAAAA,CAAYX,EAAAA;EACd,CAAA,CAAA;AAGN,aACIY,mBAAAA,KAAA4B,mBAAAA,UAAA,EAAAC,UACKP,EAAQ7B,IAAI,SAACC,IAAemB,IAAAA;AACzB,QAAMH,KAAQE,EAAOC,EAAAA;AAErB,eAAOiB,aAAAA,eAAcrB,IAAgB,EACjChD,KAAKiD,GAAMU,IACXA,IAAIV,GAAMU,IACVC,QAAQX,GAAMW,QACdvC,OAAO4B,GAAM5B,OACb4C,GAAGhB,GAAMgB,GACTC,GAAGjB,GAAMiB,GACT1B,UAAUP,GAAAA,CAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAK9B;AD3CO,IC2CP,IAAA,CAAA,OAAA;AD3CO,IEdDqC,QAAyBC,aAAAA,MAAK,SAAA1E,IAAAA;AAA6C,MAA1CsB,KAAMtB,GAANsB,QAC7BP,KAAQC,EAAAA,GACdY,KAA0CC,GAAAA,GAAlCC,KAAOF,GAAPE,SAAiBC,KAAYH,GAApBI,QAEXI,KAAgBC,UAAU,EAC5Bf,QAAAA,IACAU,QAAQD,IACRU,WAAAA,CAAYX,GAAAA,CAAAA;AAGhB,aACIY,mBAAAA,KAACC,SAASgC,QAAMd,EAAA,EACZvB,MAAK,QACLsC,GAAGC,GAAGzC,GAAcd,QAAQ,SAAAwD,IAAAA;AAAK,WAAIC,KAAKC,IAAIF,IAAO,CAAA;EAAE,CAAA,EAAA,GAClD/D,GAAMkE,KAAKC,IAAAA,CAAAA;AAG5B,CAAA;AFHO,IEYDC,IAAuB,SAAHC,IAAAA;AAKO,MAJ7B9D,KAAM8D,GAAN9D,QACAd,KAAQ4E,GAAR5E,UACAC,KAAS2E,GAAT3E,WACA4E,KAAUD,GAAVC,YAEMtE,KAAQC,EAAAA,GAERsE,QAAqBlE,aAAAA,SACvB,WAAA;AAAA,WACIC,mBAAAA,EACKG,MAAM,SAAAE,IAAAA;AAAC,aAAIlB,KAAWkB,KAAIjB;IAAU,CAAA,EACpCa,OAAOA,EAAAA,EACPK,MAAM4D,oBAAAA;EAAkB,GACjC,CAAC/E,IAAUC,IAAWa,EAAAA,CAAAA,GAGpBkE,IAASC,MAAMC,KAAK,EAAExB,QAAQmB,GAAAA,GAAc,SAAC5D,IAAGC,IAAAA;AAAC,WAAKA;EAAAA,CAAAA,GACtDO,IAAeC,GAAgBoD,EAAmBE,CAAAA,CAAAA;AAExD,aACI9C,mBAAAA,KAACC,SAASC,MAAIiB,EAAA,EACVvB,MAAK,QACLf,GAAGU,EAAAA,GACElB,GAAMkE,KAAKC,IAAAA,CAAAA;AAG5B;AFvCO,IEiDMS,IAAkB,SAAHC,IAAAA;AAGG,MAF3BC,KAAKD,GAALC,OACGC,KAAKC,EAAAH,IAAAI,CAAAA;AAER,SAAiB,eAAVH,SACHnD,mBAAAA,KAAC+B,GAAsB,EAACnD,QAAQwE,GAAMxE,OAAAA,CAAAA,QAEtCoB,mBAAAA,KAACyC,GAAoBtB,EAAA,CAAA,GAAKiC,EAAAA,CAAAA;AAElC;AF1DO,IGPMG,IAAY,SAAHjG,IAAAA;AASG,MARrBkD,KAAOlD,GAAPkD,SACAgD,KAAMlG,GAANkG,QACAL,KAAK7F,GAAL6F,OACAvE,KAAMtB,GAANsB,QACAd,KAAQR,GAARQ,UACAC,IAAST,GAATS,WACA2C,IAAKpD,GAALoD,OACAC,IAAWrD,GAAXqD,aAEMtC,IAAQC,EAAAA,GACdmF,QAA0B/E,aAAAA,SAAQ,WAAA;AAC9B,WAAO,EACHgF,OAAOX,MAAMC,KAAK,EAAExB,QAAQgC,GAAAA,CAAAA,EACvB/D,IAAI,SAACV,IAAGC,IAAAA;AAAC,aAAMJ,KAAS4E,MAAWxE,KAAI;IAAG,CAAA,EAC1C2E,QAAAA,GACLpD,QAAQwC,MAAMC,KAAK,EAAExB,QAAQhB,GAAQgB,OAAAA,CAAAA,EAAU/B,IAC3C,SAACV,IAAGC,IAAAA;AAAC,aAAKlB,KAAWkB,KAAIjB,IAAYsE,KAAKuB,KAAK;IAAA,CAAA,EAAA;EAG3D,GAAG,CAACpD,IAASgD,IAAQ5E,IAAQd,IAAUC,CAAAA,CAAAA,GAT/B2F,IAAKD,EAALC,OAAOnD,IAAMkD,EAANlD;AAWf,aACIsD,mBAAAA,MAAAjC,mBAAAA,UAAA,EAAAC,UAAA,CACKtB,EAAOd,IAAI,SAACX,IAAOE,IAAAA;AAChB,QAAM+B,KAAWC,GAAkBlC,IAAOF,EAAAA;AAC1C,eACIoB,mBAAAA,KAAAA,QAAAmB,EAAA,EAEI2C,IAAI,GACJC,IAAI,GACJC,IAAIjD,GAASW,GACbuC,IAAIlD,GAASY,EAAAA,GACRtD,EAAMkE,KAAKC,IAAAA,GAAI,UALPxD,EAAAA;EAQxB,CAAA,GACA0E,EAAMjE,IAAI,SAACb,IAAQI,IAAAA;AAAC,eACjBgB,mBAAAA,KAACiD,GAAe,EAEZE,OAAOA,IACPvE,QAAQA,IACRd,UAAUA,IACVC,WAAWA,GACX4E,YAAYnC,GAAQgB,OAAAA,GAAO,WALbxC,EAAAA;EAMhB,CAAA,OAENgB,mBAAAA,KAACM,IAAe,EACZ1B,QAAQA,IACR2B,QAAQA,GACRC,SAASA,IACTG,aAAaA,GACbD,OAAOA,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIvB;AHjDO,IIJMwD,IAAa,SAAH5G,IAAAA;AAYG,MAXtB6G,KAAK7G,GAAL6G,OACAC,KAAI9G,GAAJ8G,MACAvD,IAAKvD,GAALuD,OACAwD,IAAW/G,GAAX+G,aACA1G,IAAUL,GAAVK,YACAiB,IAAMtB,GAANsB,QACA0F,IAAUhH,GAAVgH,YACAC,IAAQjH,GAARiH,UACAC,IAAYlH,GAAZkH,cACAC,IAAOnH,GAAPmH,SACAC,IAAOpH,GAAPoH,SAEAC,QAA8BC,aAAAA,UAAAA,KAAS,GAAhCC,IAAOF,EAAA,CAAA,GAAEG,KAAUH,EAAA,CAAA,GACpBtG,IAAQC,EAAAA,GACdyG,IAA8CC,EAAAA,GAAtCC,IAAoBF,EAApBE,sBAAsBC,IAAWH,EAAXG,aAExBC,QAAcC,aAAAA,aAChB,SAACC,IAAAA;AAAiC,WAAKX,QAAAA,IAAAA,SAAAA,EAAUP,IAAOkB,EAAAA;EAAM,GAC9D,CAACX,GAASP,EAAAA,CAAAA,GAGRmB,QAAc5G,aAAAA,SAAQ,WAAA;AACxB,QAAMlB,KAAiC4G,GAAK3E,IAAI,SAAAhC,IAAAA;AAAG,aAAK,EACpDqC,OAAOnC,EAAWF,EAAAA,GAClB2D,IAAI3D,IACJ2E,OAAO+B,GAAM1G,EAAAA,GACb8H,gBAAgBlB,EAAYF,GAAM1G,EAAAA,GAAgBA,EAAAA,EAAAA;IACrD,CAAA;AAID,WAHAD,GAAKgI,KAAK,SAACC,IAAGC,IAAAA;AAAC,aAAKD,GAAErD,QAAQsD,GAAEtD;IAAAA,CAAAA,GAChC5E,GAAKmG,QAAAA,GAEEnG;EACV,GAAE,CAAC2G,IAAOC,IAAMC,GAAa1G,CAAAA,CAAAA,GAExBgI,QAAkBP,aAAAA,aACpB,SAACC,IAAAA;AACGP,IAAAA,GAAAA,IAAW,GACXG,MACInD,aAAAA,eAAc2C,GAAS,EACnB5D,OAAAA,GACArD,MAAM8H,EAAAA,CAAAA,GAEVD,EAAAA;EAEP,GACD,CAACJ,GAAsBR,GAAS5D,GAAOyE,CAAAA,CAAAA,GAGrCM,QAAkBR,aAAAA,aAAY,WAAA;AAChCN,IAAAA,GAAAA,KAAW,GACXI,EAAAA;EACJ,GAAG,CAACA,GAAaJ,EAAAA,CAAAA,GAEjBrB,QAA6B/E,aAAAA,SAAQ,WAAA;AACjC,QAAMqC,KAAWC,GACbsD,IAAuC,OAAzBC,IAAWD,KAAoBjC,KAAKuB,KAAK,GACvDhF,CAAAA;AAGJ,WAAO,EACHsB,MAAMsE,EAAa,EAAEF,YAAAA,GAAYC,UAAAA,EAAAA,CAAAA,GACjCsB,MAAM9E,GAASW,GACfoE,MAAM/E,GAASY,EAAAA;EAEtB,GAAE,CAAC2C,GAAYC,GAAU3F,GAAQ4F,CAAAA,CAAAA,GAX1BtE,IAAIuD,EAAJvD,MAAM2F,IAAIpC,EAAJoC,MAAMC,IAAIrC,EAAJqC;AAapB,aACIjC,mBAAAA,MAAAjC,mBAAAA,UAAA,EAAAC,UAAA,CACKgD,SAAW7E,mBAAAA,KAAA,QAAA,EAAM8D,IAAI,GAAGC,IAAI,GAAGC,IAAI6B,GAAM5B,IAAI6B,GAAM1F,OAAO/B,EAAM0H,UAAUvD,KAAAA,CAAAA,OAC3ExC,mBAAAA,KAAA,QAAA,EACInB,GAAGqB,GACHN,MAAK,QACLzB,aAAa,GACb6H,cAAcL,GACdM,aAAaN,GACbO,cAAcN,GACdlB,SAASS,EAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAIzB;AJ7EO,IKRMgB,IAAc,SAAH7I,IAAAA;AAWG,MAVvBE,KAAIF,GAAJE,MACA4G,KAAI9G,GAAJ8G,MACAgC,KAAQ9I,GAAR8I,UACA/B,KAAW/G,GAAX+G,aACA1G,KAAUL,GAAVK,YACAiB,KAAMtB,GAANsB,QACAd,IAAQR,GAARQ,UACAC,IAAST,GAATS,WACA0G,IAAOnH,GAAPmH,SACAC,IAAOpH,GAAPoH,SAEM2B,IAAMC,YAAAA,EAAkDC,YAAY3H,EAAAA,EAAQ4H,YAAY,CAAA,GAG1FC,IAAiB3I,IADa,MAAZC;AAGtB,aACIiC,mBAAAA,KAAA4B,mBAAAA,UAAA,EAAAC,UACKrE,GAAKiC,IAAI,SAAAZ,IAAAA;AACN,QAAMgC,KAAQuF,GAASvH,EAAAA,GACjByF,KAAamC;AAKnB,WAFAA,KAAkB1I,OAGdiC,mBAAAA,KAACkE,GAAU,EAEPC,OAAOtF,IACPuF,MAAMA,IACNvD,OAAOA,IACPwD,aAAaA,IACb1G,YAAYA,IACZ2G,YAAYA,IACZC,UAbSD,KAAavG,GActBa,QAAQA,IACR4F,cAAc6B,GACd5B,SAASA,GACTC,SAASA,EAAAA,GAXJ7D,EAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAiB7B;ALrCO,IMCM6F,IAAY,SAAHpJ,IAAAA;AAiBG,MAhBrBE,KAAIF,GAAJE,MACA4G,KAAI9G,GAAJ8G,MACAgC,KAAQ9I,GAAR8I,UACAzI,KAAUL,GAAVK,YACAE,KAAWP,GAAXO,aACAC,IAAQR,GAARQ,UACAC,IAAST,GAATS,WACA4I,IAAMrJ,GAANqJ,QAAMC,IAAAtJ,GACNuJ,MAAAA,IAAAA,WAAID,IAAG,IAACA,GAAAE,IAAAxJ,GACRwC,OAAAA,IAAAA,WAAKgH,IAAG,EAAE9D,MAAM,QAAA,IAAS8D,GAAAC,IAAAzJ,GACzBW,aAAAA,IAAAA,WAAW8I,IAAG,IAACA,GAAAC,KAAA1J,GACfY,aAAAA,IAAAA,WAAW8I,KAAG,EAAEhE,MAAM,QAAA,IAASgE,IAAAC,IAAA3J,GAC/B4J,aAAAA,IAAAA,WAAWD,KAAQA,GAAAE,IAAA7J,GACnBoD,OAAAA,IAAAA,WAAKyG,IAAG,UAAOA,GACf9C,IAAW/G,GAAX+G,aACA+C,IAAY9J,GAAZ8J,cAEM/I,IAAQC,EAAAA,GACR+I,IAAYC,GAA2BxH,GAAOzB,CAAAA,GAC9CkJ,IAAcD,GAA2BpJ,GAAaG,CAAAA,GACtDmJ,IAAWC,GAAgD/G,CAAAA,GAE3DoC,SAAuBpE,aAAAA,SACzB,WAAA;AAAA,WACIlB,GAAKkK,OAAO,SAACC,IAAKxD,IAAOnF,IAAAA;AACrB,UAAM6B,KAAQuF,GAASjC,EAAAA;AA2BvB,aA1BAC,GAAKwD,QAAQ,SAAAnK,IAAAA;AACT,YAAM2E,KAAQ+B,GAAM1G,EAAAA,GAEdoK,IAAuB,EACzBhH,OAAAA,IACApD,KAAAA,IACA2E,OAAAA,IACAmD,gBAAgBlB,EAAYjC,IAAO3E,EAAAA,GACnCqC,OAAOnC,GAAWF,EAAAA,EAAAA;AAGtBkK,QAAAA,GAAIG,KAAK,EACLrK,KAAQA,KAAG,MAAIoD,IACfH,OAAOwG,IAAcM,EAASK,CAAAA,IAAAA,QAC9BzH,OAAKe,EAAA,EACDvB,MAAMyH,EAAUQ,CAAAA,GAChBhI,QAAQ0H,EAAYM,CAAAA,EAAAA,GACjB7G,GACClD,IAAWC,IAAYiB,KAAIqD,KAAKuB,KAAK,GACrC/F,GAAYsG,GAAM1G,EAAAA,CAAAA,CAAAA,CAAAA,GAG1BD,MAAMqK,EAAAA,CAAAA;MAEd,CAAA,GAEOF;IACV,GAAE,CAAA,CAAA;EACP,GAAA,CACInK,IACA4G,IACAgC,IACAzI,IACAuJ,GACAM,GACAnD,GACAgD,GACAE,GACAzJ,GACAC,GACAF,EAAAA,CAAAA;AAIR,aACImC,mBAAAA,KAAA4B,mBAAAA,UAAA,EAAAC,UACKiB,GAAOrD,IAAI,SAAAsI,IAAAA;AAAK,eACb/H,mBAAAA,KAACgI,IAAQ,EAELtG,GAAGqG,GAAM3H,MAAMsB,GACfC,GAAGoG,GAAM3H,MAAMuB,GACfgF,QAAQA,GACRE,MAAMA,GACN/G,OAAOiI,GAAM3H,MAAMR,MACnB3B,aAAaA,GACbC,aAAa6J,GAAM3H,MAAMP,QACzBa,OAAOqH,GAAMrH,OACb0G,cAAcA,GACdjD,OAAO4D,GAAMvK,KAAAA,GAVRuK,GAAMtK,GAAAA;EAAAA,CAAAA,EAAAA,CAAAA;AAe/B;AN3FO,IOrBMwK,IAAkB,EAC3BC,QAAQ,CAAC,QAAQ,UAAU,UAAU,QAAQ,SAAA,GAE7CC,UAAU,QAEVrK,UAAU,GAEVmB,OAAO,gBAEPhB,aAAa,GACbC,aAAa,EAAE8E,MAAM,QAAA,GAErBoF,YAAY,GACZC,WAAW,YACXC,iBAAiB,IACjBC,WCd0B,SAAHjL,IAAAA;AAAgE,MAA1D8D,KAAE9D,GAAF8D,IAAIC,KAAM/D,GAAN+D,QAAkB3B,KAAapC,GAAvB2C,UACnC5B,KAAQC,EAAAA;AAEd,aACI0B,mBAAAA,KAACC,SAASuI,GAAC,EAAC/G,WAAW/B,GAAc+B,WAAUI,cAC3C7B,mBAAAA,KAACyI,GAAI,EAACrI,OAAO/B,GAAMqK,KAAKC,MAAMC,MAAMC,kBAAiB,WAAU5H,YAAYI,IAAOQ,UAC7ET,GAAAA,CAAAA,EAAAA,CAAAA;AAIjB,GDMI0H,YAAAA,MACAC,SAAS,GACTC,UAAU,EAAEhG,MAAM,QAAA,GAClBiG,gBAAgB,GAChBC,gBAAgB,EAAElG,MAAM,QAAA,GACxBmG,gBAAAA,OACAC,UAAU,kBACVC,iBAAAA,KAEAC,QAAQ,EAAEC,QAAQ,OAAA,GAClBpL,aAAa,MACbC,WAAW,UAEXoL,eAAAA,MACAC,cE/B6B,SAAHnM,IAAAA;AAAgD,MAA1CuD,KAAKvD,GAALuD,OAAOrD,KAAIF,GAAJE,MACjCkM,SAAOhL,aAAAA,SACT,WAAA;AAAA,WACIlB,GAAKiC,IAAI,SAAA0E,IAAAA;AAAK,aAAI,KACdnE,mBAAAA,KAAC2J,GAAI,EAAgB7J,OAAOqE,GAAMrE,MAAAA,GAAvBqE,GAAM/C,EAAAA,GACjB+C,GAAM/C,IACN+C,GAAMoB,cAAAA;IAAAA,CAAAA;EAAAA,GAEd,CAAC/H,EAAAA,CAAAA;AAGL,aAAOwC,mBAAAA,KAAC4J,GAAY,EAACC,WAAO7J,mBAAAA,KAAA,UAAA,EAAA6B,UAAShB,GAAAA,CAAAA,GAAiB6I,MAAMA,GAAAA,CAAAA;AAChE,GFqBII,SAAS,CAAA,GACTC,MAAM,OAEN3K,SAAAA,MACA4K,cAAc,UAEdC,MAAM,CAAA,GACNrK,MAAM,CAAA,EAAA;APnBH,IOmBG,KAAA,CAAA,MAAA;APnBH,IOmBG,KAAA,CAAA,iBAAA,WAAA,gBAAA,SAAA,eAAA;APnBH,IUPDsK,KAAa,SAAH5M,IAAAA;AAyCU,MAxCtBE,KAAIF,GAAJE,MACA4G,KAAI9G,GAAJ8G,MACA+F,KAAO7M,GAAP6M,SAAOC,IAAA9M,GACP4K,QAAAA,IAAAA,WAAMkC,IAAGnC,EAAgBC,SAAMkC,GAAAC,IAAA/M,GAC/BQ,UAAUwM,IAAAA,WAAeD,IAAGpC,EAAgBnK,WAAQuM,GAAAE,IAAAjN,GACpD6K,UAAAA,IAAAA,WAAQoC,IAAGtC,EAAgBE,WAAQoC,GACnCC,IAAWlN,GAAXkN,aAAWC,IAAAnN,GACX2B,OAAAA,IAAAA,WAAKwL,IAAGxC,EAAgBhJ,QAAKwL,GACrBC,IAAapN,GAArBqN,QACAC,IAAKtN,GAALsN,OACAC,IAAMvN,GAANuN,QAAM9D,IAAAzJ,GACNW,aAAAA,IAAAA,WAAW8I,IAAGkB,EAAgBhK,cAAW8I,GAAAC,KAAA1J,GACzCY,aAAAA,IAAAA,WAAW8I,KAAGiB,EAAgB/J,cAAW8I,IAAA8D,IAAAxN,GACzC8K,YAAAA,KAAAA,WAAU0C,IAAG7C,EAAgBG,aAAU0C,GAAAC,KAAAzN,GACvC+K,WAAAA,IAAAA,WAAS0C,KAAG9C,EAAgBI,YAAS0C,IAAAC,IAAA1N,GACrCiL,WAAAA,IAAAA,WAASyC,IAAG/C,EAAgBM,YAASyC,GAAAC,IAAA3N,GACrCgL,iBAAAA,KAAAA,WAAe2C,IAAGhD,EAAgBK,kBAAe2C,GAAAC,KAAA5N,GACjDwL,YAAAA,KAAAA,WAAUoC,KAAGjD,EAAgBa,aAAUoC,IACvCC,KAAS7N,GAAT6N,WAASC,KAAA9N,GACTyL,SAAAA,KAAAA,WAAOqC,KAAGnD,EAAgBc,UAAOqC,IAAAC,MAAA/N,GACjC0L,UAAAA,MAAAA,WAAQqC,MAAGpD,EAAgBe,WAAQqC,KAAAC,MAAAhO,GACnC2L,gBAAAA,MAAAA,WAAcqC,MAAGrD,EAAgBgB,iBAAcqC,KAAAC,MAAAjO,GAC/C4L,gBAAAA,KAAAA,WAAcqC,MAAGtD,EAAgBiB,iBAAcqC,KAAAC,KAAAlO,GAC/C6L,gBAAAA,KAAAA,WAAcqC,KAAGvD,EAAgBkB,iBAAcqC,IAAAC,KAAAnO,GAC/C8L,UAAAA,KAAAA,WAAQqC,KAAGxD,EAAgBmB,WAAQqC,IAAAC,KAAApO,GACnC+L,iBAAAA,KAAAA,WAAeqC,KAAGzD,EAAgBoB,kBAAeqC,IAAAC,KAAArO,GACjDgM,QAAAA,KAAAA,WAAMqC,KAAG1D,EAAgBqB,SAAMqC,IAAAC,KAAAtO,GAC/Ba,aAAAA,KAAAA,WAAWyN,KAAG3D,EAAgB9J,cAAWyN,IAAAC,KAAAvO,GACzCc,WAAAA,KAAAA,WAASyN,KAAG5D,EAAgB7J,YAASyN,IAAAC,KAAAxO,GACrCkM,eAAAA,KAAAA,WAAasC,KAAG7D,EAAgBuB,gBAAasC,IAAAC,KAAAzO,GAC7CmM,cAAAA,KAAAA,WAAYsC,KAAG9D,EAAgBwB,eAAYsC,IAAAC,KAAA1O,GAC3CwM,SAAAA,KAAAA,WAAOkC,KAAG/D,EAAgB6B,UAAOkC,IACjCjC,KAAIzM,GAAJyM,MACAkC,KAAS3O,GAAT2O,WACAC,KAAc5O,GAAd4O,gBACAC,KAAe7O,GAAf6O,iBAAeC,KAAA9O,GACf2M,MAAAA,KAAAA,WAAImC,KAAGnE,EAAgBgC,OAAImC,IAAAC,KAAA/O,GAC3BsC,MAAAA,KAAAA,WAAIyM,KAAGpE,EAAgBrI,OAAIyM,IAC3B3H,KAAOpH,GAAPoH,SACA4H,MAAYhP,GAAZgP,cAEAC,KAAqEC,GACjE5B,GACAC,GACAH,CAAAA,GAHIC,KAAM4B,GAAN5B,QAAQ8B,KAAUF,GAAVE,YAAYC,KAAWH,GAAXG,aAAaC,KAAUJ,GAAVI,YAAYC,MAAWL,GAAXK,aAMrDC,KC7CoB,SAAHvP,IAAAA;AA4Bf,QA3BFE,KAAIF,GAAJE,MACA4G,KAAI9G,GAAJ8G,MACA+F,KAAO7M,GAAP6M,SACAG,KAAehN,GAAfgN,iBACAnC,KAAQ7K,GAAR6K,UACAqC,KAAWlN,GAAXkN,aACAvL,KAAK3B,GAAL2B,OACA2L,KAAKtN,GAALsN,OACAC,KAAMvN,GAANuN,QAAMc,KAAArO,GACNgM,QAAAA,KAAAA,WAAMqC,KAAG1D,EAAgBqB,SAAMqC,IAC/B7B,IAAOxM,GAAPwM,SACAG,KAAI3M,GAAJ2M,MACArK,KAAItC,GAAJsC,MAgBMwG,IAAWqB,GAA+B0C,EAAAA,GAC1C3J,SAAU9B,aAAAA,SAAQ,WAAA;AAAA,aAAMlB,GAAKiC,IAAI2G,CAAAA;IAAS,GAAE,CAAC5I,IAAM4I,CAAAA,CAAAA,GACnD/B,KAAcyI,GAAkCtC,EAAAA,GAChD1M,KAAWiP,GAAiBzC,EAAAA,GAE5B0C,KAAWC,GAAqD3D,IAAQ,KAAA,GACxE3L,SAAgCe,aAAAA,SAClC,WAAA;AAAA,aACI0F,GAAKsD,OAA0B,SAACwF,IAASzP,IAAKoD,IAAAA;AAE1C,eADAqM,GAAQzP,EAAAA,IAAOuP,GAAS,EAAEvP,KAAAA,IAAKoD,OAAAA,GAAAA,CAAAA,GACxBqM;MACV,GAAE,CAAE,CAAA;IAAC,GACV,CAAC9I,IAAM4I,EAAAA,CAAAA,GAGXvJ,SAAiC/E,aAAAA,SAAQ,WAAA;AAErC,UAAMyO,KAAU/I,GAAK3E,IAAI,SAAA2N,IAAAA;AAAC,eAAK,EAAE3P,KAAK2P,IAAGtN,OAAOnC,GAAWyP,EAAAA,GAAI5P,MAAAA,IAAMoC,MAAM,KAAA;MAAM,CAAA,GAC3EyN,KAAYC,GAASrD,IAAMkD,IAASvN,EAAAA,GACpChC,KAAYuP,GAAQzF,OAAsC,SAACwF,IAASK,IAAAA;AACtE,YAAaC,KAAkBD,GAAvB9P,KAAcmC,KAAS2N,GAAT3N;AAEtB,eADAsN,GAAQM,EAAAA,IAAW5N,IACZsN;MACV,GAAE,CAAE,CAAA;AAEL,aAAO,EAAEG,WAAAA,IAAWzP,WAAAA,GAAAA;IACxB,GAAG,CAACwG,IAAM5G,IAAMyM,IAAMrK,IAAMjC,EAAAA,CAAAA,GAXpB0P,KAAS5J,GAAT4J,WAAWzP,KAAS6F,GAAT7F,WAanB6P,SAA6D/O,aAAAA,SAAQ,WAAA;AACjE,UAAMgP,KAAsBlQ,GAAKkK,OAC7B,SAACC,IAAe9I,IAAAA;AAAC,eAAA8O,CAAAA,EAAAA,OAAShG,IAAQvD,GAAK3E,IAAI,SAAAhC,IAAAA;AAAG,iBAAIoB,GAAEpB,EAAAA;QAAc,CAAA,CAAA;MAAE,GACpE,CAAA,CAAA,GAEEmQ,KAAgC,WAAbzF,KAAsBA,KAAW9F,KAAKC,IAAGuL,MAARxL,MAAYqL,EAAAA,GAEhE9O,KAASyD,KAAKyL,IAAIlD,IAAOC,EAAAA,IAAU;AAKzC,aAAO,EACHjM,QAAAA,IACAf,aANgBkQ,OAAAA,EACfC,MAAM,CAAC,GAAGpP,EAAAA,CAAAA,EACVqP,OAAO,CAAC,GAAGL,EAAAA,CAAAA,GAKZM,SAAStD,KAAQ,GACjBuD,SAAStD,KAAS,GAClB9M,WAAsB,IAAVsE,KAAKuB,KAAUpG,GAAKgE,OAAAA;IAExC,GAAG,CAAC4C,IAAM5G,IAAM2K,IAAUyC,IAAOC,EAAAA,CAAAA,GAnBzBjM,KAAM6O,GAAN7O,QAAQf,KAAW4P,GAAX5P,aAAaqQ,KAAOT,GAAPS,SAASC,KAAOV,GAAPU,SAASpQ,KAAS0P,GAAT1P,WAqBzCC,KAAeoQ,GAAsBnP,EAAAA,GAErCoP,SAA6C3P,aAAAA,SAC/C,WAAA;AAAA,aAAO,EACHlB,MAAAA,IACA4G,MAAAA,IACA5D,SAAAA,IACA7C,YAAAA,IACAuQ,SAAAA,IACAC,SAAAA,IACAtQ,aAAAA,IACAE,WAAAA,GAAAA;IACH,GACD,CAACP,IAAM4G,IAAM5D,IAAS7C,IAAYuQ,IAASC,IAAStQ,IAAaE,EAAAA,CAAAA,GAG/DuQ,SAAa5P,aAAAA,SACf,WAAA;AAAA,aAAM0F,GAAK3E,IAAI,SAAAhC,IAAAA;AAAG,eAAK,EAAE2D,IAAI3D,IAAKiD,OAAOjD,IAAKqC,OAAOnC,GAAWF,EAAAA,EAAAA;MAAM,CAAA;IAAE,GACxE,CAAC2G,IAAMzG,EAAAA,CAAAA,GAGL4Q,SAAmC7P,aAAAA,SACrC,WAAA;AAAA,aACIoL,EAAQrK,IAAI,SAAAiD,IAAAA;AAAqC,YAA5B8L,KAAU9L,GAAhBlF,MAAqBiR,KAAMpL,EAAAX,IAAAY,EAAAA,GAChCoL,KAAYF,QAAAA,KAAAA,SAAAA,GAAY/O,IAAI,SAAAkP,IAAAA;AAE9B,iBAAAxN,EAAA,CAAA,GADiBmN,GAAWM,KAAK,SAAAC,IAAAA;AAAE,mBAAIA,GAAGzN,OAAOuN,GAAGvN;UAAG,CAAA,KAAI,CAAA,GAClCuN,EAAAA;QAC7B,CAAA;AACA,eAAAxN,EAAAA,CAAAA,GAAYsN,IAAM,EAAEjR,MAAMkR,MAAaJ,GAAAA,CAAAA;MAC3C,CAAA;IAAE,GACN,CAACxE,GAASwE,EAAAA,CAAAA;AAGd,WAAO,EACHlI,UAAAA,GACA5F,SAAAA,IACA6D,aAAAA,IACA1G,YAAAA,IACAC,WAAAA,IACAyP,WAAAA,IACAvP,UAAAA,IACAc,QAAAA,IACAf,aAAAA,IACAqQ,SAAAA,IACAC,SAAAA,IACApQ,WAAAA,IACAC,cAAAA,IACAsQ,YAAAA,IACAC,cAAAA,IACAF,kBAAAA,GAAAA;EAER,EDpEoB,EACZ7Q,MAAAA,IACA4G,MAAAA,IACA+F,SAAAA,IACAG,iBAAAA,GACAnC,UAAAA,GACAqC,aAAAA,GACAvL,OAAAA,GACA2L,OAAO6B,IACP5B,QAAQ6B,IACRpD,QAAAA,IACAQ,SAAAA,IACAG,MAAAA,IACArK,MAAAA,GAAAA,CAAAA,GA5BAwG,KAAQyG,GAARzG,UACA5F,KAAOqM,GAAPrM,SACA6D,KAAWwI,GAAXxI,aACA1G,KAAUkP,GAAVlP,YACAC,KAASiP,GAATjP,WACAyP,KAASR,GAATQ,WACAvP,KAAQ+O,GAAR/O,UACAc,KAAMiO,GAANjO,QACAf,KAAWgP,GAAXhP,aACAqQ,KAAOrB,GAAPqB,SACAC,KAAOtB,GAAPsB,SACApQ,KAAS8O,GAAT9O,WACAC,KAAY6O,GAAZ7O,cACAuQ,KAAY1B,GAAZ0B,cACAF,KAAgBxB,GAAhBwB,kBAiBES,KAA6C,EAC/CvM,MAAM,MACN2F,QAAQ,MACR6G,QAAQ,MACRC,MAAM,MACNlF,SAAS,KAAA;AAuGb,SApGI5B,EAAO+G,SAAS,MAAA,MAChBH,GAAUvM,WACNvC,mBAAAA,KAAA,KAAA,EAAcyB,WAAwByM,eAAAA,KAAYC,OAAAA,KAAW,KAAAtM,cACzD7B,mBAAAA,KAACuD,GAAS,EACNC,QAAQ4E,IACRjF,OAAOkF,GACPzJ,QAAQA,IACRd,UAAUA,IACVC,WAAWA,IACXyC,SAASA,IACTE,OAAO6H,GACP5H,aAAa2H,GAAAA,CAAAA,EAAAA,GATd,MAAA,IAeXJ,EAAO+G,SAAS,QAAA,MAChBH,GAAU5G,aACNlI,mBAAAA,KAAA,KAAA,EAAgByB,WAAwByM,eAAAA,KAAYC,OAAAA,KAAW,KAAAtM,UAC1DuC,GAAK3E,IAAI,SAAAhC,IAAAA;AAAG,eACTuC,mBAAAA,KAAC3C,GAAU,EAEPG,MAAMA,IACNE,MAAMD,IACNE,YAAYA,IACZC,WAAWA,IACXC,aAAaA,IACbC,UAAUA,IACVC,WAAWA,IACXC,cAAcA,IACdC,aAAaA,GACbC,aAAaA,GACbC,aAAaA,IACbC,WAAWA,GAAAA,GAZNX,EAAAA;EAAAA,CAAAA,EAAAA,GAHV,QAAA,IAsBXyK,EAAO+G,SAAS,QAAA,KAAazF,OAC7BsF,GAAUC,aACN/O,mBAAAA,KAAA,KAAA,EAAgByB,WAAwByM,eAAAA,KAAYC,OAAAA,KAAW,KAAAtM,cAC3D7B,mBAAAA,KAACmG,GAAW,EACR3I,MAAMA,IACN4G,MAAMA,IACNgC,UAAUA,IACV/B,aAAaA,IACb1G,YAAYA,IACZiB,QAAQA,IACRd,UAAUA,IACVC,WAAWA,IACX0G,SAASgF,IACT/E,SAASA,GAAAA,CAAAA,EAAAA,GAXV,QAAA,IAiBXwD,EAAO+G,SAAS,MAAA,KAAWnG,OAC3BgG,GAAUE,WACNhP,mBAAAA,KAAA,KAAA,EAAcyB,WAAwByM,eAAAA,KAAYC,OAAAA,KAAW,KAAAtM,cACzD7B,mBAAAA,KAAC0G,GAAS,EACNlJ,MAAMA,IACN4G,MAAMA,IACNgC,UAAUA,IACVvI,aAAaA,IACbC,UAAUA,IACVC,WAAWA,IACX4I,QAAQwE,IACRtE,MAAMkC,IACNpL,YAAYA,IACZmC,OAAOkJ,KACP/K,aAAagL,KACb/K,aAAagL,IACbhC,aAAaiC,IACbzI,OAAO0I,IACP/E,aAAaA,IACb+C,cAAciC,GAAAA,CAAAA,EAAAA,GAjBf,MAAA,IAuBXnB,EAAO+G,SAAS,SAAA,MAChBH,GAAUhF,cACN9J,mBAAAA,KAACkP,aAAAA,UAAQ,EAAArN,UACJ0M,GAAa9O,IAAI,SAACgP,IAAQzP,IAAAA;AAAC,eACxBgB,mBAAAA,KAACmP,IAAYhO,EAAAA,CAAAA,GAELsN,IAAM,EACVW,gBAAgBxE,GAChByE,iBAAiBxE,EAAAA,CAAAA,GAHZ7L,EAAAA;EAAAA,CAAAA,EAAAA,GAHH,SAAA,QAclBgB,mBAAAA,KAACsP,IAAU,EACPrF,MAAMoD,IACNzC,OAAO+B,IACP9B,QAAQ+B,KACRjC,QAAQA,IACRZ,MAAMA,IACNkC,WAAWA,IACXC,gBAAgBA,IAChBC,iBAAiBA,IACjBoD,KAAKjD,KAAazK,UAEjBqG,EAAOzI,IAAI,SAAC+P,IAAOxQ,IAAAA;AAAM,QAAAyQ;AACtB,WAAqB,cAAA,OAAVD,SACAxP,mBAAAA,KAACkP,aAAAA,UAAQ,EAAArN,cAAUC,aAAAA,eAAc0N,IAAOnB,EAAAA,EAAAA,GAAzBrP,EAAAA,IAGD,SAAzByQ,KAAOX,QAAAA,KAAAA,SAAAA,GAAYU,EAAAA,KAAMC,KAAI;EAAA,CAAA,EAAA,CAAA;AAI7C;AV1MO,IU4MMC,SAAQC,aAAAA,YACjB,SAAAjN,IASI6M,IAAAA;AAAuB,MAAAK,KAAAlN,GAPnB8G,eAAAA,KAAAA,WAAaoG,KAAG3H,EAAgBuB,gBAAaoG,IAAAC,KAAAnN,GAC7CtD,SAAAA,KAAAA,WAAOyQ,KAAG5H,EAAgB7I,UAAOyQ,IAAAC,KAAApN,GACjCsH,cAAAA,IAAAA,WAAY8F,KAAG7H,EAAgB+B,eAAY8F,IAC3CzR,IAAKqE,GAALrE,OACA0R,IAAarN,GAAbqN,eACG3M,IAAKC,EAAAX,IAAAY,EAAAA;AAAA,aAIZtD,mBAAAA,KAACgQ,IAAS,EACN5Q,SAASA,IACToK,eAAeA,IACfQ,cAAcA,GACd+F,eAAeA,GACf1R,OAAOA,GAAMwD,cAEb7B,mBAAAA,KAACkK,IAAU/I,EAAA,EAAIqI,eAAeA,GAAAA,GAAmBpG,GAAK,EAAEkJ,cAAciD,GAAAA,CAAAA,CAAAA,EAAAA,CAAAA;AAC9D,CAAA;AVhOb,IUgOa,KAAA,CAAA,gBAAA,iBAAA,YAAA,gBAAA;AVhOb,IYpBMU,SAAkBN,aAAAA,YAC3B,SAAArS,IAQIiS,IAAAA;AAAuB,MANnBW,KAAY5S,GAAZ4S,cACAC,KAAa7S,GAAb6S,eACAC,KAAQ9S,GAAR8S,UACAC,KAAc/S,GAAd+S,gBACGjN,KAAKC,EAAA/F,IAAAgG,EAAAA;AAAA,aAIZtD,mBAAAA,KAACsQ,IAAiB,EACdJ,cAAcA,IACdC,eAAeA,IACfC,UAAUA,IACVC,gBAAgBA,IAAexO,UAE9B,SAAAa,IAAAA;AAAA,QAAGkI,KAAKlI,GAALkI,OAAOC,KAAMnI,GAANmI;AAAM,eAAO7K,mBAAAA,KAAC0P,IAAKvO,EAAAA,CAAAA,GAAQiC,IAAK,EAAEwH,OAAOA,IAAOC,QAAQA,IAAQ0E,KAAKA,GAAAA,CAAAA,CAAAA;EAAO,EAAA,CAAA;AACvE,CAAA;", "names": ["n", "i", "z", "z2", "t", "RadarLayer", "_ref", "_fillByKey$key", "data", "key", "item", "colorByKey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "radiusScale", "rotation", "angleStep", "curveFactory", "borderWidth", "borderColor", "fillOpacity", "blendMode", "theme", "useTheme", "getBorderColor", "useInheritedColor", "lineGenerator", "useMemo", "lineRadial", "radius", "d", "angle", "_", "i", "curve", "_useMotionConfig", "useMotionConfig", "animate", "springConfig", "config", "animatedPath", "useAnimatedPath", "map", "animatedProps", "useSpring", "fill", "stroke", "color", "immediate", "_jsx", "animated", "path", "strokeWidth", "style", "mixBlendMode", "RadarGridLabels", "angles", "indices", "labelComponent", "label", "labelOffset", "labels", "index", "_angle", "position", "positionFromAngle", "textAnchor", "radiansToDegrees", "_extends", "id", "anchor", "springs", "useSprings", "length", "transform", "x", "y", "_Fragment", "children", "createElement", "RadarGridLevelCircular", "memo", "circle", "r", "to", "value", "Math", "max", "grid", "line", "RadarGridLevelLinear", "_ref2", "dataLength", "radarLineGenerator", "curveLinearClosed", "points", "Array", "from", "RadarGridLevels", "_ref3", "shape", "props", "_objectWithoutPropertiesLoose", "_excluded", "RadarGrid", "levels", "_useMemo", "radii", "reverse", "PI", "_jsxs", "x1", "y1", "x2", "y2", "RadarSlice", "datum", "keys", "formatValue", "startAngle", "endAngle", "arcGenerator", "tooltip", "onClick", "_useState", "useState", "isHover", "setIsHover", "_useTooltip", "useTooltip", "showTooltipFromEvent", "hideTooltip", "handleClick", "useCallback", "event", "tooltipData", "formattedValue", "sort", "a", "b", "showItemTooltip", "hideItemTooltip", "tipX", "tipY", "crosshair", "onMouseEnter", "onMouseMove", "onMouseLeave", "RadarSlices", "getIndex", "arc", "d3Arc", "outerRadius", "innerRadius", "rootStartAngle", "RadarDots", "symbol", "_ref$size", "size", "_ref$color", "_ref$borderWidth", "_ref$borderColor", "_ref$enableLabel", "enableLabel", "_ref$label", "labelYOffset", "fillColor", "getInheritedColorGenerator", "strokeColor", "get<PERSON><PERSON><PERSON>", "usePropertyAccessor", "reduce", "acc", "for<PERSON>ach", "pointData", "push", "point", "DotsItem", "svgDefaultProps", "layers", "maxValue", "gridLevels", "gridShape", "gridLabelOffset", "gridLabel", "g", "Text", "axis", "ticks", "text", "dominantBaseline", "enableDots", "dotSize", "dotColor", "dotBorderWidth", "dotBorderColor", "enableDotLabel", "dotLabel", "dotLabelYOffset", "colors", "scheme", "isInteractive", "sliceTooltip", "rows", "Chip", "TableTooltip", "title", "legends", "role", "motionConfig", "defs", "InnerRadar", "indexBy", "_ref$layers", "_ref$rotation", "rotationDegrees", "_ref$maxValue", "valueFormat", "_ref$curve", "<PERSON><PERSON><PERSON><PERSON>", "margin", "width", "height", "_ref$gridLevels", "_ref$gridShape", "_ref$gridLabel", "_ref$gridLabelOffset", "_ref$enableDots", "dotSymbol", "_ref$dotSize", "_ref$dotColor", "_ref$dotBorderWidth", "_ref$dotBorderColor", "_ref$enableDotLabel", "_ref$dotLabel", "_ref$dotLabelYOffset", "_ref$colors", "_ref$fillOpacity", "_ref$blendMode", "_ref$isInteractive", "_ref$sliceTooltip", "_ref$legends", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaDescribedBy", "_ref$defs", "_ref$fill", "forwardedRef", "_useDimensions", "useDimensions", "innerWidth", "innerHeight", "outerWidth", "outerHeight", "_useRadar", "useValueFormatter", "degreesToRadians", "getColor", "useOrdinalColorScale", "mapping", "keyData", "k", "boundDefs", "bindDefs", "keyDatum", "keyName", "_useMemo2", "allValues", "concat", "computedMaxValue", "apply", "min", "scaleLinear", "range", "domain", "centerX", "centerY", "useCurveInterpolation", "customLayerProps", "legendData", "boundLegends", "customData", "legend", "boundData", "cd", "find", "ld", "layerById", "slices", "dots", "includes", "Fragment", "BoxLegendSvg", "containerWidth", "containerHeight", "SvgWrapper", "ref", "layer", "_layerById$layer", "Radar", "forwardRef", "_ref2$isInteractive", "_ref2$animate", "_ref2$motionConfig", "renderWrapper", "Container", "ResponsiveRadar", "defaultWidth", "defaultHeight", "onResize", "debounceResize", "ResponsiveWrapper"]}