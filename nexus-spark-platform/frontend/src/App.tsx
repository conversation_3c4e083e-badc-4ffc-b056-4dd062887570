import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import Landing from "./pages/Landing";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import EnhancedDashboard from "./pages/EnhancedDashboard";
import Analytics from "./pages/Analytics";
import AnalyticsDashboard from "./pages/AnalyticsDashboard";
import ModernAnalytics from "./pages/ModernAnalytics";
import TestSelection from "./pages/TestSelection";
import ModernTestSelection from "./pages/ModernTestSelection";
import TestInterface from "./pages/TestInterface";
import AssessmentInterface from "./pages/AssessmentInterface";
import ModernAssessment from "./pages/ModernAssessment";
import QuestionGeneration from "./pages/QuestionGeneration";
import TestResults from "./pages/TestResults";
import NotFound from "./pages/NotFound";
import LayoutTest from "./components/test/LayoutTest";
import SimpleLanding from "./pages/SimpleLanding";
import SimpleLandingFixed from "./pages/SimpleLandingFixed";
import SimpleLogin from "./pages/SimpleLogin";
import SimpleLoginFixed from "./pages/SimpleLoginFixed";
import SimpleDashboard from "./pages/SimpleDashboard";
import ModernDashboard from "./pages/ModernDashboard";
import TestPage from "./pages/TestPage";

const queryClient = new QueryClient();

// Force demo mode for this deployment
if (import.meta.env.VITE_DEMO_MODE === 'true') {
  localStorage.setItem('demo_mode', 'true');
  console.log('🎭 Demo mode enabled globally');
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50">
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<SimpleLandingFixed />} />
            <Route path="/landing-original" element={<SimpleLanding />} />
            <Route path="/test" element={<TestPage />} />
            <Route path="/original" element={<Landing />} />
            <Route path="/login" element={<SimpleLoginFixed />} />
            <Route path="/login-original" element={<SimpleLogin />} />
            <Route path="/login-legacy" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <ModernDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/simple"
              element={
                <ProtectedRoute>
                  <SimpleDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard-enhanced"
              element={
                <ProtectedRoute>
                  <EnhancedDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/legacy"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/analytics"
              element={
                <ProtectedRoute>
                  <ModernAnalytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/analytics/enhanced"
              element={
                <ProtectedRoute>
                  <AnalyticsDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/analytics/legacy"
              element={
                <ProtectedRoute>
                  <Analytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/tests"
              element={
                <ProtectedRoute>
                  <ModernTestSelection />
                </ProtectedRoute>
              }
            />
            <Route
              path="/tests/legacy"
              element={
                <ProtectedRoute>
                  <TestSelection />
                </ProtectedRoute>
              }
            />
            <Route
              path="/tests/:testId"
              element={
                <ProtectedRoute>
                  <TestInterface />
                </ProtectedRoute>
              }
            />
            <Route
              path="/assessment"
              element={
                <ProtectedRoute>
                  <ModernAssessment />
                </ProtectedRoute>
              }
            />
            <Route
              path="/assessment/:testId"
              element={
                <ProtectedRoute>
                  <ModernAssessment />
                </ProtectedRoute>
              }
            />
            <Route
              path="/assessment/legacy"
              element={
                <ProtectedRoute>
                  <AssessmentInterface />
                </ProtectedRoute>
              }
            />
            <Route
              path="/generate"
              element={
                <ProtectedRoute>
                  <QuestionGeneration />
                </ProtectedRoute>
              }
            />
            <Route
              path="/layout-test"
              element={<LayoutTest />}
            />
            <Route
              path="/test-results"
              element={
                <ProtectedRoute>
                  <TestResults />
                </ProtectedRoute>
              }
            />

            {/* 404 route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
      </div>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
