@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 221.2 83.2% 63.3%;
    --primary-dark: 221.2 83.2% 43.3%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-dark: 210 40% 86%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --destructive-light: 0 84.2% 70.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --success-light: 142.1 76.2% 46.3%;
    --warning: 47.9 95.8% 53.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --warning-light: 47.9 95.8% 63.1%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-strong: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-glow: 0 0 0 1px rgba(59, 130, 246, 0.5), 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --primary-light: 217.2 91.2% 69.8%;
    --primary-dark: 217.2 91.2% 49.8%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-dark: 217.2 32.6% 12.5%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --destructive-light: 0 62.8% 40.6%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 144.9 80.4% 10%;
    --success-light: 142.1 70.6% 55.3%;
    --warning: 47.9 95.8% 53.1%;
    --warning-foreground: 26 83.3% 14.1%;
    --warning-light: 47.9 95.8% 63.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 94.1%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

/* Enterprise-Level Animations and Effects */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 3D Card Effects */
.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.card-3d:hover {
  transform: rotateY(5deg) rotateX(5deg) translateZ(10px);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-border {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
  padding: 2px;
  border-radius: 12px;
}

.gradient-border-inner {
  background: white;
  border-radius: 10px;
  height: 100%;
  width: 100%;
}

/* Smooth Transitions */
.transition-all-300 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Modern Shadows */
.shadow-enterprise {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

/* Loading Animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Advanced Enterprise Effects */
@keyframes morphing {
  0%, 100% {
    border-radius: 40% 60% 70% 30% / 40% 40% 60% 50%;
  }
  34% {
    border-radius: 70% 30% 50% 50% / 30% 30% 70% 70%;
  }
  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.morphing-blob {
  animation: morphing 8s ease-in-out infinite;
}

.particle-float {
  animation: particle-float 4s ease-in-out infinite;
}

.gradient-shift {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* Interactive Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
  transform: scale(1.02);
}

/* Glassmorphism Effects */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Neon Effects */
.neon-blue {
  box-shadow:
    0 0 5px #3b82f6,
    0 0 10px #3b82f6,
    0 0 15px #3b82f6,
    0 0 20px #3b82f6;
}

.neon-purple {
  box-shadow:
    0 0 5px #8b5cf6,
    0 0 10px #8b5cf6,
    0 0 15px #8b5cf6,
    0 0 20px #8b5cf6;
}

/* Text Effects */
.text-gradient {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow-glow {
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Loading Animations */
@keyframes pulse-ring {
  0% {
    transform: scale(0.33);
  }
  40%, 50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.33);
  }
}

.pulse-ring {
  animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

/* Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #2563eb, #7c3aed);
}

@layer base {
  :root {
    /* Base Colors */
    --background: 250 100% 99%;
    --foreground: 222 47% 11%;

    /* Card System */
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    /* Interactive Elements */
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    /* Primary Brand Colors - Educational Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 217 91% 70%;
    --primary-dark: 217 91% 50%;

    /* Secondary Colors - Academic Purple */
    --secondary: 267 84% 81%;
    --secondary-foreground: 222 47% 11%;
    --secondary-dark: 267 84% 65%;

    /* Success & Progress Colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 71% 90%;

    /* Warning Colors */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 92% 95%;

    /* Error Colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --destructive-light: 0 84% 95%;

    /* Neutral System */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    /* Borders & Inputs */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 217 91% 60%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(267, 84%, 81%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142, 71%, 45%) 0%, hsl(142, 71%, 60%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(267, 84%, 65%) 50%, hsl(217, 91%, 70%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0, 0%, 100%) 0%, hsl(210, 40%, 98%) 100%);

    /* Shadows */
    --shadow-soft: 0 2px 10px hsl(217, 91%, 60%, 0.1);
    --shadow-medium: 0 4px 20px hsl(217, 91%, 60%, 0.15);
    --shadow-strong: 0 8px 30px hsl(217, 91%, 60%, 0.2);
    --shadow-glow: 0 0 20px hsl(217, 91%, 60%, 0.3);

    /* Layout */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins;
  }
}

@layer components {
  /* Button Variants */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
  }

  .btn-hero {
    @apply bg-gradient-to-r from-primary via-secondary to-primary-light text-primary-foreground px-8 py-4 rounded-lg font-semibold shadow-strong hover:shadow-glow transition-all duration-300 transform hover:scale-105;
  }

  .btn-success {
    @apply bg-gradient-to-r from-success to-success text-success-foreground shadow-md hover:shadow-lg;
  }

  /* Card Variants */
  .card-gradient {
    background: var(--gradient-card);
    @apply shadow-soft border border-border/50;
  }

  .card-feature {
    @apply bg-card border border-border rounded-lg shadow-medium hover:shadow-strong transition-all duration-300 transform hover:scale-[1.02];
  }

  .card-analytics {
    @apply bg-gradient-to-br from-card to-muted/30 border border-border rounded-xl shadow-medium backdrop-blur-sm;
  }

  /* Text Variants */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-hero {
    @apply text-5xl md:text-7xl font-bold text-gradient leading-tight;
  }

  /* Animation Classes */
  .animate-fade-in {
    @apply animate-in fade-in duration-700;
  }

  .animate-slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-700;
  }

  .animate-scale-in {
    @apply animate-in zoom-in-95 duration-500;
  }

  /* Glass Effect */
  .glass {
    @apply bg-card/80 backdrop-blur-md border border-border/20 shadow-medium;
  }

  /* Interactive Elements */
  .hover-lift {
    @apply transition-all duration-300 hover:transform hover:scale-[1.02] hover:shadow-medium;
  }

  /* Chart Container */
  .chart-container {
    @apply bg-gradient-to-br from-card to-primary/5 rounded-xl p-6 shadow-soft border border-border/50;
  }
}