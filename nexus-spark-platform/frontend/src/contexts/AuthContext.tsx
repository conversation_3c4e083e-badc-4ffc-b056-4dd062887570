import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { nexusAPI } from '@/lib/api/nexusClient';
import { useToast } from '@/hooks/use-toast';

interface User {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'student' | 'teacher' | 'admin' | 'parent' | 'content_creator' | 'analyst';
  status: 'active' | 'inactive' | 'suspended' | 'pending_verification';
  grade_level?: number;
  school_id?: string;
  school_name?: string;
  preferences: Record<string, any>;
  notification_settings: Record<string, boolean>;
  last_login?: string;
  login_count: number;
  created_at: string;
  updated_at: string;
  topics_completed: number;
  assessments_taken: number;
  average_score?: number;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateProfile: (profileData: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string, confirmPassword: string) => Promise<void>;
}

interface RegisterData {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
  grade_level?: number;
  school_id?: string;
  parent_email?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const isAuthenticated = !!user;

  // Initialize auth state on app load
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const refreshToken = localStorage.getItem('refresh_token');
      
      if (token) {
        // Try to get current user with existing token
        try {
          const response = await nexusAPI.getCurrentUser();
          if (response.success && response.data) {
            setUser(response.data);
          } else {
            throw new Error('Invalid token');
          }
        } catch (error) {
          // Token might be expired, try to refresh
          if (refreshToken) {
            try {
              await refreshAuth();
            } catch (refreshError) {
              // Refresh failed, clear tokens
              clearAuthData();
            }
          } else {
            clearAuthData();
          }
        }
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      clearAuthData();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string, rememberMe: boolean = false) => {
    try {
      setIsLoading(true);

      // Force demo mode for demo credentials
      const isDemoLogin = email === '<EMAIL>' && password === 'demo123';
      if (isDemoLogin) {
        localStorage.setItem('demo_mode', 'true');
        console.log('🎭 Demo login detected, forcing demo mode');
      }

      const response = await nexusAPI.login(email, password, rememberMe);
      
      if (response.success && response.data) {
        const { access_token, refresh_token, user: userData } = response.data;
        
        // Store tokens
        nexusAPI.setAuthToken(access_token);
        localStorage.setItem('refresh_token', refresh_token);
        localStorage.setItem('user_profile', JSON.stringify(userData));
        
        setUser(userData);
        
        toast({
          title: "Welcome back!",
          description: `Hello ${userData.first_name}, you've successfully logged in.`,
        });
      } else {
        throw new Error(response.error || 'Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast({
        title: "Login failed",
        description: error.message || "Please check your credentials and try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true);
      const response = await nexusAPI.register(userData);
      
      if (response.success && response.data) {
        toast({
          title: "Registration successful!",
          description: "Please check your email to verify your account.",
        });
      } else {
        throw new Error(response.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      toast({
        title: "Registration failed",
        description: error.message || "Please check your information and try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await nexusAPI.logout();
      clearAuthData();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
    } catch (error) {
      console.error('Logout error:', error);
      // Clear data anyway
      clearAuthData();
    }
  };

  const refreshAuth = async () => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await nexusAPI.refreshToken(refreshToken);
      
      if (response.success && response.data) {
        const { access_token, refresh_token: newRefreshToken, user: userData } = response.data;
        
        // Update tokens
        nexusAPI.setAuthToken(access_token);
        localStorage.setItem('refresh_token', newRefreshToken);
        localStorage.setItem('user_profile', JSON.stringify(userData));
        
        setUser(userData);
      } else {
        throw new Error(response.error || 'Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      clearAuthData();
      throw error;
    }
  };

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      setIsLoading(true);
      const response = await nexusAPI.updateCurrentUser(profileData);
      
      if (response.success && response.data) {
        setUser(response.data);
        localStorage.setItem('user_profile', JSON.stringify(response.data));
        
        toast({
          title: "Profile updated",
          description: "Your profile has been successfully updated.",
        });
      } else {
        throw new Error(response.error || 'Profile update failed');
      }
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast({
        title: "Update failed",
        description: error.message || "Failed to update profile.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string, confirmPassword: string) => {
    try {
      setIsLoading(true);
      const response = await nexusAPI.changePassword(currentPassword, newPassword, confirmPassword);
      
      if (response.success) {
        toast({
          title: "Password changed",
          description: "Your password has been successfully updated.",
        });
      } else {
        throw new Error(response.error || 'Password change failed');
      }
    } catch (error: any) {
      console.error('Password change error:', error);
      toast({
        title: "Password change failed",
        description: error.message || "Failed to change password.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const clearAuthData = () => {
    nexusAPI.clearAuthToken();
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_profile');
    setUser(null);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshAuth,
    updateProfile,
    changePassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
