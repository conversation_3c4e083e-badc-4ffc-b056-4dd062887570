import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Navbar } from "@/components/layout/Navbar";
import { useAuth } from "@/contexts/AuthContext";
import { 
  useDashboardData, 
  useUserPerformance, 
  useRealTimeMetrics,
  useRefreshDashboard 
} from "@/hooks/useDashboard";
import StatsCards from "@/components/dashboard/StatsCards";
import PerformanceCharts from "@/components/dashboard/PerformanceCharts";
import RecentActivity from "@/components/dashboard/RecentActivity";
import { 
  RefreshCw,
  Settings,
  Download,
  Filter,
  Calendar,
  PlayCircle,
  Plus,
  BookOpen,
  Target,
  TrendingUp
} from "lucide-react";

export default function EnhancedDashboard() {
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  
  // Fetch dashboard data
  const { dashboardData, isDashboardLoading, dashboardError } = useDashboardData();
  const { userPerformance, isPerformanceLoading, performanceError } = useUserPerformance();
  const { metrics: realTimeMetrics, isLoading: isMetricsLoading, error: metricsError } = useRealTimeMetrics();
  const { refreshAll } = useRefreshDashboard();

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshAll();
    } finally {
      setRefreshing(false);
    }
  };

  const isLoadingData = isDashboardLoading || isPerformanceLoading || isMetricsLoading;
  const hasError = dashboardError || performanceError || metricsError;

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar
        isAuthenticated={true}
        user={user}
      />

      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0">
          <div className="flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
              Welcome back, {user.first_name}! 👋
            </h1>
            <p className="text-gray-600">
              Here's your learning progress and platform insights
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </Button>
            <Button variant="outline" size="sm" className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </Button>
          </div>
        </div>

        {/* Error State */}
        {hasError && (
          <Card className="mb-6 border-destructive">
            <CardContent className="pt-6">
              <p className="text-destructive">
                Failed to load dashboard data. Please try refreshing the page.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="mb-8">
          <StatsCards
            dashboardData={dashboardData}
            userPerformance={userPerformance}
            realTimeMetrics={realTimeMetrics}
            isLoading={isLoadingData}
          />
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto">
            <TabsTrigger value="overview" className="text-sm">Overview</TabsTrigger>
            <TabsTrigger value="performance" className="text-sm">Performance</TabsTrigger>
            <TabsTrigger value="activity" className="text-sm">Activity</TabsTrigger>
            <TabsTrigger value="insights" className="text-sm">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <PlayCircle className="h-5 w-5" />
                    <span>Quick Actions</span>
                  </CardTitle>
                  <CardDescription>
                    Start your learning session
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link to="/tests">
                    <Button className="w-full justify-start">
                      <BookOpen className="h-4 w-4 mr-2" />
                      Take Assessment
                    </Button>
                  </Link>
                  <Link to="/generate">
                    <Button variant="outline" className="w-full justify-start">
                      <Plus className="h-4 w-4 mr-2" />
                      Generate Questions
                    </Button>
                  </Link>
                  <Link to="/analytics">
                    <Button variant="outline" className="w-full justify-start">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View Analytics
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Learning Goals */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="h-5 w-5" />
                    <span>Learning Goals</span>
                  </CardTitle>
                  <CardDescription>
                    Your progress towards mastery
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Topics Mastered</span>
                        <span>{userPerformance?.topicsCompleted || 0}/{userPerformance?.totalTopics || 0}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300" 
                          style={{ width: `${userPerformance?.masteryPercentage || 0}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Average Score</span>
                        <span>{userPerformance?.averageScore ? Math.round(userPerformance.averageScore * 100) : 0}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                          style={{ width: `${userPerformance?.averageScore ? userPerformance.averageScore * 100 : 0}%` }}
                        ></div>
                      </div>
                    </div>

                    {userPerformance?.recommendedTopics && userPerformance.recommendedTopics.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium mb-2">Recommended Topics:</p>
                        <div className="flex flex-wrap gap-2">
                          {userPerformance.recommendedTopics.slice(0, 3).map((topic, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {topic}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <PerformanceCharts
              dashboardData={dashboardData}
              userPerformance={userPerformance}
              isLoading={isLoadingData}
            />
          </TabsContent>

          <TabsContent value="activity" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <RecentActivity
                activities={dashboardData?.recentActivity}
                isLoading={isLoadingData}
                showUserInfo={user?.role === 'admin' || user?.role === 'teacher'}
              />
              
              {/* System Status */}
              <Card>
                <CardHeader>
                  <CardTitle>System Status</CardTitle>
                  <CardDescription>
                    Real-time platform health
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Users</span>
                      <Badge variant="default">
                        {realTimeMetrics?.activeUsers || 0}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Assessments</span>
                      <Badge variant="secondary">
                        {realTimeMetrics?.activeAssessments || 0}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Questions Generated Today</span>
                      <Badge variant="outline">
                        {realTimeMetrics?.questionsGeneratedToday || 0}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Platform Insights */}
              <Card>
                <CardHeader>
                  <CardTitle>Platform Insights</CardTitle>
                  <CardDescription>
                    Key performance indicators
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm">Platform Performance</span>
                      <span className="font-medium">
                        {dashboardData?.platformPerformance?.averageScore || 0}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">User Satisfaction</span>
                      <span className="font-medium">
                        {dashboardData?.platformPerformance?.userSatisfaction || 0}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Content Quality</span>
                      <span className="font-medium">
                        {dashboardData?.platformPerformance?.contentQuality || 0}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Export Options */}
              <Card>
                <CardHeader>
                  <CardTitle>Export & Reports</CardTitle>
                  <CardDescription>
                    Download your data and reports
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="h-4 w-4 mr-2" />
                    Export Performance Data
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="h-4 w-4 mr-2" />
                    Generate Progress Report
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Filter className="h-4 w-4 mr-2" />
                    Custom Analytics
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
