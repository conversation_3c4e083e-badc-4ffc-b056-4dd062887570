import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Clock, 
  Users, 
  BookOpen, 
  Target, 
  Search, 
  Filter, 
  Star, 
  TrendingUp, 
  Brain,
  Zap,
  Award,
  Play,
  ChevronRight,
  Calendar,
  BarChart3,
  Sparkles,
  Globe,
  Layers
} from 'lucide-react';

interface Test {
  id: string;
  title: string;
  description: string;
  subject: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  duration: number;
  questions: number;
  participants: number;
  rating: number;
  tags: string[];
  isPopular?: boolean;
  isNew?: boolean;
  estimatedScore?: number;
}

const ModernTestSelection: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [loading, setLoading] = useState(true);

  const mockTests: Test[] = [
    {
      id: 'math-calc-001',
      title: 'Advanced Calculus Mastery',
      description: 'Comprehensive assessment covering derivatives, integrals, and applications of calculus in real-world scenarios.',
      subject: 'Mathematics',
      difficulty: 'Advanced',
      duration: 45,
      questions: 25,
      participants: 1247,
      rating: 4.8,
      tags: ['Calculus', 'Derivatives', 'Integrals', 'Applications'],
      isPopular: true,
      estimatedScore: 78
    },
    {
      id: 'cs-algo-001',
      title: 'Data Structures & Algorithms',
      description: 'Test your knowledge of fundamental computer science concepts including arrays, trees, graphs, and sorting algorithms.',
      subject: 'Computer Science',
      difficulty: 'Intermediate',
      duration: 60,
      questions: 30,
      participants: 2156,
      rating: 4.9,
      tags: ['Algorithms', 'Data Structures', 'Complexity', 'Problem Solving'],
      isPopular: true,
      estimatedScore: 85
    },
    {
      id: 'ai-ml-001',
      title: 'Machine Learning Fundamentals',
      description: 'Explore the basics of machine learning, neural networks, and artificial intelligence applications.',
      subject: 'AI/ML',
      difficulty: 'Beginner',
      duration: 30,
      questions: 20,
      participants: 892,
      rating: 4.6,
      tags: ['Machine Learning', 'Neural Networks', 'AI', 'Python'],
      isNew: true,
      estimatedScore: 92
    },
    {
      id: 'phys-quantum-001',
      title: 'Quantum Physics Principles',
      description: 'Advanced concepts in quantum mechanics, wave-particle duality, and quantum computing fundamentals.',
      subject: 'Physics',
      difficulty: 'Advanced',
      duration: 50,
      questions: 22,
      participants: 456,
      rating: 4.7,
      tags: ['Quantum Mechanics', 'Wave Functions', 'Quantum Computing'],
      estimatedScore: 72
    },
    {
      id: 'bio-genetics-001',
      title: 'Genetics and Molecular Biology',
      description: 'Comprehensive test on DNA, RNA, protein synthesis, and modern genetic engineering techniques.',
      subject: 'Biology',
      difficulty: 'Intermediate',
      duration: 40,
      questions: 28,
      participants: 734,
      rating: 4.5,
      tags: ['Genetics', 'DNA', 'Molecular Biology', 'CRISPR'],
      estimatedScore: 88
    },
    {
      id: 'chem-organic-001',
      title: 'Organic Chemistry Reactions',
      description: 'Master organic chemistry reactions, mechanisms, and synthesis pathways.',
      subject: 'Chemistry',
      difficulty: 'Advanced',
      duration: 55,
      questions: 26,
      participants: 623,
      rating: 4.4,
      tags: ['Organic Chemistry', 'Reactions', 'Mechanisms', 'Synthesis'],
      estimatedScore: 75
    }
  ];

  const [tests, setTests] = useState<Test[]>(mockTests);
  const [filteredTests, setFilteredTests] = useState<Test[]>(mockTests);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    let filtered = tests;

    if (searchTerm) {
      filtered = filtered.filter(test => 
        test.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        test.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        test.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (selectedSubject !== 'all') {
      filtered = filtered.filter(test => test.subject === selectedSubject);
    }

    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(test => test.difficulty === selectedDifficulty);
    }

    setFilteredTests(filtered);
  }, [searchTerm, selectedSubject, selectedDifficulty, tests]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800 border-green-200';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Advanced': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSubjectIcon = (subject: string) => {
    switch (subject) {
      case 'Mathematics': return <Target className="h-5 w-5" />;
      case 'Computer Science': return <Brain className="h-5 w-5" />;
      case 'AI/ML': return <Zap className="h-5 w-5" />;
      case 'Physics': return <Globe className="h-5 w-5" />;
      case 'Biology': return <Layers className="h-5 w-5" />;
      case 'Chemistry': return <BookOpen className="h-5 w-5" />;
      default: return <BookOpen className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Tests</h2>
          <p className="text-gray-500">Preparing your personalized assessments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8 animate-slide-up">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              Choose Your Assessment
            </h1>
            <p className="text-xl text-gray-600">
              Discover personalized tests tailored to your learning journey
            </p>
          </div>

          {/* Filters */}
          <Card className="shadow-enterprise mb-8 animate-slide-up" style={{ animationDelay: '0.1s' }}>
            <CardContent className="p-6">
              <div className="grid md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search tests..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Subjects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Subjects</SelectItem>
                    <SelectItem value="Mathematics">Mathematics</SelectItem>
                    <SelectItem value="Computer Science">Computer Science</SelectItem>
                    <SelectItem value="AI/ML">AI/ML</SelectItem>
                    <SelectItem value="Physics">Physics</SelectItem>
                    <SelectItem value="Biology">Biology</SelectItem>
                    <SelectItem value="Chemistry">Chemistry</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="Beginner">Beginner</SelectItem>
                    <SelectItem value="Intermediate">Intermediate</SelectItem>
                    <SelectItem value="Advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" className="hover-scale">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card className="card-3d hover-scale shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-2 animate-float">
                  <BookOpen className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-900">{filteredTests.length}</div>
                <p className="text-sm text-gray-600">Available Tests</p>
              </CardContent>
            </Card>

            <Card className="card-3d hover-scale shadow-enterprise animate-slide-up" style={{ animationDelay: '0.3s' }}>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-2 animate-float" style={{ animationDelay: '1s' }}>
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {filteredTests.reduce((sum, test) => sum + test.participants, 0).toLocaleString()}
                </div>
                <p className="text-sm text-gray-600">Total Participants</p>
              </CardContent>
            </Card>

            <Card className="card-3d hover-scale shadow-enterprise animate-slide-up" style={{ animationDelay: '0.4s' }}>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-2 animate-float" style={{ animationDelay: '2s' }}>
                  <Star className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {(filteredTests.reduce((sum, test) => sum + test.rating, 0) / filteredTests.length).toFixed(1)}
                </div>
                <p className="text-sm text-gray-600">Average Rating</p>
              </CardContent>
            </Card>

            <Card className="card-3d hover-scale shadow-enterprise animate-slide-up" style={{ animationDelay: '0.5s' }}>
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-2 animate-float" style={{ animationDelay: '3s' }}>
                  <BarChart3 className="h-6 w-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {Math.round(filteredTests.reduce((sum, test) => sum + (test.estimatedScore || 0), 0) / filteredTests.length)}%
                </div>
                <p className="text-sm text-gray-600">Avg. Expected Score</p>
              </CardContent>
            </Card>
          </div>

          {/* Test Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTests.map((test, index) => (
              <Card 
                key={test.id} 
                className="card-3d hover-scale shadow-enterprise transition-all-300 animate-slide-up group cursor-pointer"
                style={{ animationDelay: `${0.1 * index}s` }}
                onClick={() => navigate(`/assessment/${test.id}`)}
              >
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white">
                        {getSubjectIcon(test.subject)}
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {test.subject}
                      </Badge>
                    </div>
                    <div className="flex space-x-1">
                      {test.isPopular && (
                        <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                      {test.isNew && (
                        <Badge className="bg-gradient-to-r from-green-500 to-blue-500 text-white text-xs">
                          <Sparkles className="h-3 w-3 mr-1" />
                          New
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <CardTitle className="text-lg leading-tight group-hover:text-blue-600 transition-colors">
                    {test.title}
                  </CardTitle>
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {test.description}
                  </p>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="space-y-3">
                    {/* Test Stats */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-4 w-4 mr-1" />
                        {test.duration} min
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Target className="h-4 w-4 mr-1" />
                        {test.questions} questions
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Users className="h-4 w-4 mr-1" />
                        {test.participants.toLocaleString()}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Star className="h-4 w-4 mr-1 fill-current text-yellow-500" />
                        {test.rating}
                      </div>
                    </div>

                    {/* Difficulty Badge */}
                    <div className="flex justify-between items-center">
                      <Badge className={`${getDifficultyColor(test.difficulty)} border`}>
                        {test.difficulty}
                      </Badge>
                      {test.estimatedScore && (
                        <div className="text-sm text-gray-600">
                          Expected: <span className="font-semibold text-blue-600">{test.estimatedScore}%</span>
                        </div>
                      )}
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1">
                      {test.tags.slice(0, 3).map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {test.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{test.tags.length - 3} more
                        </Badge>
                      )}
                    </div>

                    {/* Action Button */}
                    <Button 
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 group-hover:shadow-glow-blue transition-all-300"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/assessment/${test.id}`);
                      }}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Test
                      <ChevronRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTests.length === 0 && (
            <div className="text-center py-12 animate-slide-up">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No tests found</h3>
              <p className="text-gray-500 mb-4">Try adjusting your search criteria or filters</p>
              <Button 
                onClick={() => {
                  setSearchTerm('');
                  setSelectedSubject('all');
                  setSelectedDifficulty('all');
                }}
                variant="outline"
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernTestSelection;
