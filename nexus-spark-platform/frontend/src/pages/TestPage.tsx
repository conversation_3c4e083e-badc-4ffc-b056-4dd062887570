import React from 'react';

const TestPage: React.FC = () => {
  return (
    <div style={{ 
      padding: '2rem', 
      textAlign: 'center',
      minHeight: '100vh',
      backgroundColor: '#f3f4f6',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <h1 style={{ 
        fontSize: '3rem', 
        color: '#1f2937',
        marginBottom: '1rem'
      }}>
        🎉 Test Page Working!
      </h1>
      <p style={{ 
        fontSize: '1.2rem', 
        color: '#6b7280',
        marginBottom: '2rem'
      }}>
        If you can see this, the routing is working correctly.
      </p>
      <div style={{
        display: 'flex',
        gap: '1rem',
        flexWrap: 'wrap',
        justifyContent: 'center'
      }}>
        <a 
          href="/login" 
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#3b82f6',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '0.5rem',
            fontWeight: '500'
          }}
        >
          Go to Login
        </a>
        <a 
          href="/dashboard" 
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#10b981',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '0.5rem',
            fontWeight: '500'
          }}
        >
          Go to Dashboard
        </a>
        <a 
          href="/tests" 
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#8b5cf6',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '0.5rem',
            fontWeight: '500'
          }}
        >
          Browse Tests
        </a>
        <a 
          href="/analytics" 
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: '#f59e0b',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '0.5rem',
            fontWeight: '500'
          }}
        >
          View Analytics
        </a>
      </div>
      
      <div style={{
        marginTop: '3rem',
        padding: '1.5rem',
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        maxWidth: '600px'
      }}>
        <h2 style={{ 
          fontSize: '1.5rem', 
          color: '#1f2937',
          marginBottom: '1rem'
        }}>
          🚀 Application Status
        </h2>
        <div style={{ textAlign: 'left' }}>
          <p style={{ margin: '0.5rem 0', color: '#374151' }}>
            ✅ <strong>Frontend:</strong> Running on http://localhost:5173
          </p>
          <p style={{ margin: '0.5rem 0', color: '#374151' }}>
            ✅ <strong>API Gateway:</strong> Running on http://localhost:8080
          </p>
          <p style={{ margin: '0.5rem 0', color: '#374151' }}>
            ✅ <strong>Database:</strong> PostgreSQL in Docker
          </p>
          <p style={{ margin: '0.5rem 0', color: '#374151' }}>
            ✅ <strong>Cache:</strong> Redis in Docker
          </p>
          <p style={{ margin: '0.5rem 0', color: '#374151' }}>
            ✅ <strong>Modern UI:</strong> Enterprise-level design with animations
          </p>
        </div>
      </div>

      <div style={{
        marginTop: '2rem',
        padding: '1rem',
        backgroundColor: '#fef3c7',
        border: '1px solid #f59e0b',
        borderRadius: '0.5rem',
        maxWidth: '600px'
      }}>
        <p style={{ 
          margin: 0, 
          color: '#92400e',
          fontSize: '0.9rem'
        }}>
          <strong>Debug Info:</strong> This test page helps verify that React Router and component rendering are working correctly.
        </p>
      </div>
    </div>
  );
};

export default TestPage;
