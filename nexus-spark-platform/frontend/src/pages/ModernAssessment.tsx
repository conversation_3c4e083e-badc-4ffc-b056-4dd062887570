import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Brain, 
  Target, 
  CheckCircle, 
  XCircle, 
  ArrowRight, 
  BookOpen,
  Zap,
  Award,
  TrendingUp
} from 'lucide-react';

interface Question {
  id: string;
  text: string;
  options: string[];
  correctAnswer: number;
  difficulty: number;
  topic: string;
  explanation: string;
}

const ModernAssessment: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [answers, setAnswers] = useState<(number | null)[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(1800); // 30 minutes
  const [isStarted, setIsStarted] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  const mockQuestions: Question[] = [
    {
      id: '1',
      text: 'What is the derivative of f(x) = 3x² + 2x - 1?',
      options: ['6x + 2', '6x - 2', '3x + 2', '6x² + 2x'],
      correctAnswer: 0,
      difficulty: 3,
      topic: 'Calculus',
      explanation: 'Using the power rule: d/dx(3x²) = 6x, d/dx(2x) = 2, d/dx(-1) = 0. Therefore, f\'(x) = 6x + 2.'
    },
    {
      id: '2',
      text: 'Which of the following is a characteristic of machine learning?',
      options: [
        'Programs are explicitly programmed for every scenario',
        'Systems learn patterns from data without explicit programming',
        'Only works with structured data',
        'Requires constant human intervention'
      ],
      correctAnswer: 1,
      difficulty: 2,
      topic: 'AI/ML',
      explanation: 'Machine learning enables systems to automatically learn and improve from experience without being explicitly programmed for every scenario.'
    },
    {
      id: '3',
      text: 'What is the time complexity of binary search?',
      options: ['O(n)', 'O(log n)', 'O(n²)', 'O(1)'],
      correctAnswer: 1,
      difficulty: 3,
      topic: 'Algorithms',
      explanation: 'Binary search divides the search space in half with each comparison, resulting in O(log n) time complexity.'
    }
  ];

  const [questions] = useState<Question[]>(mockQuestions);

  useEffect(() => {
    if (isStarted && timeRemaining > 0 && !isCompleted) {
      const timer = setTimeout(() => setTimeRemaining(timeRemaining - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0) {
      handleComplete();
    }
  }, [timeRemaining, isStarted, isCompleted]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStart = () => {
    setIsStarted(true);
    setAnswers(new Array(questions.length).fill(null));
  };

  const handleAnswerSelect = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
  };

  const handleNext = () => {
    if (selectedAnswer !== null) {
      const newAnswers = [...answers];
      newAnswers[currentQuestion] = selectedAnswer;
      setAnswers(newAnswers);
      
      if (currentQuestion < questions.length - 1) {
        setCurrentQuestion(currentQuestion + 1);
        setSelectedAnswer(null);
        setShowExplanation(false);
      } else {
        handleComplete();
      }
    }
  };

  const handleComplete = () => {
    setIsCompleted(true);
  };

  const calculateScore = () => {
    let correct = 0;
    answers.forEach((answer, index) => {
      if (answer === questions[index].correctAnswer) {
        correct++;
      }
    });
    return Math.round((correct / questions.length) * 100);
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isStarted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8 animate-slide-up">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                Adaptive Assessment
              </h1>
              <p className="text-xl text-gray-600">
                AI-powered personalized testing experience
              </p>
            </div>

            {/* Assessment Overview */}
            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <Card className="card-3d hover-scale shadow-enterprise animate-slide-up">
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-float">
                    <Brain className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl">Intelligent Testing</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-gray-600 mb-4">
                    Our AI adapts questions based on your performance using advanced Item Response Theory
                  </p>
                  <div className="flex justify-center space-x-4">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      <Zap className="h-3 w-3 mr-1" />
                      Adaptive
                    </Badge>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                      <Target className="h-3 w-3 mr-1" />
                      Precise
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="card-3d hover-scale shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
                <CardHeader className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-float" style={{ animationDelay: '1s' }}>
                    <Award className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-2xl">Performance Insights</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-gray-600 mb-4">
                    Get detailed analytics and personalized recommendations for improvement
                  </p>
                  <div className="flex justify-center space-x-4">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      Analytics
                    </Badge>
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                      <BookOpen className="h-3 w-3 mr-1" />
                      Learning
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Assessment Details */}
            <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.4s' }}>
              <CardHeader>
                <CardTitle className="text-center text-2xl">Assessment Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6 text-center">
                  <div className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                    <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-lg">Duration</h3>
                    <p className="text-gray-600">30 minutes</p>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                    <BookOpen className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-lg">Questions</h3>
                    <p className="text-gray-600">{questions.length} adaptive questions</p>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                    <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-lg">Topics</h3>
                    <p className="text-gray-600">Math, CS, AI/ML</p>
                  </div>
                </div>

                <div className="text-center mt-8">
                  <Button 
                    onClick={handleStart}
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 text-lg shadow-glow-blue hover-scale"
                  >
                    Start Assessment
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (isCompleted) {
    const score = calculateScore();
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-slide-up">
              <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse-glow">
                <CheckCircle className="h-12 w-12 text-white" />
              </div>
              <h1 className="text-4xl font-bold mb-4">Assessment Complete!</h1>
              <p className="text-xl text-gray-600 mb-8">Great job, {user.first_name}!</p>
            </div>

            <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <div className="text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                    {score}%
                  </div>
                  <p className="text-xl text-gray-600">Overall Score</p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Correct Answers</h3>
                    <p className="text-2xl font-bold text-blue-600">
                      {answers.filter((answer, index) => answer === questions[index].correctAnswer).length}/{questions.length}
                    </p>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Time Used</h3>
                    <p className="text-2xl font-bold text-purple-600">
                      {formatTime(1800 - timeRemaining)}
                    </p>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                    <h3 className="font-semibold text-lg mb-2">Performance</h3>
                    <p className="text-2xl font-bold text-green-600">
                      {score >= 80 ? 'Excellent' : score >= 60 ? 'Good' : 'Needs Improvement'}
                    </p>
                  </div>
                </div>

                <div className="flex justify-center space-x-4">
                  <Button 
                    onClick={() => navigate('/analytics')}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    View Detailed Analytics
                  </Button>
                  <Button 
                    onClick={() => navigate('/dashboard')}
                    variant="outline"
                  >
                    Back to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const currentQ = questions[currentQuestion];
  const progress = ((currentQuestion + 1) / questions.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8 animate-slide-up">
            <div>
              <h1 className="text-2xl font-bold">Assessment in Progress</h1>
              <p className="text-gray-600">Question {currentQuestion + 1} of {questions.length}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">{formatTime(timeRemaining)}</div>
              <p className="text-sm text-gray-600">Time Remaining</p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-8 animate-slide-up" style={{ animationDelay: '0.1s' }}>
            <Progress value={progress} className="h-3 bg-gray-200">
              <div 
                className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500 ease-out rounded-full"
                style={{ width: `${progress}%` }}
              />
            </Progress>
            <div className="flex justify-between text-sm text-gray-600 mt-2">
              <span>Progress: {Math.round(progress)}%</span>
              <span>Difficulty: {currentQ.difficulty}/5</span>
            </div>
          </div>

          {/* Question Card */}
          <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <Badge variant="secondary" className="mb-2">
                    {currentQ.topic}
                  </Badge>
                  <CardTitle className="text-xl leading-relaxed">
                    {currentQ.text}
                  </CardTitle>
                </div>
                <div className="flex space-x-1">
                  {Array.from({ length: currentQ.difficulty }).map((_, i) => (
                    <div key={i} className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  ))}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {currentQ.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(index)}
                    className={`w-full p-4 text-left rounded-lg border-2 transition-all-300 hover-scale ${
                      selectedAnswer === index
                        ? 'border-blue-500 bg-blue-50 shadow-glow-blue'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center ${
                        selectedAnswer === index
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}>
                        {selectedAnswer === index && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span className="text-gray-800">{option}</span>
                    </div>
                  </button>
                ))}
              </div>

              {showExplanation && (
                <div className="mt-6 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-r-lg animate-slide-up">
                  <h4 className="font-semibold text-blue-800 mb-2">Explanation:</h4>
                  <p className="text-blue-700">{currentQ.explanation}</p>
                </div>
              )}

              <div className="flex justify-between items-center mt-8">
                <Button
                  variant="outline"
                  onClick={() => setShowExplanation(!showExplanation)}
                  disabled={selectedAnswer === null}
                >
                  {showExplanation ? 'Hide' : 'Show'} Explanation
                </Button>
                
                <Button
                  onClick={handleNext}
                  disabled={selectedAnswer === null}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {currentQuestion === questions.length - 1 ? 'Complete Assessment' : 'Next Question'}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ModernAssessment;
