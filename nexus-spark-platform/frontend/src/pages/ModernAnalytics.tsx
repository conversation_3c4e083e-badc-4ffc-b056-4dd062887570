import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  BookOpen, 
  Target, 
  Clock,
  Brain,
  Award,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Star,
  ArrowUp,
  ArrowDown,
  Eye,
  Download
} from 'lucide-react';

const ModernAnalytics: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  useEffect(() => {
    // Simulate loading analytics data
    const loadData = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setAnalyticsData({
        overview: {
          totalUsers: 12847,
          activeUsers: 8934,
          totalAssessments: 45678,
          avgScore: 78.5,
          growthRate: 12.3,
          completionRate: 89.2
        },
        performance: {
          weeklyScores: [72, 75, 78, 82, 85, 88, 91],
          topicMastery: [
            { topic: 'Mathematics', mastery: 85, trend: 'up' },
            { topic: 'Science', mastery: 78, trend: 'up' },
            { topic: 'English', mastery: 92, trend: 'stable' },
            { topic: 'History', mastery: 74, trend: 'down' }
          ],
          difficultyDistribution: {
            easy: 35,
            medium: 45,
            hard: 20
          }
        },
        engagement: {
          dailyActive: [120, 145, 178, 210, 234, 267, 289],
          sessionDuration: 24.5,
          retentionRate: 76.3,
          satisfactionScore: 4.6
        }
      });
      setLoading(false);
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Analytics</h2>
          <p className="text-gray-500">Preparing your insights...</p>
        </div>
      </div>
    );
  }

  const StatCard = ({ title, value, change, icon: Icon, color, trend }: any) => (
    <Card className="card-3d hover-scale shadow-enterprise transition-all-300 animate-slide-up">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900">{value}</p>
            <div className="flex items-center mt-2">
              {trend === 'up' ? (
                <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm font-medium ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                {change}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${color} animate-float`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const TopicCard = ({ topic, mastery, trend }: any) => (
    <Card className="card-3d hover-scale shadow-enterprise transition-all-300">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-800">{topic}</h3>
          <Badge variant={trend === 'up' ? 'default' : trend === 'down' ? 'destructive' : 'secondary'}>
            {trend === 'up' ? <TrendingUp className="h-3 w-3 mr-1" /> : 
             trend === 'down' ? <TrendingDown className="h-3 w-3 mr-1" /> : 
             <Activity className="h-3 w-3 mr-1" />}
            {trend}
          </Badge>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Mastery Level</span>
            <span className="font-semibold">{mastery}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${mastery}%` }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8 animate-slide-up">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Analytics Dashboard
              </h1>
              <p className="text-xl text-gray-600 mt-2">
                Comprehensive insights into learning performance
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" className="hover-scale">
                <Eye className="h-4 w-4 mr-2" />
                View Report
              </Button>
              <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 hover-scale">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
            </div>
          </div>

          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Users"
              value={analyticsData.overview.totalUsers.toLocaleString()}
              change={analyticsData.overview.growthRate}
              icon={Users}
              color="bg-gradient-to-r from-blue-500 to-blue-600"
              trend="up"
            />
            <StatCard
              title="Active Users"
              value={analyticsData.overview.activeUsers.toLocaleString()}
              change="8.7"
              icon={Activity}
              color="bg-gradient-to-r from-green-500 to-green-600"
              trend="up"
            />
            <StatCard
              title="Assessments"
              value={analyticsData.overview.totalAssessments.toLocaleString()}
              change="15.2"
              icon={BookOpen}
              color="bg-gradient-to-r from-purple-500 to-purple-600"
              trend="up"
            />
            <StatCard
              title="Avg Score"
              value={`${analyticsData.overview.avgScore}%`}
              change="3.4"
              icon={Target}
              color="bg-gradient-to-r from-orange-500 to-orange-600"
              trend="up"
            />
          </div>

          {/* Main Analytics Tabs */}
          <Tabs defaultValue="performance" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 bg-white shadow-enterprise rounded-lg p-1">
              <TabsTrigger value="performance" className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4" />
                <span>Performance</span>
              </TabsTrigger>
              <TabsTrigger value="engagement" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Engagement</span>
              </TabsTrigger>
              <TabsTrigger value="topics" className="flex items-center space-x-2">
                <Brain className="h-4 w-4" />
                <span>Topics</span>
              </TabsTrigger>
              <TabsTrigger value="insights" className="flex items-center space-x-2">
                <Zap className="h-4 w-4" />
                <span>AI Insights</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="performance" className="space-y-6">
              <div className="grid lg:grid-cols-3 gap-6">
                {/* Performance Chart */}
                <Card className="lg:col-span-2 shadow-enterprise animate-slide-up">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                      Weekly Performance Trend
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-end justify-between space-x-2">
                      {analyticsData.performance.weeklyScores.map((score: number, index: number) => (
                        <div key={index} className="flex flex-col items-center flex-1">
                          <div 
                            className="w-full bg-gradient-to-t from-blue-500 to-purple-500 rounded-t-lg transition-all duration-1000 ease-out hover-scale"
                            style={{ 
                              height: `${(score / 100) * 200}px`,
                              animationDelay: `${index * 0.1}s`
                            }}
                          />
                          <span className="text-xs text-gray-600 mt-2">
                            Week {index + 1}
                          </span>
                          <span className="text-sm font-semibold text-gray-800">
                            {score}%
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Summary */}
                <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Award className="h-5 w-5 mr-2 text-purple-600" />
                      Performance Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-green-800">Completion Rate</span>
                        <span className="text-lg font-bold text-green-600">
                          {analyticsData.overview.completionRate}%
                        </span>
                      </div>
                      <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${analyticsData.overview.completionRate}%` }}
                        />
                      </div>
                    </div>

                    <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-blue-800">Average Score</span>
                        <span className="text-lg font-bold text-blue-600">
                          {analyticsData.overview.avgScore}%
                        </span>
                      </div>
                      <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${analyticsData.overview.avgScore}%` }}
                        />
                      </div>
                    </div>

                    <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-purple-800">Growth Rate</span>
                        <span className="text-lg font-bold text-purple-600">
                          +{analyticsData.overview.growthRate}%
                        </span>
                      </div>
                      <div className="flex items-center mt-2">
                        <TrendingUp className="h-4 w-4 text-purple-500 mr-1" />
                        <span className="text-sm text-purple-600">Trending upward</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="engagement" className="space-y-6">
              <div className="grid lg:grid-cols-2 gap-6">
                <Card className="shadow-enterprise animate-slide-up">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="h-5 w-5 mr-2 text-green-600" />
                      Daily Active Users
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 flex items-end justify-between space-x-1">
                      {analyticsData.engagement.dailyActive.map((users: number, index: number) => (
                        <div key={index} className="flex flex-col items-center flex-1">
                          <div 
                            className="w-full bg-gradient-to-t from-green-400 to-green-600 rounded-t transition-all duration-1000 ease-out"
                            style={{ 
                              height: `${(users / 300) * 150}px`,
                              animationDelay: `${index * 0.1}s`
                            }}
                          />
                          <span className="text-xs text-gray-600 mt-1">
                            Day {index + 1}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Clock className="h-5 w-5 mr-2 text-orange-600" />
                      Engagement Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600 mb-1">
                        {analyticsData.engagement.sessionDuration} min
                      </div>
                      <p className="text-sm text-gray-600">Average Session Duration</p>
                    </div>

                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600 mb-1">
                        {analyticsData.engagement.retentionRate}%
                      </div>
                      <p className="text-sm text-gray-600">User Retention Rate</p>
                    </div>

                    <div className="text-center">
                      <div className="flex justify-center items-center mb-1">
                        <span className="text-3xl font-bold text-yellow-600 mr-2">
                          {analyticsData.engagement.satisfactionScore}
                        </span>
                        <Star className="h-6 w-6 text-yellow-500 fill-current" />
                      </div>
                      <p className="text-sm text-gray-600">Satisfaction Score</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="topics" className="space-y-6">
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {analyticsData.performance.topicMastery.map((topic: any, index: number) => (
                  <div key={index} style={{ animationDelay: `${index * 0.1}s` }}>
                    <TopicCard {...topic} />
                  </div>
                ))}
              </div>

              <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.4s' }}>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="h-5 w-5 mr-2 text-indigo-600" />
                    Difficulty Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                      <div className="text-2xl font-bold text-green-600 mb-2">
                        {analyticsData.performance.difficultyDistribution.easy}%
                      </div>
                      <p className="text-sm font-medium text-green-800">Easy Questions</p>
                    </div>
                    <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600 mb-2">
                        {analyticsData.performance.difficultyDistribution.medium}%
                      </div>
                      <p className="text-sm font-medium text-yellow-800">Medium Questions</p>
                    </div>
                    <div className="text-center p-4 bg-gradient-to-br from-red-50 to-red-100 rounded-lg">
                      <div className="text-2xl font-bold text-red-600 mb-2">
                        {analyticsData.performance.difficultyDistribution.hard}%
                      </div>
                      <p className="text-sm font-medium text-red-800">Hard Questions</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="insights" className="space-y-6">
              <div className="grid lg:grid-cols-2 gap-6">
                <Card className="shadow-enterprise animate-slide-up">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Brain className="h-5 w-5 mr-2 text-purple-600" />
                      AI-Powered Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border-l-4 border-blue-500">
                      <h4 className="font-semibold text-blue-800 mb-2">Performance Prediction</h4>
                      <p className="text-blue-700 text-sm">
                        Based on current trends, user performance is expected to improve by 8-12% over the next month.
                      </p>
                    </div>
                    <div className="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border-l-4 border-green-500">
                      <h4 className="font-semibold text-green-800 mb-2">Optimal Study Time</h4>
                      <p className="text-green-700 text-sm">
                        Users show peak performance during 20-25 minute study sessions with 5-minute breaks.
                      </p>
                    </div>
                    <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border-l-4 border-purple-500">
                      <h4 className="font-semibold text-purple-800 mb-2">Difficulty Adaptation</h4>
                      <p className="text-purple-700 text-sm">
                        The adaptive algorithm has reduced question difficulty variance by 23% while maintaining engagement.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.2s' }}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Zap className="h-5 w-5 mr-2 text-yellow-600" />
                      Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg">
                      <h4 className="font-semibold text-orange-800 mb-2">Focus Areas</h4>
                      <ul className="text-orange-700 text-sm space-y-1">
                        <li>• Increase History topic coverage by 15%</li>
                        <li>• Add more intermediate-level Science questions</li>
                        <li>• Implement spaced repetition for Math concepts</li>
                      </ul>
                    </div>
                    <div className="p-4 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg">
                      <h4 className="font-semibold text-indigo-800 mb-2">Engagement Boosters</h4>
                      <ul className="text-indigo-700 text-sm space-y-1">
                        <li>• Add gamification elements to assessments</li>
                        <li>• Introduce peer comparison features</li>
                        <li>• Create achievement badges for milestones</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ModernAnalytics;
