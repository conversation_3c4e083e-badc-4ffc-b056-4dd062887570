import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  Users,
  BookOpen,
  Target,
  Clock,
  Brain,
  Award,
  BarChart3,
  Activity,
  Zap,
  Star,
  ArrowUp,
  ArrowDown,
  Play,
  ChevronRight,
  Calendar,
  Sparkles,
  Trophy,
  Flame,
  Globe,
  Layers
} from 'lucide-react';

const ModernDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);

  useEffect(() => {
    // Simulate loading dashboard data
    const loadData = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setDashboardData({
        stats: {
          totalAssessments: 23,
          averageScore: 85.7,
          timeSpent: 47.2,
          currentStreak: 12,
          improvementRate: 15.3,
          topicsCompleted: 8,
          rank: 156,
          totalUsers: 12847
        },
        recentActivity: [
          {
            id: 1,
            activity: 'Completed Advanced Calculus Assessment',
            score: 92,
            time: '2 hours ago',
            type: 'assessment',
            difficulty: 'Advanced'
          },
          {
            id: 2,
            activity: 'Started Machine Learning Fundamentals',
            time: '5 hours ago',
            type: 'start',
            difficulty: 'Beginner'
          },
          {
            id: 3,
            activity: 'Achieved 90% in Data Structures',
            score: 90,
            time: '1 day ago',
            type: 'achievement',
            difficulty: 'Intermediate'
          }
        ],
        upcomingTests: [
          {
            id: 'quantum-001',
            title: 'Quantum Physics Principles',
            subject: 'Physics',
            difficulty: 'Advanced',
            duration: 50,
            scheduledFor: '2024-07-27T10:00:00Z'
          },
          {
            id: 'bio-genetics-001',
            title: 'Genetics and Molecular Biology',
            subject: 'Biology',
            difficulty: 'Intermediate',
            duration: 40,
            scheduledFor: '2024-07-28T14:00:00Z'
          }
        ],
        topicProgress: [
          { topic: 'Mathematics', progress: 85, trend: 'up', color: 'blue' },
          { topic: 'Computer Science', progress: 92, trend: 'up', color: 'purple' },
          { topic: 'Physics', progress: 78, trend: 'stable', color: 'green' },
          { topic: 'AI/ML', progress: 95, trend: 'up', color: 'orange' }
        ]
      });
      setLoading(false);
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Dashboard</h2>
          <p className="text-gray-500">Preparing your personalized insights...</p>
        </div>
      </div>
    );
  }

  const StatCard = ({ title, value, change, icon: Icon, color, trend, subtitle }: any) => (
    <Card className="card-3d hover-scale shadow-enterprise transition-all-300 animate-slide-up">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
            {change && (
              <div className="flex items-center">
                {trend === 'up' ? (
                  <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  {change}%
                </span>
                <span className="text-sm text-gray-500 ml-1">{subtitle}</span>
              </div>
            )}
          </div>
          <div className={`w-14 h-14 rounded-xl flex items-center justify-center ${color} animate-float shadow-lg`}>
            <Icon className="h-7 w-7 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const QuickActionCard = ({ title, description, icon: Icon, color, onClick, badge }: any) => (
    <Card 
      className="card-3d hover-scale shadow-enterprise transition-all-300 cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color} animate-float group-hover:scale-110 transition-transform`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
          {badge && (
            <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white">
              {badge}
            </Badge>
          )}
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
          {title}
        </h3>
        <p className="text-sm text-gray-600 mb-4">{description}</p>
        <div className="flex items-center text-blue-600 group-hover:translate-x-1 transition-transform">
          <span className="text-sm font-medium">Get Started</span>
          <ChevronRight className="h-4 w-4 ml-1" />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8 animate-slide-up">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Welcome back, {user?.first_name}! 👋
              </h1>
              <p className="text-xl text-gray-600 mt-2">
                Ready to continue your learning journey?
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Badge className="bg-gradient-to-r from-green-500 to-blue-500 text-white px-3 py-1">
                <Flame className="h-4 w-4 mr-1" />
                {dashboardData.stats.currentStreak} day streak
              </Badge>
              <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1">
                <Trophy className="h-4 w-4 mr-1" />
                Rank #{dashboardData.stats.rank}
              </Badge>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Assessments"
              value={dashboardData.stats.totalAssessments}
              change={dashboardData.stats.improvementRate}
              icon={BookOpen}
              color="bg-gradient-to-r from-blue-500 to-blue-600"
              trend="up"
              subtitle="this month"
            />
            <StatCard
              title="Average Score"
              value={`${dashboardData.stats.averageScore}%`}
              change="8.3"
              icon={Target}
              color="bg-gradient-to-r from-green-500 to-green-600"
              trend="up"
              subtitle="improvement"
            />
            <StatCard
              title="Study Time"
              value={`${dashboardData.stats.timeSpent}h`}
              change="12.5"
              icon={Clock}
              color="bg-gradient-to-r from-purple-500 to-purple-600"
              trend="up"
              subtitle="this week"
            />
            <StatCard
              title="Topics Mastered"
              value={dashboardData.stats.topicsCompleted}
              change="25.0"
              icon={Brain}
              color="bg-gradient-to-r from-orange-500 to-orange-600"
              trend="up"
              subtitle="completed"
            />
          </div>

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-3 gap-8 mb-8">
            {/* Quick Actions */}
            <div className="lg:col-span-2">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 animate-slide-up">Quick Actions</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <QuickActionCard
                  title="Take Assessment"
                  description="Start an AI-powered adaptive assessment tailored to your level"
                  icon={Play}
                  color="bg-gradient-to-r from-blue-500 to-blue-600"
                  onClick={() => navigate('/assessment')}
                  badge="AI Powered"
                />
                <QuickActionCard
                  title="Browse Tests"
                  description="Explore our comprehensive library of tests across all subjects"
                  icon={BookOpen}
                  color="bg-gradient-to-r from-purple-500 to-purple-600"
                  onClick={() => navigate('/tests')}
                />
                <QuickActionCard
                  title="View Analytics"
                  description="Deep dive into your performance metrics and learning insights"
                  icon={BarChart3}
                  color="bg-gradient-to-r from-green-500 to-green-600"
                  onClick={() => navigate('/analytics')}
                />
                <QuickActionCard
                  title="Generate Questions"
                  description="Create custom questions using our advanced AI question generator"
                  icon={Sparkles}
                  color="bg-gradient-to-r from-orange-500 to-orange-600"
                  onClick={() => navigate('/generate')}
                  badge="New"
                />
              </div>
            </div>

            {/* Topic Progress */}
            <div className="animate-slide-up" style={{ animationDelay: '0.2s' }}>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Topic Progress</h2>
              <Card className="shadow-enterprise">
                <CardContent className="p-6">
                  <div className="space-y-6">
                    {dashboardData.topicProgress.map((topic: any, index: number) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-gray-900">{topic.topic}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-semibold text-gray-700">{topic.progress}%</span>
                            {topic.trend === 'up' ? (
                              <TrendingUp className="h-4 w-4 text-green-500" />
                            ) : topic.trend === 'down' ? (
                              <TrendingDown className="h-4 w-4 text-red-500" />
                            ) : (
                              <Activity className="h-4 w-4 text-gray-400" />
                            )}
                          </div>
                        </div>
                        <Progress value={topic.progress} className="h-2">
                          <div 
                            className={`h-full bg-gradient-to-r from-${topic.color}-400 to-${topic.color}-600 transition-all duration-1000 ease-out rounded-full`}
                            style={{ width: `${topic.progress}%` }}
                          />
                        </Progress>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Activity & Upcoming Tests */}
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.3s' }}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2 text-blue-600" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.recentActivity.map((activity: any) => (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg hover-scale transition-all-300">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        activity.type === 'assessment' ? 'bg-blue-100' :
                        activity.type === 'achievement' ? 'bg-green-100' : 'bg-purple-100'
                      }`}>
                        {activity.type === 'assessment' ? <Target className="h-5 w-5 text-blue-600" /> :
                         activity.type === 'achievement' ? <Award className="h-5 w-5 text-green-600" /> :
                         <Play className="h-5 w-5 text-purple-600" />}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{activity.activity}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          {activity.score && (
                            <Badge variant="secondary" className="text-xs">
                              {activity.score}%
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {activity.difficulty}
                          </Badge>
                          <span className="text-xs text-gray-500">{activity.time}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Tests */}
            <Card className="shadow-enterprise animate-slide-up" style={{ animationDelay: '0.4s' }}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-purple-600" />
                  Upcoming Tests
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardData.upcomingTests.map((test: any) => (
                    <div key={test.id} className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100 hover-scale transition-all-300">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold text-gray-900">{test.title}</h4>
                        <Badge variant="outline" className="text-xs">
                          {test.subject}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {test.duration} min
                        </div>
                        <div className="flex items-center">
                          <Target className="h-4 w-4 mr-1" />
                          {test.difficulty}
                        </div>
                      </div>
                      <div className="mt-3">
                        <Button 
                          size="sm" 
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                          onClick={() => navigate(`/assessment/${test.id}`)}
                        >
                          Start Test
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernDashboard;
