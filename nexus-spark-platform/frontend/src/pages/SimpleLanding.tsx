import React from 'react';
import { Link } from 'react-router-dom';

const SimpleLanding: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#f9fafb',
      fontFamily: 'Inter, sans-serif'
    }}>
      {/* Navigation */}
      <nav style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e5e7eb',
        padding: '1rem 0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 1rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#111827'
          }}>
            Nexus-Spark Platform
          </div>
          <div style={{ display: 'flex', gap: '1rem' }}>
            <Link
              to="/login?demo=true"
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#16a34a',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '0.375rem',
                fontWeight: '500'
              }}
            >
              🚀 Demo Login
            </Link>
            <Link
              to="/login"
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#2563eb',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '0.375rem',
                fontWeight: '500'
              }}
            >
              Login
            </Link>
            <Link
              to="/register"
              style={{
                padding: '0.5rem 1rem',
                border: '1px solid #d1d5db',
                color: '#374151',
                textDecoration: 'none',
                borderRadius: '0.375rem',
                fontWeight: '500'
              }}
            >
              Register
            </Link>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '4rem 1rem',
        textAlign: 'center'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: '1rem',
          lineHeight: '1.2'
        }}>
          AI-Powered Adaptive Learning Platform
        </h1>
        <p style={{
          fontSize: '1.25rem',
          color: '#6b7280',
          marginBottom: '2rem',
          maxWidth: '600px',
          margin: '0 auto 2rem auto'
        }}>
          Experience personalized learning with advanced AI question generation, 
          adaptive assessments, and comprehensive analytics.
        </p>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
          <Link
            to="/login?demo=true"
            style={{
              padding: '0.75rem 2rem',
              backgroundColor: '#16a34a',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          >
            🚀 Demo Login
          </Link>
          <Link
            to="/dashboard"
            style={{
              padding: '0.75rem 2rem',
              backgroundColor: '#2563eb',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem'
            }}
          >
            Get Started
          </Link>
          <Link
            to="/layout-test"
            style={{
              padding: '0.75rem 2rem',
              border: '1px solid #d1d5db',
              color: '#374151',
              textDecoration: 'none',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem'
            }}
          >
            View Layout Test
          </Link>
        </div>

        {/* Demo Credentials */}
        <div style={{
          marginTop: '3rem',
          padding: '1.5rem',
          backgroundColor: '#f0f9ff',
          border: '1px solid #0ea5e9',
          borderRadius: '0.5rem',
          maxWidth: '400px',
          margin: '3rem auto 0 auto'
        }}>
          <h3 style={{
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#0c4a6e',
            marginBottom: '1rem',
            textAlign: 'center'
          }}>
            🎭 Demo Credentials
          </h3>
          <div style={{ fontSize: '0.875rem', color: '#0c4a6e' }}>
            <div style={{ marginBottom: '0.5rem' }}>
              <strong>Email:</strong> <EMAIL>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <strong>Password:</strong> demo123
            </div>
            <div style={{
              fontSize: '0.75rem',
              color: '#0369a1',
              textAlign: 'center',
              fontStyle: 'italic'
            }}>
              Click "Demo Login" for auto-filled credentials
            </div>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem 1rem'
      }}>
        <h2 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#111827',
          textAlign: 'center',
          marginBottom: '3rem'
        }}>
          Platform Features
        </h2>
        <div style={{
          display: 'grid',
          gap: '2rem',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))'
        }}>
          {[
            {
              title: 'Adaptive Assessments',
              description: 'AI-powered assessments that adapt to student ability using Item Response Theory',
              icon: '🎯'
            },
            {
              title: 'Question Generation',
              description: 'Generate unlimited questions using advanced AI models like GPT-4 and Groq',
              icon: '🤖'
            },
            {
              title: 'Analytics Dashboard',
              description: 'Comprehensive analytics with real-time insights and performance tracking',
              icon: '📊'
            },
            {
              title: 'Personalized Learning',
              description: 'Tailored learning paths based on individual student performance and preferences',
              icon: '👤'
            }
          ].map((feature, index) => (
            <div key={index} style={{
              backgroundColor: 'white',
              padding: '2rem',
              borderRadius: '0.5rem',
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
              textAlign: 'center'
            }}>
              <div style={{
                fontSize: '3rem',
                marginBottom: '1rem'
              }}>
                {feature.icon}
              </div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '0.5rem'
              }}>
                {feature.title}
              </h3>
              <p style={{
                color: '#6b7280',
                lineHeight: '1.5'
              }}>
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Status Section */}
      <div style={{
        backgroundColor: 'white',
        borderTop: '1px solid #e5e7eb',
        padding: '2rem 0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 1rem',
          textAlign: 'center'
        }}>
          <h3 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '1rem'
          }}>
            Platform Status
          </h3>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.5rem 1rem',
            backgroundColor: '#dcfce7',
            color: '#166534',
            borderRadius: '0.375rem',
            fontSize: '0.875rem',
            fontWeight: '500'
          }}>
            <div style={{
              width: '8px',
              height: '8px',
              backgroundColor: '#22c55e',
              borderRadius: '50%'
            }}></div>
            All Systems Operational
          </div>
          <p style={{
            color: '#6b7280',
            marginTop: '1rem',
            fontSize: '0.875rem'
          }}>
            Running in demo mode with mock data for testing purposes
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleLanding;
