/**
 * Nexus API Client for connecting frontend to backend services
 */

import { mockApi } from '../mockApi';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  meta?: {
    page?: number;
    per_page?: number;
    total?: number;
    total_pages?: number;
  };
}

interface AssessmentSession {
  session_id: string;
  user_id: string;
  topic_id: string;
  status: 'active' | 'completed' | 'paused';
  current_theta: number[];
  items_administered: number;
  max_items: number;
  precision_threshold: number;
  started_at: string;
  completed_at?: string;
}

interface AssessmentItem {
  item_id: string;
  content: {
    question: string;
    options: string[];
    correct_answer: string;
  };
  topic_id: number;
  difficulty_level: string;
  estimated_time: number;
}

interface AssessmentResponse {
  status: 'in_progress' | 'completed';
  next_item?: AssessmentItem;
  theta_estimate?: number[];
  progress?: {
    current_item: number;
    total_items: number;
    percentage: number;
  };
}

interface QuestionGenerationParams {
  subject: string;
  grade_level: number;
  difficulty_level: 'easy' | 'medium' | 'hard';
  topics: string[];
  board_type: string;
  question_count: number;
  bloom_taxonomy_level: string;
}

interface GeneratedQuestion {
  content: string;
  options: string[];
  correct_answer: string;
  explanation: string;
  estimated_difficulty: number;
  bloom_level: string;
  metadata: Record<string, any>;
}

class NexusAPIClient {
  private baseURL: string;
  private authToken: string | null = null;
  private isDemoMode: boolean;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.isDemoMode = import.meta.env.VITE_DEMO_MODE === 'true' ||
                      import.meta.env.VITE_DEMO_MODE === true ||
                      localStorage.getItem('demo_mode') === 'true';

    if (this.isDemoMode) {
      console.log('🎭 Running in DEMO MODE with mock data');
      console.log('🎭 VITE_DEMO_MODE:', import.meta.env.VITE_DEMO_MODE);
      console.log('🎭 All env vars:', import.meta.env);
    }
    this.authToken = localStorage.getItem('auth_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    // Handle demo mode with mock data
    if (this.isDemoMode) {
      return this.getMockData<T>(endpoint, options);
    }

    const url = `${this.baseURL}${endpoint}`;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private async getMockData<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    console.log('🎭 Mock API called for endpoint:', endpoint);

    // Mock data mapping
    const mockDataMap: Record<string, any> = {
      '/auth/login': {
        success: true,
        data: {
          user: {
            user_id: 'demo-user-123',
            email: '<EMAIL>',
            first_name: 'Demo',
            last_name: 'Student',
            role: 'student',
            created_at: new Date().toISOString(),
            last_login: new Date().toISOString(),
          },
          tokens: {
            access_token: 'mock-jwt-token-12345',
            refresh_token: 'mock-refresh-token-67890',
            expires_in: 3600,
          },
        },
      },
      '/questions/templates': {
        success: true,
        data: [
          {
            template_id: 'template-1',
            name: 'Multiple Choice - Basic',
            description: 'Standard multiple choice questions with 4 options',
            question_type: 'multiple_choice',
            structure: {
              question: 'string',
              options: ['string', 'string', 'string', 'string'],
              correct_answer: 'string',
              explanation: 'string'
            }
          },
          {
            template_id: 'template-2',
            name: 'True/False - Conceptual',
            description: 'True or false questions for concept verification',
            question_type: 'true_false',
            structure: {
              question: 'string',
              correct_answer: 'boolean',
              explanation: 'string'
            }
          }
        ]
      },
      '/questions/providers/status': {
        success: true,
        data: {
          groq: {
            status: 'available',
            response_time: 1.2,
            rate_limit: { remaining: 95, total: 100 },
            models: ['llama-3.1-70b-versatile', 'mixtral-8x7b-32768']
          },
          openai: {
            status: 'available',
            response_time: 2.1,
            rate_limit: { remaining: 48, total: 50 },
            models: ['gpt-4', 'gpt-3.5-turbo']
          },
          anthropic: {
            status: 'limited',
            response_time: 1.8,
            rate_limit: { remaining: 5, total: 10 },
            models: ['claude-3-sonnet', 'claude-3-haiku']
          }
        }
      },
      '/dashboard/data': await mockApi.mockDashboardData(),
      '/analytics/dashboard': {
        success: true,
        data: {
          totalUsers: 1247,
          activeUsersToday: 89,
          activeUsersWeek: 456,
          totalAssessments: 3456,
          recentActivity: [
            {
              user_id: 'demo-user-123',
              activity: 'Completed Math Assessment',
              timestamp: new Date().toISOString(),
              score: 85
            },
            {
              user_id: 'demo-user-456',
              activity: 'Generated Science Questions',
              timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
            }
          ],
          trendingTopics: [
            { topic_id: 1, topic_name: 'Algebra', popularity_score: 95, recent_attempts: 234 },
            { topic_id: 2, topic_name: 'Biology', popularity_score: 87, recent_attempts: 189 },
            { topic_id: 3, topic_name: 'Grammar', popularity_score: 82, recent_attempts: 156 }
          ],
          platformPerformance: {
            averageScore: 78.5,
            completionRate: 89.2,
            userSatisfaction: 4.6,
            contentQuality: 4.8
          },
          userEngagement: {
            dailyActiveUsers: 89,
            averageSessionTime: 24.5,
            retentionRate: 76.3,
            questionsGenerated: 12890
          },
          userGrowthChart: {
            labels: Array.from({ length: 7 }, (_, i) =>
              new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString()
            ),
            data: [120, 145, 178, 210, 234, 267, 289]
          },
          performanceTrends: {
            labels: Array.from({ length: 7 }, (_, i) =>
              new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString()
            ),
            datasets: [
              {
                label: 'Average Score',
                data: [65, 68, 72, 75, 78, 82, 85],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)'
              }
            ]
          },
          topicPopularity: {
            labels: ['Math', 'Science', 'English', 'History'],
            data: [85, 78, 92, 65]
          }
        }
      },
      '/analytics/performance': {
        success: true,
        data: {
          performance_data: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            score: 60 + Math.random() * 40,
            ability: Math.random(),
            confidence: 0.7 + Math.random() * 0.3,
            items_completed: Math.floor(Math.random() * 20) + 5,
          })),
          summary: {
            total_assessments: 45,
            average_score: 78.5,
            improvement_rate: 12.3,
            time_spent: 145.2,
          },
        },
      },
      '/analytics/metrics': {
        success: true,
        data: {
          real_time: {
            active_users: 1247,
            active_sessions: 89,
            questions_answered: 15420,
            avg_response_time: 2.3,
          },
          daily: {
            new_users: 23,
            completed_assessments: 156,
            generated_questions: 890,
            system_uptime: 99.8,
          },
        },
      },
    };

    // Handle question generation endpoints
    if (endpoint === '/questions/generate' && options.method === 'POST') {
      const jobId = `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      return {
        success: true,
        data: {
          job_id: jobId,
          request: JSON.parse(options.body as string),
          status: 'pending',
          progress: 0,
          total_questions: JSON.parse(options.body as string).count || 5,
          generated_questions: [],
          failed_count: 0,
          created_at: new Date().toISOString(),
          estimated_completion: new Date(Date.now() + 30000).toISOString()
        }
      };
    }

    if (endpoint.includes('/questions/jobs/')) {
      const jobId = endpoint.split('/').pop();
      return {
        success: true,
        data: {
          job_id: jobId,
          status: 'completed',
          progress: 100,
          total_questions: 5,
          generated_questions: Array.from({ length: 5 }, (_, i) => ({
            question_id: `q-${jobId}-${i + 1}`,
            content: {
              question: `Sample question ${i + 1} about the given topic?`,
              options: [
                'Option A - Correct answer',
                'Option B - Incorrect',
                'Option C - Incorrect',
                'Option D - Incorrect'
              ],
              correct_answer: 'Option A - Correct answer',
              explanation: 'This is the correct answer because...',
              distractors: ['Option B - Incorrect', 'Option C - Incorrect', 'Option D - Incorrect']
            },
            metadata: {
              subject: 'Mathematics',
              topic: 'Algebra',
              grade_level: 9,
              difficulty_level: 'medium',
              bloom_level: 'apply',
              question_type: 'multiple_choice',
              estimated_difficulty: 0.6,
              cognitive_load: 0.7
            },
            quality_metrics: {
              overall_score: 0.85,
              clarity_score: 0.9,
              difficulty_appropriateness: 0.8,
              educational_value: 0.85,
              language_quality: 0.9,
              bias_check: 0.95
            },
            ai_provider: 'groq',
            generation_time: 2.3,
            created_at: new Date().toISOString(),
            status: 'generated'
          })),
          failed_count: 0,
          created_at: new Date(Date.now() - 30000).toISOString(),
          completed_at: new Date().toISOString()
        }
      };
    }

    if (endpoint.includes('/questions/history/')) {
      return {
        success: true,
        data: [
          {
            job_id: 'job-history-1',
            request: {
              subject: 'Mathematics',
              topic: 'Algebra',
              grade_level: 9,
              difficulty_level: 'medium',
              bloom_level: 'apply',
              question_type: 'multiple_choice',
              count: 5
            },
            status: 'completed',
            total_questions: 5,
            generated_questions: 5,
            created_at: new Date(Date.now() - 86400000).toISOString(),
            completed_at: new Date(Date.now() - 86400000 + 30000).toISOString()
          },
          {
            job_id: 'job-history-2',
            request: {
              subject: 'Science',
              topic: 'Biology',
              grade_level: 10,
              difficulty_level: 'hard',
              bloom_level: 'analyze',
              question_type: 'short_answer',
              count: 3
            },
            status: 'completed',
            total_questions: 3,
            generated_questions: 3,
            created_at: new Date(Date.now() - 172800000).toISOString(),
            completed_at: new Date(Date.now() - 172800000 + 45000).toISOString()
          }
        ]
      };
    }

    // Handle dynamic endpoints (with IDs)
    if (endpoint.includes('/analytics/user/')) {
      return {
        success: true,
        data: {
          totalAssessments: 23,
          averageScore: 0.785,
          improvementRate: 0.123,
          timeSpent: 145.2,
          topicsCompleted: 15,
          currentStreak: 7,
          recentPerformance: Array.from({ length: 10 }, (_, i) => ({
            date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            score: 0.6 + Math.random() * 0.4,
            topic: ['Math', 'Science', 'English', 'History'][Math.floor(Math.random() * 4)],
            assessment_id: `assessment-${i + 1}`,
            time_spent: Math.floor(Math.random() * 30) + 10,
          })),
          skillProgress: [
            { skill: 'Algebra', level: 0.8, progress: 0.15 },
            { skill: 'Geometry', level: 0.6, progress: 0.22 },
            { skill: 'Statistics', level: 0.9, progress: 0.08 },
            { skill: 'Calculus', level: 0.4, progress: 0.35 }
          ],
          weeklyProgress: Array.from({ length: 7 }, (_, i) => ({
            day: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('en', { weekday: 'short' }),
            assessments: Math.floor(Math.random() * 5) + 1,
            score: 0.6 + Math.random() * 0.4,
            timeSpent: Math.floor(Math.random() * 60) + 20
          }))
        },
      };
    }

    if (endpoint.includes('/analytics/metrics/realtime')) {
      return {
        success: true,
        data: {
          activeUsers: 89,
          activeAssessments: 23,
          questionsAnswered: 1547,
          averageResponseTime: 2.3,
          systemLoad: 45.2,
          errorRate: 0.02,
          lastUpdated: new Date().toISOString()
        },
      };
    }

    if (endpoint.includes('/analytics/topic/')) {
      return {
        success: true,
        data: {
          topicName: 'Mathematics',
          totalQuestions: 1250,
          averageDifficulty: 3.2,
          completionRate: 78.5,
          userEngagement: 85.3,
          recentActivity: Array.from({ length: 5 }, (_, i) => ({
            user_id: `user-${i + 1}`,
            activity: 'Completed assessment',
            score: 0.6 + Math.random() * 0.4,
            timestamp: new Date(Date.now() - i * 60 * 60 * 1000).toISOString()
          }))
        },
      };
    }

    if (endpoint.includes('/analytics/assessment/')) {
      return {
        success: true,
        data: {
          assessmentName: 'Math Assessment #1',
          totalAttempts: 156,
          averageScore: 0.785,
          averageTime: 24.5,
          difficultyDistribution: {
            easy: 25,
            medium: 45,
            hard: 30
          },
          performanceByQuestion: Array.from({ length: 10 }, (_, i) => ({
            questionId: `q-${i + 1}`,
            correctRate: 0.5 + Math.random() * 0.5,
            averageTime: 10 + Math.random() * 20,
            difficulty: Math.random() * 5
          }))
        },
      };
    }

    // Default mock response
    const defaultResponse = {
      success: true,
      data: {
        message: 'Mock data response',
        endpoint: endpoint,
        timestamp: new Date().toISOString(),
      },
    };

    return mockDataMap[endpoint] || defaultResponse;
  }

  // Authentication
  setAuthToken(token: string) {
    this.authToken = token;
    localStorage.setItem('auth_token', token);
  }

  clearAuthToken() {
    this.authToken = null;
    localStorage.removeItem('auth_token');
  }

  // Assessment API
  async startAssessment(userId: string, topicId: string): Promise<ApiResponse<AssessmentSession>> {
    return this.request('/assessments', {
      method: 'POST',
      body: JSON.stringify({
        user_id: userId,
        topic_id: topicId,
      }),
    });
  }

  async getAssessment(sessionId: string): Promise<ApiResponse<AssessmentSession>> {
    return this.request(`/assessments/${sessionId}`);
  }

  async submitResponse(
    sessionId: string,
    isCorrect: boolean
  ): Promise<ApiResponse<AssessmentResponse>> {
    return this.request(`/assessments/${sessionId}/respond`, {
      method: 'POST',
      body: JSON.stringify({
        is_correct: isCorrect,
      }),
    });
  }

  async endAssessment(sessionId: string): Promise<ApiResponse<void>> {
    return this.request(`/assessments/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // Item Bank API
  async getItems(params?: {
    topic_ids?: number[];
    bloom_ids?: number[];
    b_min?: number;
    b_max?: number;
    exclude_ids?: string[];
  }): Promise<ApiResponse<AssessmentItem[]>> {
    const searchParams = new URLSearchParams();
    
    if (params?.topic_ids) {
      params.topic_ids.forEach(id => searchParams.append('topic_id', id.toString()));
    }
    if (params?.bloom_ids) {
      params.bloom_ids.forEach(id => searchParams.append('bloom_dimension_id', id.toString()));
    }
    if (params?.b_min !== undefined) {
      searchParams.append('b_min', params.b_min.toString());
    }
    if (params?.b_max !== undefined) {
      searchParams.append('b_max', params.b_max.toString());
    }
    if (params?.exclude_ids) {
      searchParams.append('exclude_ids', params.exclude_ids.join(','));
    }

    const queryString = searchParams.toString();
    const endpoint = queryString ? `/items/query?${queryString}` : '/items/query';
    
    return this.request(endpoint);
  }

  // Question Generation API
  async generateQuestions(
    params: QuestionGenerationParams
  ): Promise<ApiResponse<GeneratedQuestion[]>> {
    return this.request('/questions/generate', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }



  // Analytics API
  async getAnalytics(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/user/${userId}`);
  }

  async getDashboardData(): Promise<ApiResponse<any>> {
    return this.request('/analytics/dashboard');
  }

  async getPerformanceMetrics(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/performance/${userId}`);
  }

  // Authentication API
  async login(email: string, password: string, rememberMe: boolean = false): Promise<ApiResponse<{
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    user: any;
  }>> {
    // Use mock API for demo credentials or if demo mode is enabled
    if (this.isDemoMode || (email === '<EMAIL>' && password === 'demo123')) {
      console.log('🎭 Using mock login API');
      return await mockApi.mockLogin(email, password);
    }

    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        remember_me: rememberMe,
      }),
    });
  }

  async register(userData: {
    email: string;
    password: string;
    confirm_password: string;
    first_name: string;
    last_name: string;
    grade_level?: number;
    school_id?: string;
    parent_email?: string;
  }): Promise<ApiResponse<any>> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
    user: any;
  }>> {
    return this.request('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({
        refresh_token: refreshToken,
      }),
    });
  }

  async logout(): Promise<void> {
    this.clearAuthToken();
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_profile');
  }

  async requestPasswordReset(email: string): Promise<ApiResponse<any>> {
    return this.request('/auth/password-reset', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async confirmPasswordReset(token: string, newPassword: string, confirmPassword: string): Promise<ApiResponse<any>> {
    return this.request('/auth/password-reset/confirm', {
      method: 'POST',
      body: JSON.stringify({
        token,
        new_password: newPassword,
        confirm_password: confirmPassword,
      }),
    });
  }

  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<ApiResponse<any>> {
    return this.request('/users/change-password', {
      method: 'POST',
      body: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword,
        confirm_password: confirmPassword,
      }),
    });
  }

  async verifyEmail(token: string): Promise<ApiResponse<any>> {
    return this.request('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify({ token }),
    });
  }

  // User Management API
  async getCurrentUser(): Promise<ApiResponse<any>> {
    return this.request('/users/me');
  }

  async updateCurrentUser(profile: any): Promise<ApiResponse<any>> {
    return this.request('/users/me', {
      method: 'PUT',
      body: JSON.stringify(profile),
    });
  }

  async searchUsers(params: {
    query?: string;
    role?: string;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();

    if (params.query) searchParams.append('query', params.query);
    if (params.role) searchParams.append('role', params.role);
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());

    const queryString = searchParams.toString();
    const endpoint = queryString ? `/users?${queryString}` : '/users';

    return this.request(endpoint);
  }

  // Analytics API
  async getDashboardData(): Promise<ApiResponse<any>> {
    return this.request('/analytics/dashboard');
  }

  async getUserAnalytics(userId: string, timeRange: string = 'last_30_days'): Promise<ApiResponse<any>> {
    return this.request(`/analytics/user/${userId}?time_range=${timeRange}`);
  }

  async getTopicAnalytics(topicId: number, timeRange: string = 'last_30_days'): Promise<ApiResponse<any>> {
    return this.request(`/analytics/topic/${topicId}?time_range=${timeRange}`);
  }

  async getAssessmentAnalytics(assessmentId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/assessment/${assessmentId}`);
  }

  async getRealTimeMetrics(): Promise<ApiResponse<any>> {
    return this.request('/analytics/metrics/realtime');
  }

  async getMetricTrends(metricType: string, timeRange: string = 'last_7_days'): Promise<ApiResponse<any>> {
    return this.request(`/analytics/metrics/trends?metric_type=${metricType}&time_range=${timeRange}`);
  }

  async generateReport(reportData: any): Promise<ApiResponse<any>> {
    return this.request('/analytics/reports/generate', {
      method: 'POST',
      body: JSON.stringify(reportData),
    });
  }

  async getReportStatus(reportId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/reports/${reportId}`);
  }

  // Assessment API
  async getAvailableAssessments(): Promise<ApiResponse<any>> {
    return this.request('/assessments/available');
  }

  async createAssessmentSession(config: any): Promise<ApiResponse<any>> {
    return this.request('/assessments/sessions', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  }

  async getAssessmentSession(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/assessments/sessions/${sessionId}`);
  }

  async submitAssessmentResponse(sessionId: string, response: any): Promise<ApiResponse<any>> {
    return this.request(`/assessments/sessions/${sessionId}/responses`, {
      method: 'POST',
      body: JSON.stringify(response),
    });
  }

  async pauseAssessmentSession(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/assessments/sessions/${sessionId}/pause`, {
      method: 'POST',
    });
  }

  async resumeAssessmentSession(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/assessments/sessions/${sessionId}/resume`, {
      method: 'POST',
    });
  }

  async getAssessmentResults(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/assessments/sessions/${sessionId}/results`);
  }

  async getAssessmentHistory(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/assessments/history/${userId}`);
  }

  async getAssessmentAnalytics(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/assessments/analytics/${sessionId}`);
  }



  async batchGenerateQuestions(requests: any[]): Promise<ApiResponse<any>> {
    return this.request('/questions/generate/batch', {
      method: 'POST',
      body: JSON.stringify({ requests }),
    });
  }

  async getGenerationJob(jobId: string): Promise<ApiResponse<any>> {
    return this.request(`/questions/jobs/${jobId}`);
  }

  async getGenerationHistory(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/questions/history/${userId}`);
  }

  async getQuestionTemplates(): Promise<ApiResponse<any>> {
    return this.request('/questions/templates');
  }

  async getAIProviderStatus(): Promise<ApiResponse<any>> {
    return this.request('/questions/providers/status');
  }

  async validateQuestion(questionId: string): Promise<ApiResponse<any>> {
    return this.request(`/questions/${questionId}/validate`, {
      method: 'POST',
    });
  }

  async batchValidateQuestions(questionIds: string[]): Promise<ApiResponse<any>> {
    return this.request('/questions/validate/batch', {
      method: 'POST',
      body: JSON.stringify({ question_ids: questionIds }),
    });
  }

  async cancelGenerationJob(jobId: string): Promise<ApiResponse<any>> {
    return this.request(`/questions/jobs/${jobId}/cancel`, {
      method: 'POST',
    });
  }

  async retryGenerationJob(jobId: string): Promise<ApiResponse<any>> {
    return this.request(`/questions/jobs/${jobId}/retry`, {
      method: 'POST',
    });
  }

  // Advanced Analytics API
  async getAnalyticsData(filters: any): Promise<ApiResponse<any>> {
    return this.request('/analytics/data', {
      method: 'POST',
      body: JSON.stringify({ filters }),
    });
  }

  async getAnalyticsMetrics(filters: any): Promise<ApiResponse<any>> {
    return this.request('/analytics/metrics', {
      method: 'POST',
      body: JSON.stringify({ filters }),
    });
  }

  async getAnalyticsCharts(filters: any, chartTypes: string[]): Promise<ApiResponse<any>> {
    return this.request('/analytics/charts', {
      method: 'POST',
      body: JSON.stringify({ filters, chart_types: chartTypes }),
    });
  }

  async getAnalyticsInsights(filters: any): Promise<ApiResponse<any>> {
    return this.request('/analytics/insights', {
      method: 'POST',
      body: JSON.stringify({ filters }),
    });
  }

  async createCustomReport(reportData: any): Promise<ApiResponse<any>> {
    return this.request('/analytics/reports', {
      method: 'POST',
      body: JSON.stringify(reportData),
    });
  }

  async getUserReports(userId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/reports/user/${userId}`);
  }

  async generateCustomReport(reportId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/reports/${reportId}/generate`, {
      method: 'POST',
    });
  }

  async deleteCustomReport(reportId: string): Promise<ApiResponse<any>> {
    return this.request(`/analytics/reports/${reportId}`, {
      method: 'DELETE',
    });
  }

  async exportAnalyticsData(params: any): Promise<ApiResponse<any>> {
    return this.request('/analytics/export', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  // Health Check
  async healthCheck(): Promise<ApiResponse<{ status: string }>> {
    return this.request('/health');
  }
}

// Export singleton instance
export const nexusAPI = new NexusAPIClient();
export default nexusAPI;

// Export types for use in components
export type {
  ApiResponse,
  AssessmentSession,
  AssessmentItem,
  AssessmentResponse,
  QuestionGenerationParams,
  GeneratedQuestion,
};
