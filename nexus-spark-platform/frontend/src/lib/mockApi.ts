// Mock API Service for Demo Mode
export class MockApiService {
  private static instance: MockApiService;
  private isEnabled: boolean = false;

  constructor() {
    this.isEnabled = import.meta.env.VITE_DEMO_MODE === 'true' || 
                     localStorage.getItem('demo_mode') === 'true';
    
    if (this.isEnabled) {
      console.log('🎭 Mock API Service initialized');
      this.setupMockData();
    }
  }

  static getInstance(): MockApiService {
    if (!MockApiService.instance) {
      MockApiService.instance = new MockApiService();
    }
    return MockApiService.instance;
  }

  private setupMockData() {
    // Enable demo mode globally
    localStorage.setItem('demo_mode', 'true');
  }

  async mockLogin(email: string, password: string): Promise<any> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    if (email === '<EMAIL>' && password === 'demo123') {
      return {
        success: true,
        data: {
          access_token: 'mock-jwt-token-' + Date.now(),
          refresh_token: 'mock-refresh-token-' + Date.now(),
          user: {
            user_id: 'demo-user-123',
            email: '<EMAIL>',
            first_name: 'Demo',
            last_name: 'Student',
            role: 'student',
            status: 'active',
            grade_level: 10,
            school_id: 'demo-school-1',
            school_name: 'Demo High School',
            preferences: {
              theme: 'light',
              language: 'en',
              notifications: true
            },
            notification_settings: {
              email: true,
              push: true,
              sms: false
            },
            last_login: new Date().toISOString(),
            login_count: 42,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: new Date().toISOString(),
            topics_completed: 15,
            assessments_taken: 23,
            average_score: 0.78
          }
        }
      };
    }

    return {
      success: false,
      error: 'Invalid credentials'
    };
  }

  async mockDashboardData(): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, 300));

    return {
      success: true,
      data: {
        user_stats: {
          active_users: 1247,
          total_assessments: 3456,
          avg_score: 78.5,
          questions_generated: 12890,
          study_time: 145.2,
          topics_mastered: 23,
          ai_questions: 8934,
          system_health: 98.7
        },
        performance_data: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          score: 65 + i * 3 + Math.random() * 5,
          ability: 0.2 + i * 0.1,
          confidence: 0.8 + i * 0.02,
        })),
        growth_data: [
          { week: 'Week 1', users: 120, assessments: 450, questions: 1200 },
          { week: 'Week 2', users: 145, assessments: 520, questions: 1450 },
          { week: 'Week 3', users: 178, assessments: 680, questions: 1890 },
          { week: 'Week 4', users: 210, assessments: 780, questions: 2340 }
        ],
        topics_data: [
          { topic: 'Mathematics', popularity: 85, difficulty: 3.2, mastery: 72 },
          { topic: 'Science', popularity: 78, difficulty: 3.8, mastery: 68 },
          { topic: 'English', popularity: 92, difficulty: 2.9, mastery: 81 },
          { topic: 'History', popularity: 65, difficulty: 3.1, mastery: 74 }
        ],
        recent_activities: [
          {
            id: '1',
            type: 'assessment_completed',
            user: 'Demo Student',
            description: 'Completed Math Assessment',
            score: 85,
            timestamp: new Date().toISOString()
          },
          {
            id: '2',
            type: 'question_generated',
            user: 'AI System',
            description: 'Generated 5 Science questions',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
          }
        ]
      }
    };
  }

  isDemo(): boolean {
    return this.isEnabled;
  }

  enableDemo(): void {
    this.isEnabled = true;
    localStorage.setItem('demo_mode', 'true');
    console.log('🎭 Demo mode enabled');
  }

  disableDemo(): void {
    this.isEnabled = false;
    localStorage.removeItem('demo_mode');
    console.log('🎭 Demo mode disabled');
  }
}

// Global instance
export const mockApi = MockApiService.getInstance();
