import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  BookOpen, 
  TrendingUp, 
  Clock, 
  Target, 
  Award,
  Brain,
  Zap,
  Activity,
  CheckCircle
} from 'lucide-react';

interface StatsCardsProps {
  dashboardData?: any;
  userPerformance?: any;
  realTimeMetrics?: any;
  isLoading?: boolean;
}

const StatsCards: React.FC<StatsCardsProps> = ({
  dashboardData,
  userPerformance,
  realTimeMetrics,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i} className="animate-pulse bg-white border border-gray-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatPercentage = (num: number) => `${Math.round(num)}%`;
  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
      {/* Real-time Active Users */}
      <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Active Users</CardTitle>
          <Activity className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {realTimeMetrics?.activeUsers || dashboardData?.activeUsersToday || 0}
          </div>
          <p className="text-xs text-gray-500">
            {dashboardData?.activeUsersWeek || 0} this week
          </p>
        </CardContent>
      </Card>

      {/* Total Assessments */}
      <Card className="bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">Total Assessments</CardTitle>
          <BookOpen className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatNumber(userPerformance?.totalAssessments || dashboardData?.totalAssessments || 0)}
          </div>
          <p className="text-xs text-gray-500">
            {realTimeMetrics?.activeAssessments || 0} active now
          </p>
        </CardContent>
      </Card>

      {/* Average Performance */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Score</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {userPerformance?.averageScore 
              ? formatPercentage(userPerformance.averageScore * 100)
              : dashboardData?.platformPerformance?.averageScore 
                ? formatPercentage(dashboardData.platformPerformance.averageScore)
                : '0%'
            }
          </div>
          <div className="mt-2">
            <Progress 
              value={userPerformance?.averageScore ? userPerformance.averageScore * 100 : 0} 
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>

      {/* Improvement Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Improvement</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-emerald-600">
            {userPerformance?.improvementRate 
              ? `+${formatPercentage(userPerformance.improvementRate)}`
              : '+0%'
            }
          </div>
          <p className="text-xs text-muted-foreground">
            vs last month
          </p>
        </CardContent>
      </Card>

      {/* Topics Mastery */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Topics Mastered</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {userPerformance?.topicsCompleted || 0}
            <span className="text-sm text-muted-foreground ml-1">
              / {userPerformance?.totalTopics || 0}
            </span>
          </div>
          <div className="mt-2">
            <Progress 
              value={userPerformance?.masteryPercentage || 0} 
              className="h-2"
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {formatPercentage(userPerformance?.masteryPercentage || 0)} complete
          </p>
        </CardContent>
      </Card>

      {/* Study Time */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Study Time</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {userPerformance?.totalStudyTime 
              ? formatTime(userPerformance.totalStudyTime)
              : '0m'
            }
          </div>
          <p className="text-xs text-muted-foreground">
            Avg: {userPerformance?.averageSessionDuration 
              ? formatTime(userPerformance.averageSessionDuration)
              : '0m'
            } per session
          </p>
        </CardContent>
      </Card>

      {/* AI Questions Generated */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">AI Questions</CardTitle>
          <Brain className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-purple-600">
            {formatNumber(realTimeMetrics?.questionsGeneratedToday || 0)}
          </div>
          <p className="text-xs text-muted-foreground">
            generated today
          </p>
        </CardContent>
      </Card>

      {/* System Health */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">System Health</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Badge 
              variant={
                realTimeMetrics?.systemHealth?.errorRate < 2 ? "default" : 
                realTimeMetrics?.systemHealth?.errorRate < 5 ? "secondary" : "destructive"
              }
              className="text-xs"
            >
              {realTimeMetrics?.systemHealth?.errorRate < 2 ? "Healthy" : 
               realTimeMetrics?.systemHealth?.errorRate < 5 ? "Warning" : "Critical"}
            </Badge>
          </div>
          <div className="mt-2 space-y-1">
            <div className="flex justify-between text-xs">
              <span>CPU</span>
              <span>{realTimeMetrics?.systemHealth?.cpuUsage || 0}%</span>
            </div>
            <Progress 
              value={realTimeMetrics?.systemHealth?.cpuUsage || 0} 
              className="h-1"
            />
            <div className="flex justify-between text-xs">
              <span>Memory</span>
              <span>{realTimeMetrics?.systemHealth?.memoryUsage || 0}%</span>
            </div>
            <Progress 
              value={realTimeMetrics?.systemHealth?.memoryUsage || 0} 
              className="h-1"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StatsCards;
