import React from 'react';

const LayoutTest: React.FC = () => {
  return (
    <div style={{
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '2rem',
      backgroundColor: '#f9fafb',
      minHeight: '100vh'
    }}>
      {/* Header */}
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#111827',
          marginBottom: '0.5rem'
        }}>
          Layout Test - Basic Styling
        </h1>
        <p style={{ color: '#6b7280' }}>
          Testing if basic styling works without Tailwind
        </p>
      </div>

      {/* 4-Column Grid */}
      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem'
        }}>
          4-Column Responsive Grid
        </h2>
        <div style={{
          display: 'grid',
          gap: '1rem',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))'
        }}>
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} style={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              padding: '1.5rem',
              boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
              transition: 'box-shadow 0.2s'
            }}>
              <h3 style={{
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Card {i + 1}
              </h3>
              <div style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#2563eb',
                marginBottom: '0.25rem'
              }}>
                {Math.floor(Math.random() * 1000)}
              </div>
              <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                Sample metric
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* 3-Column Grid */}
      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '1rem'
        }}>
          3-Column Responsive Grid
        </h2>
        <div style={{
          display: 'grid',
          gap: '1.5rem',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))'
        }}>
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} style={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              padding: '1.5rem',
              boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
            }}>
              <h3 style={{
                fontSize: '1.125rem',
                fontWeight: '600',
                color: '#111827',
                marginBottom: '1rem'
              }}>
                Feature {i + 1}
              </h3>
              <p style={{
                color: '#4b5563',
                marginBottom: '1rem',
                lineHeight: '1.5'
              }}>
                This is a sample description for feature {i + 1}. It demonstrates proper text styling and spacing.
              </p>
              <button style={{
                width: '100%',
                backgroundColor: '#2563eb',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '0.375rem',
                border: 'none',
                fontWeight: '500',
                cursor: 'pointer'
              }}>
                Action Button
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Status Message */}
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #e5e7eb',
        borderRadius: '0.5rem',
        padding: '1.5rem',
        textAlign: 'center'
      }}>
        <h2 style={{
          fontSize: '1.25rem',
          fontWeight: '600',
          color: '#374151',
          marginBottom: '0.5rem'
        }}>
          Layout Test Complete
        </h2>
        <p style={{ color: '#6b7280' }}>
          If you can see this styled content with proper grids and colors, the basic styling is working!
        </p>
      </div>
    </div>
  );
};

export default LayoutTest;
