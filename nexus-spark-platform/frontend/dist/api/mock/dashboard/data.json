{"success": true, "data": {"user_stats": {"active_users": 1247, "total_assessments": 3456, "avg_score": 78.5, "questions_generated": 12890, "study_time": 145.2, "topics_mastered": 23, "ai_questions": 8934, "system_health": 98.7}, "performance_data": [{"date": "2024-01-01", "score": 65, "ability": 0.2, "confidence": 0.8}, {"date": "2024-01-02", "score": 68, "ability": 0.3, "confidence": 0.82}, {"date": "2024-01-03", "score": 72, "ability": 0.4, "confidence": 0.85}, {"date": "2024-01-04", "score": 75, "ability": 0.5, "confidence": 0.87}, {"date": "2024-01-05", "score": 78, "ability": 0.6, "confidence": 0.9}, {"date": "2024-01-06", "score": 82, "ability": 0.7, "confidence": 0.92}, {"date": "2024-01-07", "score": 85, "ability": 0.8, "confidence": 0.95}], "growth_data": [{"week": "Week 1", "users": 120, "assessments": 450, "questions": 1200}, {"week": "Week 2", "users": 145, "assessments": 520, "questions": 1450}, {"week": "Week 3", "users": 178, "assessments": 680, "questions": 1890}, {"week": "Week 4", "users": 210, "assessments": 780, "questions": 2340}], "topics_data": [{"topic": "Mathematics", "popularity": 85, "difficulty": 3.2, "mastery": 72}, {"topic": "Science", "popularity": 78, "difficulty": 3.8, "mastery": 68}, {"topic": "English", "popularity": 92, "difficulty": 2.9, "mastery": 81}, {"topic": "History", "popularity": 65, "difficulty": 3.1, "mastery": 74}], "ability_data": [{"dimension": "Math", "ability": 0.7, "confidence": 0.9}, {"dimension": "Science", "ability": 0.6, "confidence": 0.85}, {"dimension": "Reading", "ability": 0.8, "confidence": 0.92}, {"dimension": "Writing", "ability": 0.65, "confidence": 0.88}], "recent_activities": [{"id": "1", "type": "assessment_completed", "user": "<PERSON>", "description": "Completed Math Assessment", "score": 85, "timestamp": "2024-01-15T10:30:00Z"}, {"id": "2", "type": "question_generated", "user": "AI System", "description": "Generated 5 Science questions", "timestamp": "2024-01-15T10:25:00Z"}, {"id": "3", "type": "user_login", "user": "<PERSON>", "description": "User logged in", "timestamp": "2024-01-15T10:20:00Z"}]}}