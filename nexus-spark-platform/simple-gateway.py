#!/usr/bin/env python3
"""
Simple API Gateway for Nexus-Spark Platform
This is a lightweight gateway for testing the Docker deployment
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import httpx
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Nexus-Spark Simple Gateway",
    description="Lightweight API Gateway for testing",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Service URLs
SERVICES = {
    "user-management": "http://localhost:8006",
    "assessment": "http://localhost:8000",
    "item-bank": "http://localhost:8001",
    "learner-profile": "http://localhost:8002",
    "retention": "http://localhost:8003",
    "question-generation": "http://localhost:8004",
    "analytics": "http://localhost:8005",
}

# Mock data for testing
MOCK_DATA = {
    "users": [
        {
            "user_id": "demo-user-123",
            "email": "<EMAIL>",
            "first_name": "Demo",
            "last_name": "Student",
            "role": "student",
            "created_at": "2024-01-01T00:00:00Z",
            "last_login": datetime.now().isoformat(),
        }
    ],
    "dashboard": {
        "totalUsers": 1247,
        "activeUsersToday": 89,
        "totalAssessments": 3456,
        "averageScore": 78.5,
        "recentActivity": [
            {
                "user_id": "demo-user-123",
                "activity": "Completed Math Assessment",
                "timestamp": datetime.now().isoformat(),
                "score": 85
            }
        ]
    }
}

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Nexus-Spark Simple Gateway",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "services": list(SERVICES.keys())
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": await check_services_health()
    }

async def check_services_health() -> Dict[str, str]:
    """Check health of all services"""
    health_status = {}

    async with httpx.AsyncClient(timeout=5.0, verify=False) as client:
        for service_name, service_url in SERVICES.items():
            try:
                response = await client.get(f"{service_url}/health")
                health_status[service_name] = "healthy" if response.status_code == 200 else "unhealthy"
            except Exception:
                health_status[service_name] = "unreachable"

    return health_status

@app.post("/api/v1/auth/login")
async def login(request: Request):
    """Mock login endpoint"""
    try:
        body = await request.json()
        email = body.get("email")
        password = body.get("password")
        
        # Mock authentication
        if email == "<EMAIL>" and password == "demo123":
            return JSONResponse({
                "success": True,
                "data": {
                    "access_token": f"mock-jwt-token-{datetime.now().timestamp()}",
                    "refresh_token": f"mock-refresh-token-{datetime.now().timestamp()}",
                    "token_type": "bearer",
                    "expires_in": 3600,
                    "user": MOCK_DATA["users"][0]
                }
            })
        else:
            raise HTTPException(status_code=401, detail="Invalid credentials")
            
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=400, detail="Invalid request")

@app.get("/api/v1/analytics/dashboard")
async def get_dashboard_data():
    """Mock dashboard data endpoint"""
    return JSONResponse({
        "success": True,
        "data": MOCK_DATA["dashboard"]
    })

@app.get("/api/v1/analytics/user/{user_id}")
async def get_user_analytics(user_id: str):
    """Mock user analytics endpoint"""
    return JSONResponse({
        "success": True,
        "data": {
            "user_id": user_id,
            "totalAssessments": 23,
            "averageScore": 0.785,
            "improvementRate": 0.123,
            "timeSpent": 145.2,
            "topicsCompleted": 15,
            "currentStreak": 7,
            "recentPerformance": [
                {
                    "date": (datetime.now() - timedelta(days=i)).isoformat().split('T')[0],
                    "score": 0.6 + (i * 0.05),
                    "topic": ["Math", "Science", "English", "History"][i % 4],
                    "assessment_id": f"assessment-{i + 1}",
                    "time_spent": 15 + (i * 2)
                }
                for i in range(10)
            ]
        }
    })

@app.api_route("/api/v1/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_to_service(path: str, request: Request):
    """Proxy requests to appropriate microservices"""
    
    # Determine which service to route to based on path
    service_name = None
    if path.startswith("auth/") or path.startswith("users/"):
        service_name = "user-management"
    elif path.startswith("assessments/"):
        service_name = "assessment"
    elif path.startswith("items/"):
        service_name = "item-bank"
    elif path.startswith("profiles/"):
        service_name = "learner-profile"
    elif path.startswith("retention/"):
        service_name = "retention"
    elif path.startswith("questions/"):
        service_name = "question-generation"
    elif path.startswith("analytics/"):
        service_name = "analytics"
    
    if not service_name or service_name not in SERVICES:
        # Return mock data for unknown endpoints
        return JSONResponse({
            "success": True,
            "data": {
                "message": f"Mock response for {path}",
                "timestamp": datetime.now().isoformat(),
                "path": path,
                "method": request.method
            }
        })
    
    # Try to proxy to the actual service
    service_url = SERVICES[service_name]
    target_url = f"{service_url}/{path}"
    
    try:
        async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
            # Forward the request
            response = await client.request(
                method=request.method,
                url=target_url,
                headers=dict(request.headers),
                content=await request.body(),
                params=dict(request.query_params)
            )
            
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=dict(response.headers)
            )
            
    except Exception as e:
        logger.warning(f"Service {service_name} unavailable, returning mock data: {e}")
        
        # Return mock data if service is unavailable
        return JSONResponse({
            "success": True,
            "data": {
                "message": f"Mock response for {path} (service unavailable)",
                "timestamp": datetime.now().isoformat(),
                "service": service_name,
                "error": str(e)
            }
        })

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8080))
    uvicorn.run(
        "simple-gateway:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
