#!/usr/bin/env python3
"""
Simple Assessment Service for Nexus-Spark Platform
This is a lightweight assessment service for testing the Docker deployment
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
import random

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Nexus-Spark Assessment Service",
    description="Assessment and IRT-based adaptive testing service",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock assessment data
MOCK_ASSESSMENTS = [
    {
        "assessment_id": "math-basic-001",
        "title": "Basic Mathematics Assessment",
        "description": "Fundamental math concepts for grade 10",
        "subject": "Mathematics",
        "grade_level": 10,
        "difficulty_range": [1, 5],
        "estimated_time": 30,
        "question_count": 20,
        "topics": ["Algebra", "Geometry", "Statistics"],
        "created_at": "2024-01-01T00:00:00Z",
        "status": "active"
    },
    {
        "assessment_id": "science-bio-001", 
        "title": "Biology Fundamentals",
        "description": "Basic biology concepts and principles",
        "subject": "Biology",
        "grade_level": 9,
        "difficulty_range": [1, 4],
        "estimated_time": 25,
        "question_count": 15,
        "topics": ["Cell Biology", "Genetics", "Ecology"],
        "created_at": "2024-01-01T00:00:00Z",
        "status": "active"
    }
]

MOCK_QUESTIONS = [
    {
        "question_id": "q001",
        "assessment_id": "math-basic-001",
        "question_text": "What is the value of x in the equation 2x + 5 = 13?",
        "question_type": "multiple_choice",
        "options": ["x = 3", "x = 4", "x = 5", "x = 6"],
        "correct_answer": "x = 4",
        "difficulty": 2.5,
        "topic": "Algebra",
        "explanation": "Solve for x: 2x + 5 = 13, so 2x = 8, therefore x = 4"
    },
    {
        "question_id": "q002",
        "assessment_id": "math-basic-001", 
        "question_text": "Calculate the area of a circle with radius 5 units.",
        "question_type": "multiple_choice",
        "options": ["25π", "10π", "5π", "50π"],
        "correct_answer": "25π",
        "difficulty": 3.0,
        "topic": "Geometry",
        "explanation": "Area = πr² = π(5)² = 25π"
    }
]

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Assessment Service",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "assessment",
        "timestamp": datetime.now().isoformat(),
        "database": "connected",
        "cache": "connected"
    }

@app.get("/assessments")
async def list_assessments(
    subject: Optional[str] = None,
    grade_level: Optional[int] = None,
    limit: int = 10
):
    """List available assessments"""
    assessments = MOCK_ASSESSMENTS.copy()
    
    if subject:
        assessments = [a for a in assessments if a["subject"].lower() == subject.lower()]
    
    if grade_level:
        assessments = [a for a in assessments if a["grade_level"] == grade_level]
    
    return JSONResponse({
        "success": True,
        "data": {
            "assessments": assessments[:limit],
            "total": len(assessments),
            "filters": {
                "subject": subject,
                "grade_level": grade_level,
                "limit": limit
            }
        }
    })

@app.get("/assessments/{assessment_id}")
async def get_assessment(assessment_id: str):
    """Get specific assessment details"""
    assessment = next((a for a in MOCK_ASSESSMENTS if a["assessment_id"] == assessment_id), None)
    
    if not assessment:
        raise HTTPException(status_code=404, detail="Assessment not found")
    
    return JSONResponse({
        "success": True,
        "data": assessment
    })

@app.post("/assessments/{assessment_id}/start")
async def start_assessment(assessment_id: str, request: Request):
    """Start an assessment session"""
    try:
        body = await request.json()
        user_id = body.get("user_id", "demo-user-123")
        
        assessment = next((a for a in MOCK_ASSESSMENTS if a["assessment_id"] == assessment_id), None)
        if not assessment:
            raise HTTPException(status_code=404, detail="Assessment not found")
        
        session_id = f"session-{datetime.now().timestamp()}"
        
        return JSONResponse({
            "success": True,
            "data": {
                "session_id": session_id,
                "assessment_id": assessment_id,
                "user_id": user_id,
                "started_at": datetime.now().isoformat(),
                "estimated_duration": assessment["estimated_time"],
                "total_questions": assessment["question_count"],
                "current_question": 1,
                "status": "in_progress"
            }
        })
        
    except Exception as e:
        logger.error(f"Error starting assessment: {e}")
        raise HTTPException(status_code=400, detail="Invalid request")

@app.get("/sessions/{session_id}/next-question")
async def get_next_question(session_id: str):
    """Get the next question using IRT-based adaptive selection"""
    
    # Simulate IRT-based question selection
    question = random.choice(MOCK_QUESTIONS)
    
    return JSONResponse({
        "success": True,
        "data": {
            "session_id": session_id,
            "question": {
                "question_id": question["question_id"],
                "question_text": question["question_text"],
                "question_type": question["question_type"],
                "options": question["options"],
                "topic": question["topic"],
                "estimated_difficulty": question["difficulty"]
            },
            "progress": {
                "current_question": random.randint(1, 20),
                "total_questions": 20,
                "time_remaining": random.randint(300, 1800)
            }
        }
    })

@app.post("/sessions/{session_id}/submit-answer")
async def submit_answer(session_id: str, request: Request):
    """Submit an answer and get feedback"""
    try:
        body = await request.json()
        question_id = body.get("question_id")
        answer = body.get("answer")
        
        question = next((q for q in MOCK_QUESTIONS if q["question_id"] == question_id), None)
        if not question:
            raise HTTPException(status_code=404, detail="Question not found")
        
        is_correct = answer == question["correct_answer"]
        
        return JSONResponse({
            "success": True,
            "data": {
                "session_id": session_id,
                "question_id": question_id,
                "is_correct": is_correct,
                "correct_answer": question["correct_answer"],
                "explanation": question["explanation"],
                "points_earned": 10 if is_correct else 0,
                "ability_estimate": random.uniform(-2, 2),
                "confidence_interval": [random.uniform(-3, -1), random.uniform(1, 3)]
            }
        })
        
    except Exception as e:
        logger.error(f"Error submitting answer: {e}")
        raise HTTPException(status_code=400, detail="Invalid request")

@app.post("/sessions/{session_id}/complete")
async def complete_assessment(session_id: str, request: Request):
    """Complete an assessment session"""
    try:
        body = await request.json()
        
        # Generate mock results
        total_questions = 20
        correct_answers = random.randint(12, 18)
        score_percentage = (correct_answers / total_questions) * 100
        
        return JSONResponse({
            "success": True,
            "data": {
                "session_id": session_id,
                "completed_at": datetime.now().isoformat(),
                "results": {
                    "total_questions": total_questions,
                    "correct_answers": correct_answers,
                    "score_percentage": score_percentage,
                    "grade": "A" if score_percentage >= 90 else "B" if score_percentage >= 80 else "C",
                    "time_taken": random.randint(900, 1800),
                    "ability_estimate": random.uniform(-1, 2),
                    "topics_mastered": random.randint(2, 5),
                    "recommendations": [
                        "Review algebraic equations",
                        "Practice geometry problems",
                        "Study statistical concepts"
                    ]
                },
                "performance_breakdown": {
                    "Algebra": {"correct": 8, "total": 10, "percentage": 80},
                    "Geometry": {"correct": 6, "total": 8, "percentage": 75},
                    "Statistics": {"correct": 2, "total": 2, "percentage": 100}
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error completing assessment: {e}")
        raise HTTPException(status_code=400, detail="Invalid request")

@app.get("/analytics/performance")
async def get_performance_analytics(
    user_id: Optional[str] = None,
    assessment_id: Optional[str] = None,
    days: int = 30
):
    """Get performance analytics"""
    
    return JSONResponse({
        "success": True,
        "data": {
            "user_id": user_id,
            "assessment_id": assessment_id,
            "period_days": days,
            "summary": {
                "total_assessments": random.randint(5, 25),
                "average_score": random.uniform(70, 95),
                "improvement_rate": random.uniform(5, 25),
                "time_spent_minutes": random.randint(300, 1200)
            },
            "trend_data": [
                {
                    "date": (datetime.now() - timedelta(days=i)).isoformat().split('T')[0],
                    "score": random.uniform(60, 100),
                    "ability": random.uniform(-2, 2),
                    "confidence": random.uniform(0.7, 0.95)
                }
                for i in range(min(days, 30))
            ]
        }
    })

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
